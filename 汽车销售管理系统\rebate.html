<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>商务政策返利核算 - 汽车销售管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dexie@3.2.2/dist/dexie.min.js"></script>
    <script src="xlsx.full.min.js"></script>
    <link rel="stylesheet" href="styles.css" />
    <style>
        .rebate-container {
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .rebate-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .rebate-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .tab-button {
            padding: 10px 20px;
            border: none;
            background: #e9ecef;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            background: #4361ee;
            color: white;
        }
        
        .rebate-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .toolbar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .category-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
        
        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .category-header h4 {
            margin: 0;
            color: #495057;
        }
        
        .category-actions {
            display: flex;
            gap: 5px;
        }
        
        .sub-categories {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .sub-category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        
        .calculation-filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        .table {
            width: 100%;
            margin-bottom: 1rem;
            color: #212529;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 12px;
            vertical-align: top;
            border-top: 1px solid #dee2e6;
            text-align: left;
        }
        
        .table thead th {
            vertical-align: bottom;
            border-bottom: 2px solid #dee2e6;
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25em 0.4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }
        
        .badge-success {
            color: #fff;
            background-color: #28a745;
        }
        
        .badge-warning {
            color: #212529;
            background-color: #ffc107;
        }
        
        .badge-secondary {
            color: #fff;
            background-color: #6c757d;
        }
        
        .btn {
            display: inline-block;
            font-weight: 400;
            color: #212529;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            background-color: transparent;
            border: 1px solid transparent;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 0.25rem;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            text-decoration: none;
        }
        
        .btn-primary {
            color: #fff;
            background-color: #4361ee;
            border-color: #4361ee;
        }
        
        .btn-primary:hover {
            background-color: #3f37c9;
            border-color: #3f37c9;
        }
        
        .btn-secondary {
            color: #fff;
            background-color: #6c757d;
            border-color: #6c757d;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #545b62;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }
        
        .form-control {
            display: block;
            width: 100%;
            height: calc(1.5em + 0.75rem + 2px);
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .row {
            display: flex;
            flex-wrap: wrap;
            margin-right: -15px;
            margin-left: -15px;
        }
        
        .col-md-3 {
            flex: 0 0 25%;
            max-width: 25%;
            padding-right: 15px;
            padding-left: 15px;
        }
        
        @media (max-width: 768px) {
            .col-md-3 {
                flex: 0 0 100%;
                max-width: 100%;
                margin-bottom: 15px;
            }
        }
        
        .search-container {
            margin-bottom: 20px;
        }
        
        .search-input {
            width: 300px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stats-card h3 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: bold;
        }
        
        .stats-card p {
            margin: 0;
            opacity: 0.9;
        }
    </style>
</head>
<body style="height: 100vh; margin: 0;">
    <div class="container" style="flex: 1; display: flex; flex-direction: column; height: 100%;">
        <header style="background: linear-gradient(135deg, #4361ee, #3f37c9); color: white; padding: 15px 30px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
            <div class="logo" style="display: flex; align-items: center; gap: 15px;">
                <i class="fas fa-calculator" style="font-size: 28px;"></i>
                <h1 style="font-size: 22px; font-weight: 600;">商务政策返利核算系统</h1>
            </div>
            <div class="nav-links" style="display: flex; gap: 20px;">
                <a href="index.html" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 5px; transition: background 0.3s;">
                    <i class="fas fa-home"></i> 返回主页
                </a>
                <a href="order.html" style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 5px; transition: background 0.3s;">
                    <i class="fas fa-shopping-cart"></i> 订单管理
                </a>
            </div>
        </header>
        
        <div class="rebate-container">
            <div class="rebate-header">
                <h2 style="margin: 0 0 15px 0; color: #495057;">
                    <i class="fas fa-chart-line"></i> 商务政策返利核算
                </h2>
                <p style="margin: 0; color: #6c757d;">
                    管理商务政策配置，自动计算返利金额，生成统计报表
                </p>
            </div>
            
            <div class="stats-cards">
                <div class="stats-card">
                    <h3 id="total-policies">0</h3>
                    <p>活跃政策数</p>
                </div>
                <div class="stats-card">
                    <h3 id="total-calculations">0</h3>
                    <p>本月计算次数</p>
                </div>
                <div class="stats-card">
                    <h3 id="total-rebate">¥0</h3>
                    <p>本月返利总额</p>
                </div>
                <div class="stats-card">
                    <h3 id="pending-confirmations">0</h3>
                    <p>待确认项目</p>
                </div>
            </div>
            
            <div class="rebate-tabs">
                <button class="tab-button active" data-tab="policy">
                    <i class="fas fa-cogs"></i> 政策配置
                </button>
                <button class="tab-button" data-tab="calculation">
                    <i class="fas fa-calculator"></i> 返利计算
                </button>
                <button class="tab-button" data-tab="report">
                    <i class="fas fa-chart-bar"></i> 统计报表
                </button>
            </div>
            
            <div class="search-container">
                <input type="text" id="rebate-search" class="search-input" placeholder="搜索政策、项目或计算结果...">
            </div>
            
            <div class="rebate-content" id="rebate-content">
                <!-- 内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="database.js"></script>
    <script src="rebateModule.js"></script>
    
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 初始化数据库
                await window.dbFunctions.initDB();
                
                // 初始化返利模块
                await window.rebateFunctions.init();
                
                // 更新统计数据
                updateStats();
                
                console.log('返利核算系统初始化完成');
            } catch (error) {
                console.error('系统初始化失败:', error);
                alert('系统初始化失败，请刷新页面重试');
            }
        });
        
        // 更新统计数据
        async function updateStats() {
            try {
                const policies = await window.dbFunctions.getAllRebatePolicyItems();
                const calculations = await window.dbFunctions.getAllRebateCalculations();
                
                // 活跃政策数
                const activePolicies = policies.filter(p => p.status === 'active').length;
                document.getElementById('total-policies').textContent = activePolicies;
                
                // 本月计算次数
                const currentMonth = new Date().toISOString().slice(0, 7);
                const monthlyCalculations = calculations.filter(c => c.period === currentMonth).length;
                document.getElementById('total-calculations').textContent = monthlyCalculations;
                
                // 本月返利总额
                const monthlyRebate = calculations
                    .filter(c => c.period === currentMonth)
                    .reduce((sum, c) => sum + c.rebateAmount, 0);
                document.getElementById('total-rebate').textContent = `¥${monthlyRebate.toLocaleString()}`;
                
                // 待确认项目
                const pendingConfirmations = calculations.filter(c => c.status === 'pending').length;
                document.getElementById('pending-confirmations').textContent = pendingConfirmations;
                
            } catch (error) {
                console.error('更新统计数据失败:', error);
            }
        }
    </script>
</body>
</html>
