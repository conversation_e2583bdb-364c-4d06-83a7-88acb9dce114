<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析 - 林肯汽车销售管理系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 数据分析专用样式 */
        .analytics-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .analytics-header {
            margin-bottom: 30px;
        }

        .analytics-header h2 {
            color: #333;
            margin-bottom: 20px;
        }

        .analysis-tabs {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }

        .analysis-tab {
            padding: 12px 20px;
            border: none;
            background: #f8f9fa;
            color: #6c757d;
            border-radius: 6px 6px 0 0;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .analysis-tab:hover {
            background: #e9ecef;
            color: #495057;
        }

        .analysis-tab.active {
            background: #007bff;
            color: white;
        }

        .analytics-content {
            margin-top: 20px;
        }

        .analysis-filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .analysis-filters h3 {
            margin: 0 0 15px 0;
            color: #333;
        }

        .filter-controls {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-controls label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .filter-controls input,
        .filter-controls select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .metric-header h4 {
            margin: 0;
            color: #333;
        }

        .metric-header i {
            color: #007bff;
            font-size: 20px;
        }

        .metric-content {
            margin-bottom: 15px;
        }

        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #6c757d;
            font-size: 14px;
        }

        .metric-comparison {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 3px;
            margin-top: 5px;
            display: inline-block;
        }

        .metric-comparison.up {
            background: #d4edda;
            color: #155724;
        }

        .metric-comparison.down {
            background: #f8d7da;
            color: #721c24;
        }

        .metric-comparison.stable {
            background: #e2e3e5;
            color: #383d41;
        }

        .analysis-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
        }

        .section-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .section-card h4 {
            margin: 0 0 20px 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metrics-row {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }

        .metric-item {
            text-align: center;
        }

        .metric-item .metric-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .metric-item .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .metric-item .metric-detail {
            font-size: 11px;
            color: #6c757d;
        }

        .vehicle-breakdown {
            margin-top: 20px;
        }

        .vehicle-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .vehicle-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .vehicle-rank {
            width: 30px;
            height: 30px;
            background: #007bff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .vehicle-name {
            flex: 1;
            font-weight: 500;
        }

        .vehicle-count {
            font-weight: bold;
            color: #007bff;
        }

        .ranking-table {
            margin-bottom: 20px;
        }

        .ranking-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .ranking-table th,
        .ranking-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .ranking-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .rank-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 12px;
        }

        .rank-badge.rank-1 {
            background: #ffd700;
            color: #333;
        }

        .rank-badge.rank-2 {
            background: #c0c0c0;
            color: #333;
        }

        .rank-badge.rank-3 {
            background: #cd7f32;
            color: white;
        }

        .rank-badge.rank-other {
            background: #6c757d;
            color: white;
        }

        .chart-container {
            margin-top: 20px;
            text-align: center;
        }

        .chart-container canvas {
            max-width: 100%;
            height: auto;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
            font-size: 16px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .analytics-container {
                padding: 10px;
            }

            .analysis-tabs {
                flex-direction: column;
            }

            .analysis-tab {
                justify-content: center;
            }

            .filter-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .analysis-sections {
                grid-template-columns: 1fr;
            }

            .metrics-row {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
                <img src="lincoln-logo.png" alt="Lincoln" onerror="this.style.display='none'">
                <h2>林肯汽车</h2>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html"><i class="fas fa-home"></i> 首页</a></li>
                <li><a href="customer.html"><i class="fas fa-users"></i> 客户管理</a></li>
                <li><a href="order.html"><i class="fas fa-file-contract"></i> 订单管理</a></li>
                <li><a href="parts.html"><i class="fas fa-cogs"></i> 配件管理</a></li>
                <li><a href="analytics.html" class="active"><i class="fas fa-chart-bar"></i> 数据分析</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> 系统设置</a></li>
            </ul>
        </nav>

        <main class="main-content">
            <div id="analytics-app">
                <!-- 数据分析内容将在这里动态加载 -->
            </div>
        </main>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="dexie.min.js"></script>
    <script src="database.js"></script>
    <script src="analyticsModule.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // 初始化数据分析应用
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 确保数据库已初始化
                await initDB();
                
                // 初始化分析模块
                await window.analyticsModule.init();
                
                // 渲染分析界面
                const appContainer = document.getElementById('analytics-app');
                appContainer.innerHTML = window.analyticsModule.render();
                
                console.log('数据分析模块初始化完成');
            } catch (error) {
                console.error('数据分析模块初始化失败:', error);
                document.getElementById('analytics-app').innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #dc3545; margin-bottom: 20px;"></i>
                        <h3>数据分析模块加载失败</h3>
                        <p>请刷新页面重试，或联系技术支持。</p>
                        <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
