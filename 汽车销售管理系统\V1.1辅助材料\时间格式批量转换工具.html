<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间格式批量转换工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #2a9d8f);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(to right, #2a9d8f, #1a5f7a);
            color: white;
            padding: 25px 30px;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
        }
        
        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .content {
            padding: 30px;
        }
        
        .input-section, .output-section {
            margin-bottom: 30px;
        }
        
        h2 {
            color: #1a5f7a;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #2a9d8f;
        }
        
        textarea {
            width: 100%;
            height: 150px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            resize: vertical;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        textarea:focus {
            border-color: #2a9d8f;
            outline: none;
            box-shadow: 0 0 0 2px rgba(42, 157, 143, 0.2);
        }
        
        .note {
            background: #e8f4f3;
            padding: 12px 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-size: 0.9rem;
            color: #1a5f7a;
            border-left: 4px solid #2a9d8f;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(to right, #2a9d8f, #1a5f7a);
            color: white;
            flex: 1;
        }
        
        .btn-secondary {
            background: #f1f1f1;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary:hover {
            background: linear-gradient(to right, #239b8d, #164e65);
        }
        
        .btn-secondary:hover {
            background: #e5e5e5;
        }
        
        .output-area {
            position: relative;
        }
        
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 8px 15px;
            background: rgba(42, 157, 143, 0.1);
            color: #1a5f7a;
            border: 1px solid #2a9d8f;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .copy-btn:hover {
            background: rgba(42, 157, 143, 0.2);
        }
        
        footer {
            text-align: center;
            padding: 20px;
            color: #777;
            font-size: 0.9rem;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }
        
        .example {
            background: #f0f7ff;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-size: 0.9rem;
        }
        
        .example h3 {
            color: #1a5f7a;
            margin-bottom: 10px;
        }
        
        .example ul {
            padding-left: 20px;
        }
        
        .example li {
            margin-bottom: 5px;
        }
        
        .success-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2a9d8f;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transform: translateX(200%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }
        
        .success-message.show {
            transform: translateX(0);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>时间格式批量转换工具</h1>
            <p class="subtitle">将"时:分:秒"格式批量转换为"小时分钟"格式，支持四舍五入计算</p>
        </header>
        
        <div class="content">
            <section class="input-section">
                <h2>输入时间（每行一个时间）</h2>
                <textarea id="inputText" placeholder="请输入时间，格式为 HH:MM:SS&#10;例如：&#10;01:30:15&#10;02:45:30&#10;00:59:59"></textarea>
                
                <div class="note">
                    <strong>注意：</strong> 
                    秒数将进行四舍五入处理（≥30秒进1分钟，&lt;30秒忽略）
                </div>
                
                <div class="buttons">
                    <button class="btn btn-primary" onclick="convertTimes()">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                            <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                        </svg>
                        转换时间
                    </button>
                    <button class="btn btn-secondary" onclick="clearAll()">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                            <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                        </svg>
                        清空全部
                    </button>
                    <button class="btn btn-secondary" onclick="loadExample()">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                            <path d="M5.255 5.786a.237.237 0 0 0 .241.247h.825c.138 0 .248-.113.266-.25.09-.656.54-1.134 1.342-1.134.686 0 1.314.343 1.314 1.168 0 .635-.374.927-.965 1.371-.673.489-1.206 1.06-1.168 1.987l.003.217a.25.25 0 0 0 .25.246h.811a.25.25 0 0 0 .25-.25v-.105c0-.718.273-.927 1.01-1.486.609-.463 1.244-.977 1.244-2.056 0-1.511-1.276-2.241-2.673-2.241-1.267 0-2.655.59-2.75 2.286zm1.557 5.763c0 .533.425.927 1.01.927.609 0 1.028-.394 1.028-.927 0-.552-.42-.94-1.029-.94-.584 0-1.009.388-1.009.94z"/>
                        </svg>
                        加载示例
                    </button>
                </div>
            </section>
            
            <section class="output-section">
                <h2>转换结果</h2>
                <div class="output-area">
                    <textarea id="outputText" readonly placeholder="转换结果将显示在这里"></textarea>
                    <button class="copy-btn" onclick="copyToClipboard()">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/>
                            <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/>
                        </svg>
                        复制结果
                    </button>
                </div>
            </section>
            
            <div class="example">
                <h3>转换规则说明：</h3>
                <ul>
                    <li><strong>01:30:15</strong> → 1小时30分钟（15秒舍去）</li>
                    <li><strong>01:30:30</strong> → 1小时31分钟（30秒进1分钟）</li>
                    <li><strong>02:45:59</strong> → 2小时46分钟（59秒进1分钟）</li>
                    <li><strong>00:59:29</strong> → 0小时59分钟（29秒舍去）</li>
                    <li><strong>100:15:30</strong> → 100小时16分钟</li>
                </ul>
            </div>
        </div>
        
        <footer>
            <p>© 2023 时间格式转换工具 | 将"时:分:秒"格式转换为"小时分钟"格式</p>
        </footer>
    </div>
    
    <div class="success-message" id="successMessage">
        结果已复制到剪贴板！
    </div>
    
    <script>
        function convertTimes() {
            const inputText = document.getElementById('inputText').value;
            const outputText = document.getElementById('outputText');
            
            if (!inputText.trim()) {
                outputText.value = '请输入要转换的时间';
                return;
            }
            
            const times = inputText.split('\n');
            let results = [];
            
            for (let time of times) {
                time = time.trim();
                if (!time) {
                    results.push('');
                    continue;
                }
                
                // 验证时间格式 HH:MM:SS
                const timeRegex = /^(\d{1,3}):([0-5]\d):([0-5]\d)$/;
                const match = time.match(timeRegex);
                
                if (!match) {
                    results.push(`错误格式: ${time}`);
                    continue;
                }
                
                const hours = parseInt(match[1]);
                const minutes = parseInt(match[2]);
                const seconds = parseInt(match[3]);
                
                // 计算总秒数
                const totalSeconds = hours * 3600 + minutes * 60 + seconds;
                
                // 四舍五入计算总分钟数
                const totalMinutes = Math.round(totalSeconds / 60);
                
                // 计算小时和分钟
                const resultHours = Math.floor(totalMinutes / 60);
                const resultMinutes = totalMinutes % 60;
                
                // 构建结果字符串
                results.push(`${resultHours}小时${resultMinutes}分钟`);
            }
            
            outputText.value = results.join('\n');
        }
        
        function clearAll() {
            document.getElementById('inputText').value = '';
            document.getElementById('outputText').value = '';
        }
        
        function loadExample() {
            const example = 
                `08:30:15
12:45:30
00:59:59
01:00:00
100:15:30
02:30:29
03:45:31`;
            
            document.getElementById('inputText').value = example;
        }
        
        function copyToClipboard() {
            const outputText = document.getElementById('outputText');
            if (outputText.value) {
                outputText.select();
                document.execCommand('copy');
                
                // 显示成功消息
                const message = document.getElementById('successMessage');
                message.classList.add('show');
                
                setTimeout(() => {
                    message.classList.remove('show');
                }, 2000);
            }
        }
    </script>
</body>
</html>