// 商务政策返利核算模块
window.rebateFunctions = {
    // 数据存储
    allCategories: [],
    allPolicyItems: [],
    allCalculations: [],
    allReports: [],
    currentTab: 'policy',
    
    // 列设置
    policyColumnSettings: {
        categoryName: true,
        itemName: true,
        itemType: true,
        calculationType: true,
        baseRate: true,
        startDate: true,
        endDate: true,
        status: true
    },
    
    calculationColumnSettings: {
        orderNumber: true,
        salesAdvisor: true,
        carModel: true,
        baseAmount: true,
        rebateAmount: true,
        calculationDate: true,
        period: true,
        status: true
    },

    // 初始化模块
    init: async function() {
        try {
            await this.loadAllData();
            this.setupEventListeners();
            this.renderCurrentTab();
        } catch (error) {
            console.error('返利核算模块初始化失败:', error);
        }
    },

    // 加载所有数据
    loadAllData: async function() {
        try {
            this.allCategories = await window.dbFunctions.getAllRebatePolicyCategories();
            this.allPolicyItems = await window.dbFunctions.getAllRebatePolicyItems();
            this.allCalculations = await window.dbFunctions.getAllRebateCalculations();
            this.allReports = await window.dbFunctions.getAllRebateReports();
        } catch (error) {
            console.error('加载返利数据失败:', error);
        }
    },

    // 设置事件监听器
    setupEventListeners: function() {
        // 标签页切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-button')) {
                this.switchTab(e.target.dataset.tab);
            }
        });

        // 搜索功能
        const searchInput = document.getElementById('rebate-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterData(e.target.value);
            });
        }
    },

    // 切换标签页
    switchTab: function(tabName) {
        this.currentTab = tabName;
        
        // 更新标签页样式
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // 渲染对应内容
        this.renderCurrentTab();
    },

    // 渲染当前标签页
    renderCurrentTab: function() {
        const contentDiv = document.getElementById('rebate-content');
        if (!contentDiv) return;

        switch (this.currentTab) {
            case 'policy':
                this.renderPolicyManagement();
                break;
            case 'calculation':
                this.renderCalculationManagement();
                break;
            case 'report':
                this.renderReportManagement();
                break;
            default:
                this.renderPolicyManagement();
        }
    },

    // 渲染政策管理页面
    renderPolicyManagement: function() {
        const contentDiv = document.getElementById('rebate-content');
        contentDiv.innerHTML = `
            <div class="policy-management">
                <div class="toolbar">
                    <button class="btn btn-primary" onclick="rebateFunctions.showPolicyForm()">
                        <i class="fas fa-plus"></i> 新增政策
                    </button>
                    <button class="btn btn-secondary" onclick="rebateFunctions.importPolicyExcel()">
                        <i class="fas fa-upload"></i> 导入Excel
                    </button>
                    <button class="btn btn-secondary" onclick="rebateFunctions.exportPolicyExcel()">
                        <i class="fas fa-download"></i> 导出Excel
                    </button>
                </div>
                
                <div class="policy-categories">
                    <h3>政策分类配置</h3>
                    <div id="categories-container"></div>
                </div>
                
                <div class="policy-items">
                    <h3>政策项目配置</h3>
                    <div id="policy-items-container"></div>
                </div>
            </div>
        `;
        
        this.renderCategories();
        this.renderPolicyItems();
    },

    // 渲染分类
    renderCategories: function() {
        const container = document.getElementById('categories-container');
        if (!container) return;

        const mainCategories = this.allCategories.filter(cat => cat.categoryType === 'main');
        
        let html = '<div class="categories-grid">';
        mainCategories.forEach(category => {
            const subCategories = this.allCategories.filter(cat => cat.parentId === category.id);
            html += `
                <div class="category-card">
                    <div class="category-header">
                        <h4>${category.categoryName}</h4>
                        <div class="category-actions">
                            <button class="btn btn-sm" onclick="rebateFunctions.editCategory(${category.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm" onclick="rebateFunctions.addSubCategory(${category.id})">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="sub-categories">
                        ${subCategories.map(sub => `
                            <div class="sub-category-item">
                                <span>${sub.categoryName}</span>
                                <button class="btn btn-sm" onclick="rebateFunctions.editCategory(${sub.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        container.innerHTML = html;
    },

    // 渲染政策项目
    renderPolicyItems: function() {
        const container = document.getElementById('policy-items-container');
        if (!container) return;

        let html = `
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>分类</th>
                            <th>项目名称</th>
                            <th>项目类型</th>
                            <th>计算类型</th>
                            <th>基础比例</th>
                            <th>生效期</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        this.allPolicyItems.forEach(item => {
            const category = this.allCategories.find(cat => cat.id === item.categoryId);
            const categoryName = category ? category.categoryName : '未知分类';
            
            html += `
                <tr>
                    <td>${categoryName}</td>
                    <td>${item.itemName}</td>
                    <td>${this.getItemTypeText(item.itemType)}</td>
                    <td>${this.getCalculationTypeText(item.calculationType)}</td>
                    <td>${item.baseRate}%</td>
                    <td>${item.startDate} ~ ${item.endDate}</td>
                    <td>
                        <span class="badge ${item.status === 'active' ? 'badge-success' : 'badge-secondary'}">
                            ${item.status === 'active' ? '启用' : '禁用'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm" onclick="rebateFunctions.editPolicyItem(${item.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm" onclick="rebateFunctions.deletePolicyItem(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
        
        container.innerHTML = html;
    },

    // 获取项目类型文本
    getItemTypeText: function(type) {
        const types = {
            'fixed': '固定类型',
            'achievement': '达成率类型',
            'condition': '条件类型'
        };
        return types[type] || type;
    },

    // 获取计算类型文本
    getCalculationTypeText: function(type) {
        const types = {
            'percentage': '百分比',
            'fixed_amount': '固定金额',
            'tiered': '阶梯式'
        };
        return types[type] || type;
    },

    // 显示政策表单
    showPolicyForm: function(editId = null) {
        const isEdit = editId !== null;
        const title = isEdit ? '编辑政策项目' : '新增政策项目';

        let item = {
            categoryId: '',
            itemName: '',
            itemType: 'fixed',
            calculationType: 'percentage',
            baseRate: 0,
            startDate: new Date().toISOString().split('T')[0],
            endDate: '',
            conditions: '',
            status: 'active'
        };

        if (isEdit) {
            item = this.allPolicyItems.find(p => p.id === editId) || item;
        }

        const modalHtml = `
            <div class="modal-overlay" onclick="rebateFunctions.closePolicyForm()">
                <div class="modal-content" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="close-btn" onclick="rebateFunctions.closePolicyForm()">×</button>
                    </div>
                    <div class="modal-body">
                        <form id="policy-form">
                            <div class="form-group">
                                <label>所属分类</label>
                                <select name="categoryId" class="form-control" required>
                                    <option value="">请选择分类</option>
                                    ${this.allCategories.map(cat =>
                                        `<option value="${cat.id}" ${cat.id === item.categoryId ? 'selected' : ''}>${cat.categoryName}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="form-group">
                                <label>项目名称</label>
                                <input type="text" name="itemName" class="form-control" value="${item.itemName}" required>
                            </div>
                            <div class="form-group">
                                <label>项目类型</label>
                                <select name="itemType" class="form-control" required>
                                    <option value="fixed" ${item.itemType === 'fixed' ? 'selected' : ''}>固定类型</option>
                                    <option value="achievement" ${item.itemType === 'achievement' ? 'selected' : ''}>达成率类型</option>
                                    <option value="condition" ${item.itemType === 'condition' ? 'selected' : ''}>条件类型</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>计算类型</label>
                                <select name="calculationType" class="form-control" required>
                                    <option value="percentage" ${item.calculationType === 'percentage' ? 'selected' : ''}>百分比</option>
                                    <option value="fixed_amount" ${item.calculationType === 'fixed_amount' ? 'selected' : ''}>固定金额</option>
                                    <option value="tiered" ${item.calculationType === 'tiered' ? 'selected' : ''}>阶梯式</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>基础比例/金额</label>
                                <input type="number" name="baseRate" class="form-control" value="${item.baseRate}" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label>生效开始日期</label>
                                <input type="date" name="startDate" class="form-control" value="${item.startDate}" required>
                            </div>
                            <div class="form-group">
                                <label>生效结束日期</label>
                                <input type="date" name="endDate" class="form-control" value="${item.endDate}">
                            </div>
                            <div class="form-group">
                                <label>触发条件</label>
                                <textarea name="conditions" class="form-control" rows="3" placeholder="请输入触发条件（JSON格式）">${item.conditions}</textarea>
                            </div>
                            <div class="form-group">
                                <label>状态</label>
                                <select name="status" class="form-control" required>
                                    <option value="active" ${item.status === 'active' ? 'selected' : ''}>启用</option>
                                    <option value="inactive" ${item.status === 'inactive' ? 'selected' : ''}>禁用</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="rebateFunctions.closePolicyForm()">取消</button>
                        <button class="btn btn-primary" onclick="rebateFunctions.savePolicyForm(${editId})">保存</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    // 关闭政策表单
    closePolicyForm: function() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.remove();
        }
    },

    // 保存政策表单
    savePolicyForm: async function(editId) {
        const form = document.getElementById('policy-form');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        try {
            if (editId) {
                await window.dbFunctions.updateRebatePolicyItem(editId, data);
            } else {
                await window.dbFunctions.addRebatePolicyItem(data);
            }

            await this.loadAllData();
            this.renderPolicyItems();
            this.closePolicyForm();

            alert('保存成功！');
        } catch (error) {
            console.error('保存失败:', error);
            alert('保存失败，请重试');
        }
    },

    // 导入Excel
    importPolicyExcel: function() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xlsx,.xls';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                this.processExcelFile(file);
            }
        };
        input.click();
    },

    // 处理Excel文件
    processExcelFile: function(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet);

                this.importPolicyData(jsonData);
            } catch (error) {
                console.error('Excel文件解析失败:', error);
                alert('Excel文件格式错误，请检查文件格式');
            }
        };
        reader.readAsArrayBuffer(file);
    },

    // 导入政策数据
    importPolicyData: async function(data) {
        try {
            for (const row of data) {
                const policyItem = {
                    categoryId: this.findCategoryIdByName(row['分类名称']),
                    itemName: row['项目名称'],
                    itemType: this.mapItemType(row['项目类型']),
                    calculationType: this.mapCalculationType(row['计算类型']),
                    baseRate: parseFloat(row['基础比例']) || 0,
                    startDate: this.formatDate(row['开始日期']),
                    endDate: this.formatDate(row['结束日期']),
                    conditions: row['触发条件'] || '',
                    status: row['状态'] === '启用' ? 'active' : 'inactive'
                };

                if (policyItem.categoryId && policyItem.itemName) {
                    await window.dbFunctions.addRebatePolicyItem(policyItem);
                }
            }

            await this.loadAllData();
            this.renderPolicyItems();
            alert(`成功导入 ${data.length} 条政策数据`);
        } catch (error) {
            console.error('导入数据失败:', error);
            alert('导入数据失败，请检查数据格式');
        }
    },

    // 导出Excel
    exportPolicyExcel: function() {
        try {
            const exportData = this.allPolicyItems.map(item => {
                const category = this.allCategories.find(cat => cat.id === item.categoryId);
                return {
                    '分类名称': category ? category.categoryName : '',
                    '项目名称': item.itemName,
                    '项目类型': this.getItemTypeText(item.itemType),
                    '计算类型': this.getCalculationTypeText(item.calculationType),
                    '基础比例': item.baseRate,
                    '开始日期': item.startDate,
                    '结束日期': item.endDate,
                    '触发条件': item.conditions,
                    '状态': item.status === 'active' ? '启用' : '禁用',
                    '创建日期': item.createDate.split('T')[0],
                    '更新日期': item.updateDate.split('T')[0]
                };
            });

            const worksheet = XLSX.utils.json_to_sheet(exportData);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, '政策配置');

            const fileName = `商务政策配置_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(workbook, fileName);
        } catch (error) {
            console.error('导出Excel失败:', error);
            alert('导出失败，请重试');
        }
    },

    // 编辑分类
    editCategory: function(categoryId) {
        console.log('编辑分类', categoryId);
    },

    // 添加子分类
    addSubCategory: function(parentId) {
        console.log('添加子分类', parentId);
    },

    // 编辑政策项目
    editPolicyItem: function(itemId) {
        console.log('编辑政策项目', itemId);
    },

    // 删除政策项目
    deletePolicyItem: function(itemId) {
        if (confirm('确定要删除这个政策项目吗？')) {
            // 实现删除逻辑
            console.log('删除政策项目', itemId);
        }
    },

    // 渲染计算管理页面
    renderCalculationManagement: function() {
        const contentDiv = document.getElementById('rebate-content');
        contentDiv.innerHTML = `
            <div class="calculation-management">
                <div class="toolbar">
                    <button class="btn btn-primary" onclick="rebateFunctions.calculateRebates()">
                        <i class="fas fa-calculator"></i> 计算返利
                    </button>
                    <button class="btn btn-secondary" onclick="rebateFunctions.exportCalculations()">
                        <i class="fas fa-download"></i> 导出计算结果
                    </button>
                </div>
                
                <div class="calculation-filters">
                    <div class="row">
                        <div class="col-md-3">
                            <label>计算期间</label>
                            <input type="month" id="calculation-period" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <label>销售顾问</label>
                            <select id="calculation-advisor" class="form-control">
                                <option value="">全部</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label>车型</label>
                            <select id="calculation-model" class="form-control">
                                <option value="">全部</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary" onclick="rebateFunctions.filterCalculations()" style="margin-top: 25px;">
                                <i class="fas fa-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="calculations-container"></div>
            </div>
        `;
        
        this.renderCalculations();
    },

    // 渲染计算结果
    renderCalculations: function() {
        const container = document.getElementById('calculations-container');
        if (!container) return;

        let html = `
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>销售顾问</th>
                            <th>车型</th>
                            <th>基础金额</th>
                            <th>返利金额</th>
                            <th>计算日期</th>
                            <th>期间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        this.allCalculations.forEach(calc => {
            html += `
                <tr>
                    <td>${calc.orderNumber}</td>
                    <td>${calc.salesAdvisor}</td>
                    <td>${calc.carModel}</td>
                    <td>¥${calc.baseAmount.toLocaleString()}</td>
                    <td>¥${calc.rebateAmount.toLocaleString()}</td>
                    <td>${calc.calculationDate.split('T')[0]}</td>
                    <td>${calc.period}</td>
                    <td>
                        <span class="badge ${calc.status === 'confirmed' ? 'badge-success' : 'badge-warning'}">
                            ${calc.status === 'confirmed' ? '已确认' : '待确认'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm" onclick="rebateFunctions.viewCalculationDetail(${calc.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm" onclick="rebateFunctions.editCalculation(${calc.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
        
        container.innerHTML = html;
    },

    // 渲染报表管理页面
    renderReportManagement: function() {
        const contentDiv = document.getElementById('rebate-content');
        contentDiv.innerHTML = `
            <div class="report-management">
                <div class="toolbar">
                    <button class="btn btn-primary" onclick="rebateFunctions.generateReport()">
                        <i class="fas fa-chart-bar"></i> 生成报表
                    </button>
                    <button class="btn btn-secondary" onclick="rebateFunctions.exportReport()">
                        <i class="fas fa-download"></i> 导出报表
                    </button>
                </div>
                
                <div id="reports-container"></div>
            </div>
        `;
        
        this.renderReports();
    },

    // 渲染报表列表
    renderReports: function() {
        const container = document.getElementById('reports-container');
        if (!container) return;

        let html = `
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>报表类型</th>
                            <th>期间</th>
                            <th>总金额</th>
                            <th>项目数量</th>
                            <th>生成日期</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        this.allReports.forEach(report => {
            html += `
                <tr>
                    <td>${report.reportType}</td>
                    <td>${report.period}</td>
                    <td>¥${report.totalAmount.toLocaleString()}</td>
                    <td>${report.itemCount}</td>
                    <td>${report.generateDate.split('T')[0]}</td>
                    <td>
                        <span class="badge ${report.status === 'completed' ? 'badge-success' : 'badge-warning'}">
                            ${report.status === 'completed' ? '已完成' : '处理中'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm" onclick="rebateFunctions.viewReport(${report.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm" onclick="rebateFunctions.downloadReport(${report.id})">
                            <i class="fas fa-download"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;
    },

    // ==================== 返利计算引擎 ====================

    // 计算返利
    calculateRebates: async function() {
        const period = document.getElementById('calculation-period')?.value;
        if (!period) {
            alert('请选择计算期间');
            return;
        }

        try {
            // 获取该期间的订单数据
            const orders = await this.getOrdersByPeriod(period);
            if (orders.length === 0) {
                alert('该期间没有订单数据');
                return;
            }

            // 获取活跃的政策项目
            const activePolicies = this.allPolicyItems.filter(item =>
                item.status === 'active' &&
                this.isPolicyValidForPeriod(item, period)
            );

            if (activePolicies.length === 0) {
                alert('该期间没有有效的政策配置');
                return;
            }

            let calculationResults = [];

            // 为每个订单计算返利
            for (const order of orders) {
                for (const policy of activePolicies) {
                    const rebateAmount = await this.calculateSingleRebate(order, policy);
                    if (rebateAmount > 0) {
                        const calculation = {
                            policyItemId: policy.id,
                            orderNumber: order.orderNumber,
                            salesAdvisor: order.salesAdvisor,
                            carModel: order.carModel,
                            baseAmount: parseFloat(order.contractPrice) || 0,
                            rebateAmount: rebateAmount,
                            period: period,
                            status: 'pending',
                            notes: `基于政策: ${policy.itemName}`
                        };

                        calculationResults.push(calculation);
                    }
                }
            }

            // 保存计算结果
            for (const result of calculationResults) {
                await window.dbFunctions.addRebateCalculation(result);
            }

            // 刷新数据和界面
            await this.loadAllData();
            this.renderCalculations();

            alert(`计算完成！共生成 ${calculationResults.length} 条返利记录`);

        } catch (error) {
            console.error('计算返利失败:', error);
            alert('计算失败，请重试');
        }
    },

    // 获取指定期间的订单
    getOrdersByPeriod: async function(period) {
        try {
            const allOrders = await window.dbFunctions.getAllOrderManagement();
            const [year, month] = period.split('-');

            return allOrders.filter(order => {
                const orderDate = new Date(order.orderDate);
                return orderDate.getFullYear() == year &&
                       (orderDate.getMonth() + 1) == month &&
                       order.auditStatus === '已审核';
            });
        } catch (error) {
            console.error('获取订单数据失败:', error);
            return [];
        }
    },

    // 检查政策是否在指定期间有效
    isPolicyValidForPeriod: function(policy, period) {
        const periodDate = new Date(period + '-01');
        const startDate = new Date(policy.startDate);
        const endDate = policy.endDate ? new Date(policy.endDate) : null;

        if (periodDate < startDate) return false;
        if (endDate && periodDate > endDate) return false;

        return true;
    },

    // 计算单个订单的返利
    calculateSingleRebate: async function(order, policy) {
        const baseAmount = parseFloat(order.contractPrice) || 0;
        if (baseAmount <= 0) return 0;

        switch (policy.calculationType) {
            case 'percentage':
                return this.calculatePercentageRebate(baseAmount, policy);
            case 'fixed_amount':
                return this.calculateFixedAmountRebate(order, policy);
            case 'tiered':
                return this.calculateTieredRebate(baseAmount, policy);
            default:
                return 0;
        }
    },

    // 百分比计算
    calculatePercentageRebate: function(baseAmount, policy) {
        const rate = parseFloat(policy.baseRate) || 0;
        return baseAmount * (rate / 100);
    },

    // 固定金额计算
    calculateFixedAmountRebate: function(order, policy) {
        // 检查是否满足条件
        if (this.checkPolicyConditions(order, policy)) {
            return parseFloat(policy.baseRate) || 0;
        }
        return 0;
    },

    // 阶梯式计算
    calculateTieredRebate: function(baseAmount, policy) {
        try {
            const conditions = JSON.parse(policy.conditions || '[]');
            for (const tier of conditions) {
                if (baseAmount >= tier.minAmount &&
                    (!tier.maxAmount || baseAmount <= tier.maxAmount)) {
                    return baseAmount * (tier.rate / 100);
                }
            }
        } catch (error) {
            console.error('阶梯式计算配置错误:', error);
        }
        return 0;
    },

    // 检查政策条件
    checkPolicyConditions: function(order, policy) {
        try {
            if (!policy.conditions) return true;

            const conditions = JSON.parse(policy.conditions);

            // 检查车型条件
            if (conditions.carModels && conditions.carModels.length > 0) {
                if (!conditions.carModels.includes(order.carModel)) {
                    return false;
                }
            }

            // 检查销售顾问条件
            if (conditions.salesAdvisors && conditions.salesAdvisors.length > 0) {
                if (!conditions.salesAdvisors.includes(order.salesAdvisor)) {
                    return false;
                }
            }

            // 检查金额条件
            if (conditions.minAmount && parseFloat(order.contractPrice) < conditions.minAmount) {
                return false;
            }

            if (conditions.maxAmount && parseFloat(order.contractPrice) > conditions.maxAmount) {
                return false;
            }

            return true;
        } catch (error) {
            console.error('条件检查失败:', error);
            return false;
        }
    },

    // ==================== 辅助函数 ====================

    // 根据分类名称查找ID
    findCategoryIdByName: function(categoryName) {
        const category = this.allCategories.find(cat => cat.categoryName === categoryName);
        return category ? category.id : null;
    },

    // 映射项目类型
    mapItemType: function(typeText) {
        const typeMap = {
            '固定类型': 'fixed',
            '达成率类型': 'achievement',
            '条件类型': 'condition'
        };
        return typeMap[typeText] || 'fixed';
    },

    // 映射计算类型
    mapCalculationType: function(typeText) {
        const typeMap = {
            '百分比': 'percentage',
            '固定金额': 'fixed_amount',
            '阶梯式': 'tiered'
        };
        return typeMap[typeText] || 'percentage';
    },

    // 格式化日期
    formatDate: function(dateValue) {
        if (!dateValue) return '';

        if (typeof dateValue === 'number') {
            // Excel日期序列号转换
            const excelEpoch = new Date(1900, 0, 1);
            const date = new Date(excelEpoch.getTime() + (dateValue - 2) * 24 * 60 * 60 * 1000);
            return date.toISOString().split('T')[0];
        }

        if (typeof dateValue === 'string') {
            const date = new Date(dateValue);
            if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0];
            }
        }

        return '';
    },

    // 过滤数据
    filterData: function(searchTerm) {
        // 实现搜索过滤逻辑
        console.log('搜索:', searchTerm);
    },

    // 过滤计算结果
    filterCalculations: function() {
        const period = document.getElementById('calculation-period')?.value;
        const advisor = document.getElementById('calculation-advisor')?.value;
        const model = document.getElementById('calculation-model')?.value;

        // 实现过滤逻辑
        console.log('过滤计算结果:', { period, advisor, model });
    }
};

// 确保模块立即可用
console.log('返利核算模块已加载');
