// 订单管理功能模块
window.orderFunctions = {
    allOrders: [],
    allInventory: [],
    allDeliveryTargets: [],
    allOrderTargets: [],
    allRetailTargets: [],
    currentTab: 'orders',

    // 统一日期格式化函数 - YYYY/M/DD
    formatDate: function(dateString) {
        if (!dateString) return '';

        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString; // 如果不是有效日期，返回原值

            const year = date.getFullYear();
            const month = date.getMonth() + 1; // 月份从0开始，需要+1
            const day = date.getDate();

            return `${year}/${month}/${day}`;
        } catch (error) {
            return dateString; // 出错时返回原值
        }
    },

    // 订单管理列设置
    orderColumnSettings: {
        orderNumber: true,
        auditStatus: true,
        orderDate: true,
        customerName: true,
        idNumber: false,
        phone1: true,
        address: false,
        salesAdvisor: true,
        carModel: true,
        configuration: false,
        exteriorColor: false,
        interiorColor: false,
        options: false,
        deliveryDate: false,
        deposit: false,
        contractPrice: false
    },
    
    // 加载订单管理模块
    loadOrders: async function() {
        try {
            // 加载列设置
            this.loadOrderColumnSettings();

            this.allOrders = await window.dbFunctions.getAllOrderManagement();
            this.allInventory = await window.dbFunctions.getAllInventoryManagement();
            this.allDeliveryTargets = await window.dbFunctions.getAllDeliveryTargets();
            this.allOrderTargets = await window.dbFunctions.getAllOrderTargets();
            this.allRetailTargets = await window.dbFunctions.getAllRetailTargets();
            this.allDeliveryManagement = await window.dbFunctions.getAllDeliveryManagement();
            this.renderOrderContent();
        } catch (error) {
            console.error('加载订单数据失败:', error);
        }
    },
    
    // 渲染订单管理内容
    renderOrderContent: function() {
        const moduleContainer = document.getElementById('order-module');
        if (!moduleContainer) return;
        
        moduleContainer.innerHTML = `
            <div class="module-header">
                <h1><i class="fas fa-shopping-cart"></i> 订单管理</h1>
            </div>
            
            <div class="card">
                <div class="order-tabs">
                    <div class="order-tab ${this.currentTab === 'orders' ? 'active' : ''}" data-tab="orders">
                        <i class="fas fa-shopping-cart"></i> 订单管理
                    </div>
                    <div class="order-tab ${this.currentTab === 'inventory' ? 'active' : ''}" data-tab="inventory">
                        <i class="fas fa-warehouse"></i> 库存管理
                    </div>
                    <div class="order-tab ${this.currentTab === 'paired-inventory' ? 'active' : ''}" data-tab="paired-inventory">
                        <i class="fas fa-link"></i> 已配车库存
                    </div>
                    <div class="order-tab ${this.currentTab === 'delivered-inventory' ? 'active' : ''}" data-tab="delivered-inventory">
                        <i class="fas fa-check-circle"></i> 已交车
                    </div>
                    <div class="order-tab ${this.currentTab === 'targets' ? 'active' : ''}" data-tab="targets">
                        <i class="fas fa-bullseye"></i> 目标管理
                    </div>
                    <div class="order-tab ${this.currentTab === 'audit' ? 'active' : ''}" data-tab="audit">
                        <i class="fas fa-clipboard-check"></i> 订单审核
                    </div>
                    <div class="order-tab ${this.currentTab === 'delivery' ? 'active' : ''}" data-tab="delivery">
                        <i class="fas fa-truck"></i> 配车交付
                    </div>
                </div>
                
                <div class="order-content" id="order-content">
                    ${this.renderTabContent()}
                </div>
            </div>
        `;
        
        // 绑定事件
        this.bindEvents();
    },
    
    // 渲染标签页内容
    renderTabContent: function() {
        switch(this.currentTab) {
            case 'orders':
                return this.renderOrdersTab();
            case 'inventory':
                return this.renderInventoryTab();
            case 'paired-inventory':
                return this.renderPairedInventoryTab();
            case 'delivered-inventory':
                return this.renderDeliveredInventoryTab();
            case 'targets':
                return this.renderTargetsTab();
            case 'audit':
                return this.renderOrderAuditTab();
            case 'delivery':
                return this.renderDeliveryManagementTab();
            default:
                return this.renderOrdersTab();
        }
    },
    
    // 渲染订单管理标签页
    renderOrdersTab: function() {
        return `
            <div class="tab-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 style="margin: 0;">订单管理 (${this.allOrders.length})</h2>
                <div style="display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">
                    <input type="month" id="order-month-filter" style="padding: 6px 8px; border-radius: 4px; border: 1px solid #ddd; font-size: 14px;" onchange="orderFunctions.filterOrdersByMonth(this.value)">
                    <input type="text" id="order-search" placeholder="搜索客户姓名或VIN..." style="padding: 6px 8px; border-radius: 4px; border: 1px solid #ddd; font-size: 14px; min-width: 200px;">
                    <button class="btn btn-outline btn-sm" onclick="orderFunctions.showOrderColumnSettings()" title="列设置">
                        <i class="fas fa-columns"></i>
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="orderFunctions.importOrdersFromExcel()" title="导入">
                        <i class="fas fa-upload"></i> 导入
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="orderFunctions.exportOrdersToExcel()" title="导出">
                        <i class="fas fa-download"></i> 导出
                    </button>
                    <button class="btn btn-primary btn-sm" id="new-order-btn">
                        <i class="fas fa-plus"></i> 新增订单
                    </button>
                </div>
            </div>
            
            <div class="order-list-content" id="all-orders">
                ${this.renderOrderList(this.allOrders)}
            </div>
        `;
    },
    
    // 渲染库存管理标签页
    renderInventoryTab: function() {
        // 过滤掉已配车状态的车辆，只显示可售和已交车状态的车辆
        const availableInventory = this.allInventory.filter(inv =>
            inv.inventoryStatus !== '已配车'
        );
        const uniqueModels = [...new Set(availableInventory.map(inv => inv.carModel).filter(Boolean))];
        const sortedInventory = availableInventory.sort((a, b) => (a.carModel || '').localeCompare(b.carModel || ''));

        return `
            <div class="tab-header">
                <h2>库存管理 (${availableInventory.length})</h2>
                <div>
                    <select id="inventory-model-filter" style="padding: 8px; border-radius: 4px; border: 1px solid #ddd; margin-right: 10px;" onchange="orderFunctions.filterInventoryByModel(this.value)">
                        <option value="">全部车型</option>
                        ${uniqueModels.map(model => `<option value="${model}">${model}</option>`).join('')}
                    </select>
                    <input type="text" id="inventory-search" placeholder="搜索车型或VIN..." style="padding: 8px; border-radius: 4px; border: 1px solid #ddd; margin-right: 10px;">
                    <button class="btn btn-secondary" id="import-inventory-btn" style="margin-right: 10px;">
                        <i class="fas fa-upload"></i> 导入
                    </button>
                    <button class="btn btn-secondary" id="export-inventory-btn" style="margin-right: 10px;">
                        <i class="fas fa-download"></i> 导出
                    </button>
                    <button class="btn btn-primary" id="new-inventory-btn">
                        <i class="fas fa-plus"></i> 新增库存
                    </button>
                </div>
            </div>

            <div class="inventory-list-content" id="all-inventory">
                ${this.renderInventoryList(this.filteredInventory || sortedInventory)}
            </div>
        `;
    },

    // 渲染已配车库存标签页
    renderPairedInventoryTab: function() {
        const pairedInventory = this.allInventory.filter(inv =>
            inv.inventoryStatus === '已配车'
        );
        const uniqueModels = [...new Set(pairedInventory.map(inv => inv.carModel).filter(Boolean))];

        return `
            <div class="tab-header">
                <h2>已配车库存 (${pairedInventory.length})</h2>
                <div>
                    <select id="paired-inventory-model-filter" style="padding: 8px; border-radius: 4px; border: 1px solid #ddd; margin-right: 10px;" onchange="orderFunctions.filterPairedInventoryByModel(this.value)">
                        <option value="">全部车型</option>
                        ${uniqueModels.map(model => `<option value="${model}">${model}</option>`).join('')}
                    </select>
                    <input type="text" id="paired-inventory-search" placeholder="搜索车型或VIN..." style="padding: 8px; border-radius: 4px; border: 1px solid #ddd; margin-right: 10px;">
                </div>
            </div>

            <div class="inventory-list-content" id="paired-inventory-list">
                ${this.renderInventoryList(pairedInventory)}
            </div>
        `;
    },

    // 渲染已交车标签页
    renderDeliveredInventoryTab: function() {
        const deliveredInventory = this.allInventory.filter(inv => inv.inventoryStatus === '已交车');
        const uniqueModels = [...new Set(deliveredInventory.map(inv => inv.carModel).filter(Boolean))];

        return `
            <div class="tab-header">
                <h2>已交车 (${deliveredInventory.length})</h2>
                <div>
                    <select id="delivered-inventory-model-filter" style="padding: 8px; border-radius: 4px; border: 1px solid #ddd; margin-right: 10px;" onchange="orderFunctions.filterDeliveredInventoryByModel(this.value)">
                        <option value="">全部车型</option>
                        ${uniqueModels.map(model => `<option value="${model}">${model}</option>`).join('')}
                    </select>
                    <input type="text" id="delivered-inventory-search" placeholder="搜索车型或VIN..." style="padding: 8px; border-radius: 4px; border: 1px solid #ddd; margin-right: 10px;">
                </div>
            </div>

            <div class="inventory-list-content" id="delivered-inventory-list">
                ${this.renderInventoryList(deliveredInventory)}
            </div>
        `;
    },

    // 渲染目标管理标签页
    renderTargetsTab: function() {
        return `
            <div class="tab-header" style="margin-bottom: 15px;">
                <h2 style="font-size: 20px; margin: 0;">目标管理</h2>
                <div style="font-size: 14px; color: var(--gray);">
                    管理提车、订单、零售三大目标指标
                </div>
            </div>

            <div class="targets-container">
                <div class="target-section">
                    <h3><i class="fas fa-truck"></i> 提车目标</h3>
                    <div class="target-actions">
                        <button class="btn btn-primary btn-sm" id="new-delivery-target-btn" style="font-size: 11px; padding: 4px 8px;">
                            <i class="fas fa-plus"></i> 新增
                        </button>
                    </div>
                    <div id="delivery-targets">
                        ${this.renderTargetList(this.allDeliveryTargets, 'delivery')}
                    </div>
                </div>

                <div class="target-section">
                    <h3><i class="fas fa-shopping-cart"></i> 订单目标</h3>
                    <div class="target-actions">
                        <button class="btn btn-primary btn-sm" id="new-order-target-btn" style="font-size: 11px; padding: 4px 8px;">
                            <i class="fas fa-plus"></i> 新增
                        </button>
                    </div>
                    <div id="order-targets">
                        ${this.renderTargetList(this.allOrderTargets, 'order')}
                    </div>
                </div>

                <div class="target-section">
                    <h3><i class="fas fa-chart-line"></i> 零售目标</h3>
                    <div class="target-actions">
                        <button class="btn btn-primary btn-sm" id="new-retail-target-btn" style="font-size: 11px; padding: 4px 8px;">
                            <i class="fas fa-plus"></i> 新增
                        </button>
                    </div>
                    <div id="retail-targets">
                        ${this.renderTargetList(this.allRetailTargets, 'retail')}
                    </div>
                </div>
            </div>
        `;
    },
    
    // 渲染订单列表
    renderOrderList: function(orders) {
        if (orders.length === 0) {
            return '<div style="text-align: center; padding: 40px; color: var(--gray);">暂无订单数据</div>';
        }

        // 定义所有可能的列
        const allColumns = {
            orderNumber: { label: '订单号', render: (order, index) => order.orderNumber || `生成中...` },
            auditStatus: { label: '审核状态', render: (order) => this.renderAuditStatusBadge(order.auditStatus) },
            orderDate: { label: '订单日期', render: (order) => this.formatDate(order.orderDate) || '-' },
            customerName: { label: '客户名称', render: (order) => order.customerName || '-' },
            idNumber: { label: '身份证号码', render: (order) => order.idNumber || '-' },
            phone1: { label: '联系手机', render: (order) => order.phone1 || '-' },
            address: { label: '联系地址', render: (order) => order.address || '-' },
            salesAdvisor: { label: '销售顾问', render: (order) => order.salesAdvisor || '-' },
            vin: { label: 'VIN', render: (order) => order.vin || '-' },
            carModel: { label: '车型', render: (order) => order.carModel || '-' },
            configuration: { label: '配置', render: (order) => order.configuration || '-' },
            exteriorColor: { label: '外色', render: (order) => order.exteriorColor || '-' },
            interiorColor: { label: '内饰', render: (order) => order.interiorColor || '-' },
            options: { label: '选装件', render: (order) => order.options || '-' },
            deliveryDate: { label: '交付日期', render: (order) => this.formatDate(order.deliveryDate) || '-' },
            deposit: { label: '定金', render: (order) => order.deposit ? '¥' + order.deposit : '-' },
            contractPrice: { label: '合同价', render: (order) => order.contractPrice ? '¥' + order.contractPrice : '-' }
        };

        // 获取要显示的列
        const visibleColumns = Object.keys(allColumns).filter(key => this.orderColumnSettings[key]);

        return `
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            ${visibleColumns.map(key => `<th>${allColumns[key].label}</th>`).join('')}
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${orders.map((order, index) => `
                            <tr>
                                ${visibleColumns.map(key => `<td>${allColumns[key].render(order, index)}</td>`).join('')}
                                <td>
                                    ${this.renderOrderActionButtons(order)}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    // 渲染审核状态徽章
    renderAuditStatusBadge: function(auditStatus) {
        const statusMap = {
            '待审核': 'status-warning',
            '已审核': 'status-info',
            '已配车': 'status-primary',
            '已交车': 'status-success',
            '驳回': 'status-danger'
        };

        const statusClass = statusMap[auditStatus] || 'status-secondary';
        return `<span class="status ${statusClass}">${auditStatus || '-'}</span>`;
    },

    // 渲染订单审核标签页
    renderOrderAuditTab: function() {
        // 获取所有审核相关的订单，包括待审核和已审核
        const auditOrders = this.allOrders.filter(order =>
            order.auditStatus === '待审核' || order.auditStatus === '已审核'
        );

        // 排序：待审核状态的订单置顶显示
        auditOrders.sort((a, b) => {
            if (a.auditStatus === '待审核' && b.auditStatus !== '待审核') return -1;
            if (a.auditStatus !== '待审核' && b.auditStatus === '待审核') return 1;
            // 同状态按订单日期倒序
            return new Date(b.orderDate || 0) - new Date(a.orderDate || 0);
        });

        const pendingCount = auditOrders.filter(order => order.auditStatus === '待审核').length;
        const approvedCount = auditOrders.filter(order => order.auditStatus === '已审核').length;

        return `
            <div class="tab-header">
                <h2>订单审核 (待审核: ${pendingCount}, 已审核: ${approvedCount})</h2>
            </div>
            ${this.renderOrderAuditList(auditOrders)}
        `;
    },

    // 渲染订单审核列表
    renderOrderAuditList: function(orders) {
        return `
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>审核状态</th>
                            <th>订单日期</th>
                            <th>客户名称</th>
                            <th>联系手机</th>
                            <th>销售顾问</th>
                            <th>VIN</th>
                            <th>车型</th>
                            <th>配置</th>
                            <th>外色</th>
                            <th>内饰</th>
                            <th>选装件</th>
                            <th>交付日期</th>
                            <th>定金</th>
                            <th>合同价</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${orders.map((order, index) => `
                            <tr>
                                <td>${order.serialNumber || index + 1}</td>
                                <td><span class="status status-warning">${order.auditStatus}</span></td>
                                <td>${this.formatDate(order.orderDate)}</td>
                                <td>${order.customerName}</td>
                                <td>${order.phone1}</td>
                                <td>${order.salesAdvisor || '-'}</td>
                                <td>${order.vin || '-'}</td>
                                <td>${order.carModel || '-'}</td>
                                <td>${order.configuration || '-'}</td>
                                <td>${order.exteriorColor || '-'}</td>
                                <td>${order.interiorColor || '-'}</td>
                                <td>${order.options || '-'}</td>
                                <td>${this.formatDate(order.deliveryDate) || '-'}</td>
                                <td>${order.deposit ? '¥' + order.deposit : '-'}</td>
                                <td>${order.contractPrice ? '¥' + order.contractPrice : '-'}</td>
                                <td>
                                    ${order.auditStatus === '待审核' ? `
                                        <button class="btn btn-sm btn-success" onclick="orderFunctions.approveOrder(${order.id})">
                                            <i class="fas fa-check"></i> 审核
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="orderFunctions.rejectOrder(${order.id})">
                                            <i class="fas fa-times"></i> 驳回
                                        </button>
                                    ` : `
                                        <button class="btn btn-sm btn-warning" onclick="orderFunctions.returnToAudit(${order.id})" title="返审核">
                                            <i class="fas fa-undo"></i> 返审核
                                        </button>
                                    `}
                                    <button class="btn btn-sm btn-info" onclick="orderFunctions.showOrderDetails(${order.id})" title="查看详情">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    // 渲染配车交付标签页
    renderDeliveryManagementTab: function() {
        // 获取所有配车交付数据并排序：待配车状态置顶
        const deliveries = (this.allDeliveryManagement || []).slice();
        deliveries.sort((a, b) => {
            // 已审核状态置顶（等待配车）
            if ((a.auditStatus === '已审核' || !a.auditStatus) && b.auditStatus !== '已审核') return -1;
            if (a.auditStatus !== '已审核' && (b.auditStatus === '已审核' || !b.auditStatus)) return 1;
            // 同状态按订单日期倒序
            return new Date(b.orderDate || 0) - new Date(a.orderDate || 0);
        });

        const pendingCount = deliveries.filter(d => d.auditStatus === '已审核' || !d.auditStatus).length;
        const assignedCount = deliveries.filter(d => d.auditStatus === '已配车').length;
        const deliveredCount = deliveries.filter(d => d.auditStatus === '已交车').length;

        return `
            <div class="tab-header">
                <h2>配车交付 (待配车: ${pendingCount}, 已配车: ${assignedCount}, 已交付: ${deliveredCount})</h2>
                <div>
                    <button class="btn btn-primary" onclick="orderFunctions.syncOrdersToDelivery()">
                        <i class="fas fa-sync"></i> 同步已审核订单
                    </button>
                </div>
            </div>
            ${this.renderDeliveryManagementList(deliveries)}
        `;
    },

    // 渲染配车交付列表
    renderDeliveryManagementList: function(deliveries) {
        return `
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>审核状态</th>
                            <th>订单日期</th>
                            <th>交付日期</th>
                            <th>客户名称</th>
                            <th>联系手机</th>
                            <th>销售顾问</th>
                            <th>车型</th>
                            <th>配置</th>
                            <th>外色</th>
                            <th>内饰</th>
                            <th>VIN</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${deliveries.map((delivery, index) => `
                            <tr>
                                <td>${delivery.serialNumber || index + 1}</td>
                                <td>${this.renderAuditStatusBadge(delivery.auditStatus)}</td>
                                <td>${this.formatDate(delivery.orderDate)}</td>
                                <td>${this.formatDate(delivery.deliveryDate) || '-'}</td>
                                <td>${delivery.customerName}</td>
                                <td>${delivery.phone1}</td>
                                <td>${delivery.salesAdvisor || '-'}</td>
                                <td>${delivery.carModel || '-'}</td>
                                <td>${delivery.configuration || '-'}</td>
                                <td>${delivery.exteriorColor || '-'}</td>
                                <td>${delivery.interiorColor || '-'}</td>
                                <td>${delivery.vin || '-'}</td>
                                <td>
                                    ${delivery.auditStatus === '已审核' || !delivery.auditStatus ? `
                                        <button class="btn btn-sm btn-primary" onclick="orderFunctions.assignVehicle(${delivery.id})" title="配车">
                                            <i class="fas fa-car"></i> 配车
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="orderFunctions.deleteDelivery(${delivery.id})" title="删除">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    ` : ''}
                                    ${delivery.auditStatus === '已配车' ? `
                                        <button class="btn btn-sm btn-success" onclick="orderFunctions.deliverVehicle(${delivery.id})" title="交付">
                                            <i class="fas fa-truck"></i> 交付
                                        </button>
                                        <button class="btn btn-sm btn-warning" onclick="orderFunctions.unassignVehicle(${delivery.id})" title="返配车">
                                            <i class="fas fa-undo"></i> 返配车
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="orderFunctions.deleteDelivery(${delivery.id})" title="删除">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    ` : ''}
                                    ${delivery.auditStatus === '已交车' ? `
                                        <button class="btn btn-sm btn-info" onclick="orderFunctions.returnDelivery(${delivery.id})" title="返交车">
                                            <i class="fas fa-undo-alt"></i> 返交车
                                        </button>
                                    ` : ''}
                                    <button class="btn btn-sm btn-secondary" onclick="orderFunctions.showDeliveryDetails(${delivery.id})" title="查看详情">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    // 渲染库存列表
    renderInventoryList: function(inventory) {
        if (inventory.length === 0) {
            return '<div style="text-align: center; padding: 40px; color: var(--gray);">暂无库存数据</div>';
        }

        return `
            <div class="table-container">
                <table class="data-table inventory-table">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>库存状态</th>
                            <th>车型</th>
                            <th>版本</th>
                            <th>车架号</th>
                            <th>外色</th>
                            <th>内饰</th>
                            <th>原厂选装</th>
                            <th>标准</th>
                            <th>位置</th>
                            <th>生产日期</th>
                            <th>发运日期</th>
                            <th>入库日期</th>
                            <th>库龄</th>
                            <th>指导价</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${inventory.map((item, index) => {
                            // 计算库龄和样式类
                            let stockAge = 0;
                            let ageClass = '';
                            if (item.stockDate) {
                                stockAge = Math.floor((new Date() - new Date(item.stockDate)) / (1000 * 60 * 60 * 24));
                                if (stockAge <= 60) {
                                    ageClass = 'stock-age-good';
                                } else if (stockAge <= 90) {
                                    ageClass = 'stock-age-warning';
                                } else if (stockAge <= 180) {
                                    ageClass = 'stock-age-caution';
                                } else {
                                    ageClass = 'stock-age-danger';
                                }
                            }

                            return `
                            <tr>
                                <td>${item.serialNumber || index + 1}</td>
                                <td><span class="status inventory-${item.inventoryStatus}">${item.inventoryStatus || '-'}</span></td>
                                <td>${item.carModel || '-'}</td>
                                <td>${item.version || '-'}</td>
                                <td>${item.vin || '-'}</td>
                                <td>${item.exteriorColor || '-'}</td>
                                <td>${item.interiorColor || '-'}</td>
                                <td>${item.factoryOptions || '-'}</td>
                                <td>${item.standard || '-'}</td>
                                <td>${item.location || '-'}</td>
                                <td>${item.productionDate || '-'}</td>
                                <td>${item.shipmentDate || '-'}</td>
                                <td>${item.stockDate || '-'}</td>
                                <td><span class="status ${ageClass}">${stockAge > 0 ? stockAge + '天' : '-'}</span></td>
                                <td>${item.guidePrice ? '¥' + item.guidePrice : '-'}</td>
                                <td>${item.notes || '-'}</td>
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="orderFunctions.showInventoryDetails(${item.id})" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteInventory(${item.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },
    
    // 渲染目标列表
    renderTargetList: function(targets, type) {
        if (targets.length === 0) {
            return '<div style="text-align: center; padding: 15px; color: var(--gray); font-size: 12px;">暂无数据</div>';
        }

        // 只显示最近3个月的数据，如果数据较多的话
        const displayTargets = targets.slice(-3);

        return `
            <div class="table-container" style="max-height: 200px; overflow-y: auto;">
                <table class="data-table targets-table">
                    <thead>
                        <tr>
                            <th style="width: 25%;">年月</th>
                            <th style="width: 20%;">目标</th>
                            <th style="width: 20%;">实际</th>
                            <th style="width: 20%;">完成率</th>
                            <th style="width: 15%;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${displayTargets.map(target => `
                            <tr>
                                <td style="font-size: 11px;">${target.yearMonth}</td>
                                <td style="font-size: 11px;">${target.target}</td>
                                <td style="font-size: 11px;">${target.actual}</td>
                                <td><span class="completion-rate ${target.completionRate >= 100 ? 'high' : target.completionRate >= 80 ? 'medium' : 'low'}">${target.completionRate}%</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline" onclick="editTarget(${target.id}, '${type}')" title="编辑" style="padding: 2px 4px; font-size: 10px; margin-right: 2px;">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteTarget(${target.id}, '${type}')" title="删除" style="padding: 2px 4px; font-size: 10px;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            ${targets.length > 3 ? `<div style="text-align: center; margin-top: 8px; font-size: 11px; color: var(--gray);">显示最近3条记录，共${targets.length}条</div>` : ''}
        `;
    },

    // 切换标签页
    switchTab: function(tabName) {
        this.currentTab = tabName;
        const contentContainer = document.getElementById('order-content');
        if (contentContainer) {
            contentContainer.innerHTML = this.renderTabContent();
        }

        // 更新标签页样式
        document.querySelectorAll('.order-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 重新绑定事件
        this.bindTabEvents();
    },

    // 绑定事件
    bindEvents: function() {
        this.bindTabEvents();
    },

    // 绑定标签页事件
    bindTabEvents: function() {
        // 标签页切换事件
        document.querySelectorAll('.order-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.currentTarget.dataset.tab;
                this.switchTab(tabName);
            });
        });

        // 根据当前标签页绑定相应事件
        switch(this.currentTab) {
            case 'orders':
                this.bindOrderEvents();
                break;
            case 'inventory':
                this.bindInventoryEvents();
                break;
            case 'paired-inventory':
                this.bindPairedInventoryEvents();
                break;
            case 'delivered-inventory':
                this.bindDeliveredInventoryEvents();
                break;
            case 'targets':
                this.bindTargetEvents();
                break;
        }
    },

    // 绑定订单管理事件
    bindOrderEvents: function() {
        const newOrderBtn = document.getElementById('new-order-btn');
        if (newOrderBtn) {
            newOrderBtn.addEventListener('click', () => this.showOrderForm());
        }

        const searchInput = document.getElementById('order-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const filteredOrders = this.allOrders.filter(order =>
                    order.customerName.toLowerCase().includes(searchTerm) ||
                    order.vin.toLowerCase().includes(searchTerm)
                );

                const listContainer = document.getElementById('all-orders');
                if (listContainer) {
                    listContainer.innerHTML = this.renderOrderList(filteredOrders);
                }
            });
        }
    },

    // 绑定库存管理事件
    bindInventoryEvents: function() {
        const newInventoryBtn = document.getElementById('new-inventory-btn');
        if (newInventoryBtn) {
            newInventoryBtn.addEventListener('click', () => this.showInventoryForm());
        }

        const importInventoryBtn = document.getElementById('import-inventory-btn');
        if (importInventoryBtn) {
            importInventoryBtn.addEventListener('click', () => this.showImportInventoryModal());
        }

        const exportInventoryBtn = document.getElementById('export-inventory-btn');
        if (exportInventoryBtn) {
            exportInventoryBtn.addEventListener('click', () => this.exportInventoryData());
        }

        const searchInput = document.getElementById('inventory-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const filteredInventory = this.allInventory.filter(item =>
                    item.carModel.toLowerCase().includes(searchTerm) ||
                    item.vin.toLowerCase().includes(searchTerm)
                );

                const listContainer = document.getElementById('all-inventory');
                if (listContainer) {
                    listContainer.innerHTML = this.renderInventoryList(filteredInventory);
                }
            });
        }
    },

    // 绑定已配车库存事件
    bindPairedInventoryEvents: function() {
        const searchInput = document.getElementById('paired-inventory-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const pairedInventory = this.allInventory.filter(inv => inv.inventoryStatus === '已配车');
                const filteredInventory = pairedInventory.filter(item =>
                    item.carModel.toLowerCase().includes(searchTerm) ||
                    item.vin.toLowerCase().includes(searchTerm)
                );

                const listContainer = document.getElementById('paired-inventory-list');
                if (listContainer) {
                    listContainer.innerHTML = this.renderInventoryList(filteredInventory);
                }
            });
        }
    },

    // 绑定已交车事件
    bindDeliveredInventoryEvents: function() {
        const searchInput = document.getElementById('delivered-inventory-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const deliveredInventory = this.allInventory.filter(inv => inv.inventoryStatus === '已交车');
                const filteredInventory = deliveredInventory.filter(item =>
                    item.carModel.toLowerCase().includes(searchTerm) ||
                    item.vin.toLowerCase().includes(searchTerm)
                );

                const listContainer = document.getElementById('delivered-inventory-list');
                if (listContainer) {
                    listContainer.innerHTML = this.renderInventoryList(filteredInventory);
                }
            });
        }
    },

    // 绑定目标管理事件
    bindTargetEvents: function() {
        const newDeliveryTargetBtn = document.getElementById('new-delivery-target-btn');
        if (newDeliveryTargetBtn) {
            newDeliveryTargetBtn.addEventListener('click', () => this.showTargetForm('delivery'));
        }

        const newOrderTargetBtn = document.getElementById('new-order-target-btn');
        if (newOrderTargetBtn) {
            newOrderTargetBtn.addEventListener('click', () => this.showTargetForm('order'));
        }

        const newRetailTargetBtn = document.getElementById('new-retail-target-btn');
        if (newRetailTargetBtn) {
            newRetailTargetBtn.addEventListener('click', () => this.showTargetForm('retail'));
        }
    },

    // 显示订单表单
    showOrderForm: async function(editId = null) {
        try {
            // 只获取在职的销售顾问用于新增，编辑时获取所有销售顾问以保持历史数据
            const salesAdvisors = editId ?
                await window.dbFunctions.getAllSalesAdvisors() :
                await window.dbFunctions.getActiveSalesAdvisors();
            const carModels = await window.dbFunctions.getAllCarModels();
            const highEndParts = await window.dbFunctions.getPartsByAttribute('精品');

            // 存储可用配件数据供其他函数使用
            this.availableParts = highEndParts;

            let order = {
                serialNumber: '',
                orderDate: new Date().toISOString().split('T')[0],
                customerName: '',
                idNumber: '',
                phone1: '',
                address: '',
                auditStatus: '待审核',
                salesAdvisor: '',
                vin: '',
                carModel: '',
                configuration: '',
                exteriorColor: '',
                interiorColor: '',
                options: '',

                deliveryDate: '',
                deposit: '',
                contractPrice: ''
            };

            if (editId !== null) {
                order = await window.dbFunctions.getOrderManagementById(editId) || order;
            }

            this.showModal('订单管理', this.renderOrderForm(order, salesAdvisors, carModels, highEndParts, editId));
        } catch (error) {
            console.error('显示订单表单失败:', error);
            alert('显示订单表单失败: ' + error.message);
        }
    },

    // 显示订单详情
    showOrderDetails: async function(orderId) {
        try {
            const order = await window.dbFunctions.getOrderManagementById(orderId);
            if (!order) {
                alert('订单不存在');
                return;
            }

            // 根据审批状态决定是否可编辑
            const isEditable = order.auditStatus === '待审核' || order.auditStatus === '驳回';

            if (isEditable) {
                // 可编辑状态，显示编辑表单
                this.showOrderForm(orderId);
            } else {
                // 只读状态，显示详情
                this.showModal('订单详情', this.renderOrderDetails(order));
            }
        } catch (error) {
            console.error('显示订单详情失败:', error);
            alert('显示订单详情失败: ' + error.message);
        }
    },

    // 渲染订单详情
    renderOrderDetails: function(order) {
        return `
            <div class="order-details" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="detail-group">
                    <label>订单号：</label>
                    <span>${order.serialNumber || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>审核状态：</label>
                    <span class="status audit-${order.auditStatus}">${order.auditStatus || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>订单日期：</label>
                    <span>${this.formatDate(order.orderDate) || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>客户名称：</label>
                    <span>${order.customerName || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>身份证号码：</label>
                    <span>${order.idNumber || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>联系手机：</label>
                    <span>${order.phone1 || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>联系地址：</label>
                    <span>${order.address || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>销售顾问：</label>
                    <span>${order.salesAdvisor || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>VIN码：</label>
                    <span>${order.vin || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>车型：</label>
                    <span>${order.carModel || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>配置：</label>
                    <span>${order.configuration || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>外色：</label>
                    <span>${order.exteriorColor || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>内饰：</label>
                    <span>${order.interiorColor || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>选装件：</label>
                    <span>${order.options || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>交付日期：</label>
                    <span>${this.formatDate(order.deliveryDate) || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>定金：</label>
                    <span>${order.deposit ? '¥' + order.deposit : '-'}</span>
                </div>
                <div class="detail-group">
                    <label>合同价：</label>
                    <span>${order.contractPrice ? '¥' + order.contractPrice : '-'}</span>
                </div>
                <div class="detail-group">
                    <label>审核状态：</label>
                    <span>${this.renderAuditStatusBadge(order.auditStatus)}</span>
                </div>

                <div class="detail-actions" style="grid-column: 1 / -1; margin-top: 20px; text-align: center;">
                    ${this.renderOrderActionButtons(order)}
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()" style="margin-left: 10px;">
                        <i class="fas fa-times"></i> 关闭
                    </button>
                </div>
            </div>
        `;
    },

    // 根据订单状态渲染操作按钮（带权限控制）
    renderOrderActionButtons: function(order) {
        let buttons = '';

        // 查看按钮：所有状态都可以查看
        buttons += `
            <button class="btn btn-sm btn-info" onclick="orderFunctions.showOrderDetails(${order.id})" title="查看">
                <i class="fas fa-eye"></i>
            </button>
        `;

        // 删除按钮：只有待审核状态的订单可以删除
        if (order.auditStatus === '待审核') {
            buttons += `
                <button class="btn btn-sm btn-danger" onclick="orderFunctions.deleteOrder(${order.id})" title="删除" style="margin-left: 5px;">
                    <i class="fas fa-trash"></i>
                </button>
            `;
        } else {
            // 已审核状态的订单：显示禁用的删除按钮
            buttons += `
                <button class="btn btn-sm btn-secondary" disabled title="已审核订单不可删除" style="margin-left: 5px; opacity: 0.5;">
                    <i class="fas fa-trash"></i>
                </button>
            `;
        }

        return buttons;
    },

    // 显示订单详情（只读预览）
    showOrderDetails: async function(orderId) {
        try {
            const order = await window.dbFunctions.getOrderManagementById(orderId);
            if (!order) {
                alert('订单不存在');
                return;
            }

            this.showModal('订单详情', this.renderOrderDetailsReadOnly(order));
        } catch (error) {
            console.error('显示订单详情失败:', error);
            alert('显示订单详情失败: ' + error.message);
        }
    },

    // 删除订单
    deleteOrder: async function(orderId) {
        try {
            const order = await window.dbFunctions.getOrderManagementById(orderId);
            if (!order) {
                alert('订单不存在');
                return;
            }

            // 权限检查：只有待审核状态的订单可以删除
            if (order.auditStatus !== '待审核') {
                alert('只有待审核状态的订单可以删除');
                return;
            }

            if (!confirm(`确定要删除这个订单吗？\n客户：${order.customerName}\n车型：${order.carModel}\n\n删除后将无法恢复！`)) {
                return;
            }

            await window.dbFunctions.deleteOrderManagement(orderId);
            alert('订单删除成功！');
            await this.loadOrders();
            this.switchTab('orders');
        } catch (error) {
            console.error('删除订单失败:', error);
            alert('删除订单失败: ' + error.message);
        }
    },

    // 渲染订单详情（只读预览）
    renderOrderDetailsReadOnly: function(order) {
        return `
            <div class="order-details" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="detail-group">
                    <label>订单号：</label>
                    <span>${order.orderNumber || '待生成'}</span>
                </div>
                <div class="detail-group">
                    <label>订单号：</label>
                    <span>${order.serialNumber || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>审核状态：</label>
                    <span>${this.renderAuditStatusBadge(order.auditStatus)}</span>
                </div>
                <div class="detail-group">
                    <label>订单日期：</label>
                    <span>${this.formatDate(order.orderDate) || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>客户名称：</label>
                    <span>${order.customerName || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>联系手机：</label>
                    <span>${order.phone1 || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>销售顾问：</label>
                    <span>${order.salesAdvisor || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>VIN：</label>
                    <span>${order.vin || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>车型：</label>
                    <span>${order.carModel || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>配置：</label>
                    <span>${order.configuration || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>外色：</label>
                    <span>${order.exteriorColor || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>内饰：</label>
                    <span>${order.interiorColor || '-'}</span>
                </div>
                <div class="detail-group" style="grid-column: 1 / -1;">
                    <label>选装件：</label>
                    <span>${order.options || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>交付日期：</label>
                    <span>${this.formatDate(order.deliveryDate) || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>定金：</label>
                    <span>${order.deposit ? '¥' + order.deposit : '-'}</span>
                </div>
                <div class="detail-group">
                    <label>合同价：</label>
                    <span>${order.contractPrice ? '¥' + order.contractPrice : '-'}</span>
                </div>

                <div class="detail-actions" style="grid-column: 1 / -1; margin-top: 20px; text-align: center;">
                    ${order.auditStatus === '待审核' ? `
                        <button type="button" class="btn btn-primary" onclick="orderFunctions.editOrderFromDetails(${order.id})">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                    ` : `
                        <button type="button" class="btn btn-secondary" disabled title="已审核订单不可编辑" style="opacity: 0.5;">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                    `}
                    ${this.renderOrderDetailActionButtons(order)}
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()" style="margin-left: 10px;">
                        <i class="fas fa-times"></i> 关闭
                    </button>
                </div>
            </div>
        `;
    },

    // 渲染订单详情页面的操作按钮
    renderOrderDetailActionButtons: function(order) {
        let buttons = '';

        // 根据审核状态显示不同的操作按钮
        if (order.auditStatus === '已审核') {
            // 已审核状态：显示打印合同按钮
            buttons += `
                <button type="button" class="btn btn-primary" onclick="orderFunctions.printContract(${order.id})" style="margin-left: 10px;">
                    <i class="fas fa-print"></i> 打印合同
                </button>
            `;
        }

        if (order.auditStatus === '已配车') {
            // 已配车状态：显示发票申请和打印交车单按钮
            buttons += `
                <button type="button" class="btn btn-warning" onclick="orderFunctions.showInvoiceApplication(${order.id})" style="margin-left: 10px;">
                    <i class="fas fa-file-invoice"></i> 发票申请
                </button>
                <button type="button" class="btn btn-success" onclick="orderFunctions.printDeliveryNote(${order.id})" style="margin-left: 10px;">
                    <i class="fas fa-print"></i> 打印交车单
                </button>
            `;
        }

        return buttons;
    },

    // 从详情页面编辑订单
    editOrderFromDetails: async function(orderId) {
        try {
            // 权限检查
            const order = await window.dbFunctions.getOrderManagementById(orderId);
            if (!order) {
                alert('订单不存在');
                return;
            }

            if (order.auditStatus !== '待审核') {
                alert('只有待审核状态的订单可以编辑');
                return;
            }

            // 关闭当前详情模态框
            const modalOverlay = document.querySelector('.modal-overlay');
            if (modalOverlay) {
                modalOverlay.remove();
            }

            // 显示编辑表单
            await this.showOrderForm(orderId);
        } catch (error) {
            console.error('编辑订单失败:', error);
            alert('编辑订单失败: ' + error.message);
        }
    },

    // 打印合同功能
    printContract: async function(orderId) {
        try {
            // 获取订单详细信息
            const order = await window.dbFunctions.getOrderManagementById(orderId);
            if (!order) {
                alert('订单信息不存在');
                return;
            }

            // 准备传递给合同页面的数据
            const contractData = {
                // 客户信息
                customerName: order.customerName || '',
                idNumber: order.idNumber || '',
                phone: order.phone1 || '',
                address: order.address || '',

                // 车辆信息
                carModel: order.carModel || '',
                configuration: order.configuration || '',
                exteriorColor: order.exteriorColor || '',
                interiorColor: order.interiorColor || '',
                vin: order.vin || '',

                // 价格信息
                contractPrice: order.contractPrice || '',
                deposit: order.deposit || '',

                // 日期信息
                orderDate: order.orderDate || '', // 签署日期
                deliveryDate: order.deliveryDate || '', // 交付日期

                // 其他信息
                orderNumber: order.orderNumber || '',
                salesAdvisor: order.salesAdvisor || ''
            };

            // 将数据存储到 localStorage
            localStorage.setItem('contractData', JSON.stringify(contractData));

            // 打开合同页面
            window.open('contract.html', '_blank');

        } catch (error) {
            console.error('打印合同失败:', error);
            alert('打印合同失败: ' + error.message);
        }
    },

    // 打印交车单功能
    printDeliveryNote: function(orderId) {
        // 这里可以实现打印交车单的具体逻辑
        alert('打印交车单功能开发中...\n订单ID: ' + orderId);
    },

    // 发票申请功能
    showInvoiceApplication: async function(orderId) {
        try {
            const order = await window.dbFunctions.getOrderManagementById(orderId);
            if (!order) {
                alert('订单不存在');
                return;
            }

            const invoiceFormHtml = `
                <div style="padding: 20px;">
                    <h3 style="margin-bottom: 20px; color: #333;">发票申请</h3>

                    <form id="invoice-form">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div>
                                <label>订单号：</label>
                                <input type="text" value="${order.orderNumber || '待生成'}" readonly style="background-color: #f8f9fa;">
                            </div>
                            <div>
                                <label>客户名称：</label>
                                <input type="text" value="${order.customerName || ''}" readonly style="background-color: #f8f9fa;">
                            </div>
                            <div>
                                <label>发票类型：</label>
                                <select name="invoiceType" required>
                                    <option value="">请选择发票类型</option>
                                    <option value="普通发票">普通发票</option>
                                    <option value="专用发票">专用发票</option>
                                </select>
                            </div>
                            <div>
                                <label>发票金额：</label>
                                <input type="number" name="invoiceAmount" value="${order.contractPrice || ''}" step="0.01" required>
                            </div>
                            <div style="grid-column: 1 / -1;">
                                <label>发票抬头：</label>
                                <input type="text" name="invoiceTitle" placeholder="请输入发票抬头" required style="width: 100%;">
                            </div>
                            <div style="grid-column: 1 / -1;">
                                <label>纳税人识别号：</label>
                                <input type="text" name="taxNumber" placeholder="请输入纳税人识别号">
                            </div>
                            <div style="grid-column: 1 / -1;">
                                <label>备注：</label>
                                <textarea name="notes" placeholder="请输入备注信息" rows="3" style="width: 100%;"></textarea>
                            </div>
                        </div>

                        <div style="text-align: right;">
                            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                            <button type="submit" class="btn btn-primary" style="margin-left: 10px;">提交申请</button>
                        </div>
                    </form>
                </div>
            `;

            this.showModal('发票申请', invoiceFormHtml);

            // 绑定表单提交事件
            document.getElementById('invoice-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                const invoiceData = {
                    orderId: orderId,
                    orderNumber: order.orderNumber || '待生成',
                    customerName: order.customerName,
                    invoiceType: formData.get('invoiceType'),
                    invoiceAmount: parseFloat(formData.get('invoiceAmount')),
                    invoiceTitle: formData.get('invoiceTitle'),
                    taxNumber: formData.get('taxNumber'),
                    notes: formData.get('notes'),
                    applicationDate: new Date().toISOString().split('T')[0],
                    status: '待处理'
                };

                // 这里可以保存发票申请数据到数据库
                console.log('发票申请数据:', invoiceData);
                alert('发票申请提交成功！');
                document.querySelector('.modal-overlay').remove();
            });

        } catch (error) {
            console.error('发票申请失败:', error);
            alert('发票申请失败: ' + error.message);
        }
    },

    // 显示发票申请
    showInvoiceApplication: function(orderId) {
        // 这里可以实现发票申请的具体逻辑
        alert('发票申请功能开发中...\n订单ID: ' + orderId);
    },

    // 显示库存表单
    showInventoryForm: async function(editId = null) {
        try {
            const carModels = await window.dbFunctions.getAllCarModels();

            let inventory = {
                serialNumber: '',
                inventoryStatus: '可售',
                carModel: '',
                version: '',
                vin: '',
                exteriorColor: '',
                interiorColor: '',
                factoryOptions: '',
                standard: '',
                location: '',
                productionDate: '',
                shipmentDate: '',
                stockDate: new Date().toISOString().split('T')[0],
                stockAge: 0,
                guidePrice: '',
                notes: ''
            };

            if (editId !== null) {
                inventory = await window.dbFunctions.getInventoryManagementById(editId) || inventory;
            }

            this.showModal('库存管理', this.renderInventoryForm(inventory, carModels, editId));
        } catch (error) {
            console.error('显示库存表单失败:', error);
            alert('显示库存表单失败: ' + error.message);
        }
    },

    // 显示目标表单
    showTargetForm: function(type) {
        const targetData = {
            yearMonth: new Date().toISOString().slice(0, 7),
            target: '',
            actual: '',
            completionRate: 0
        };

        this.showModal(`${type}目标管理`, this.renderTargetForm(targetData, type));
    },

    // 显示模态框
    showModal: function(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    },

    // 渲染订单表单
    renderOrderForm: function(order, salesAdvisors, carModels, highEndParts, editId) {
        return `
            <form id="order-form" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="form-group">
                    <label>订单号</label>
                    <input type="text" name="serialNumber" value="${order.serialNumber || ''}" placeholder="自动生成">
                </div>
                ${editId !== null ? `
                <div class="form-group">
                    <label>审核状态</label>
                    ${order.auditStatus === '驳回' ? `
                        <input type="text" name="auditStatus" value="待审核" readonly style="background-color: #f8f9fa;">
                        <small style="color: #6c757d; display: block; margin-top: 5px;">被拒绝的订单重新提交后将自动设为待审核状态</small>
                    ` : `
                        <select name="auditStatus">
                            <option value="待审核" ${order.auditStatus === '待审核' ? 'selected' : ''}>待审核</option>
                            <option value="已审核" ${order.auditStatus === '已审核' ? 'selected' : ''}>已审核</option>
                            <option value="驳回" ${order.auditStatus === '驳回' ? 'selected' : ''}>驳回</option>
                        </select>
                    `}
                </div>
                ` : ''}
                <div class="form-group">
                    <label>订单日期 *</label>
                    <input type="date" name="orderDate" value="${order.orderDate}" required>
                </div>
                <div class="form-group">
                    <label>客户名称 *</label>
                    <input type="text" name="customerName" value="${order.customerName}" required>
                </div>
                <div class="form-group">
                    <label>身份证号码 *</label>
                    <input type="text" name="idNumber" value="${order.idNumber || ''}" pattern="[0-9X]{18}" required placeholder="18位身份证号码" maxlength="18">
                </div>
                <div class="form-group">
                    <label>联系手机 *</label>
                    <input type="tel" name="phone1" value="${order.phone1}" pattern="[0-9]{11}" required placeholder="11位手机号">
                </div>
                <div class="form-group">
                    <label>联系地址</label>
                    <textarea name="address" rows="3" placeholder="请输入联系地址">${order.address || ''}</textarea>
                </div>
                <div class="form-group">
                    <label>销售顾问</label>
                    <select name="salesAdvisor">
                        <option value="">请选择销售顾问</option>
                        ${salesAdvisors.map(advisor => `<option value="${advisor.name}" ${order.salesAdvisor === advisor.name ? 'selected' : ''}>${advisor.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>VIN码</label>
                    <input type="text" name="vin" value="${order.vin}" maxlength="17">
                </div>
                <div class="form-group">
                    <label>车型</label>
                    <select name="carModel">
                        <option value="">请选择车型</option>
                        ${carModels.map(model => `<option value="${model.name}" ${order.carModel === model.name ? 'selected' : ''}>${model.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>配置</label>
                    <input type="text" name="configuration" value="${order.configuration}">
                </div>
                <div class="form-group">
                    <label>外色</label>
                    <input type="text" name="exteriorColor" value="${order.exteriorColor}">
                </div>
                <div class="form-group">
                    <label>内饰</label>
                    <input type="text" name="interiorColor" value="${order.interiorColor}">
                </div>
                <div class="form-group" style="grid-column: 1 / -1;">
                    <label>选装件</label>
                    <div style="border: 1px solid #ddd; border-radius: 4px; padding: 10px;">
                        <table class="data-table" id="options-table" style="margin: 0;">
                            <thead>
                                <tr style="background-color: #f8f9fa;">
                                    <th style="width: 60px; text-align: center;">序号</th>
                                    <th>配件名称</th>
                                    <th style="width: 120px; text-align: right;">销售价</th>
                                    <th style="width: 100px; text-align: center;">数量</th>
                                    <th style="width: 80px; text-align: center;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="options-tbody">
                                ${this.renderExistingOptions(order.options || '')}
                            </tbody>
                        </table>
                        <div style="margin-top: 10px; text-align: center;">
                            <button type="button" class="btn btn-sm btn-primary" onclick="orderFunctions.addOptionRow()">
                                <i class="fas fa-plus"></i> 添加选装件
                            </button>
                        </div>
                    </div>
                    <small style="color: #6c757d; margin-top: 5px; display: block;">
                        <i class="fas fa-info-circle"></i> 可从精品配件列表选择或手动输入配件名称，支持动态添加和删除
                    </small>
                </div>
                <div class="form-group">
                    <label>交付日期</label>
                    <input type="date" name="deliveryDate" value="${order.deliveryDate}">
                </div>
                <div class="form-group">
                    <label>定金</label>
                    <input type="number" name="deposit" value="${order.deposit || ''}" step="0.01" placeholder="请输入定金金额">
                </div>
                <div class="form-group">
                    <label>合同价</label>
                    <input type="number" name="contractPrice" value="${order.contractPrice || ''}" step="0.01" placeholder="请输入合同价格">
                </div>
                <div class="form-actions" style="grid-column: 1 / -1; margin-top: 20px;">
                    <button type="button" class="btn btn-primary" onclick="orderFunctions.saveOrder(${editId})">
                        <i class="fas fa-save"></i> 提交审核
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </form>
        `;
    },

    // 渲染库存表单
    renderInventoryForm: function(inventory, carModels, editId) {
        return `
            <form id="inventory-form" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="form-group">
                    <label>订单号</label>
                    <input type="text" name="serialNumber" value="${inventory.serialNumber || ''}" placeholder="自动生成">
                </div>
                <div class="form-group">
                    <label>车型 *</label>
                    <select name="carModel" required>
                        <option value="">请选择车型</option>
                        ${carModels.map(model => `<option value="${model.name}" ${inventory.carModel === model.name ? 'selected' : ''}>${model.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label>版本</label>
                    <input type="text" name="version" value="${inventory.version || ''}">
                </div>
                <div class="form-group">
                    <label>车架号</label>
                    <input type="text" name="vin" value="${inventory.vin || ''}" maxlength="17">
                </div>
                <div class="form-group">
                    <label>外色</label>
                    <input type="text" name="exteriorColor" value="${inventory.exteriorColor || ''}">
                </div>
                <div class="form-group">
                    <label>内饰</label>
                    <input type="text" name="interiorColor" value="${inventory.interiorColor || ''}">
                </div>
                <div class="form-group">
                    <label>原厂选装</label>
                    <input type="text" name="factoryOptions" value="${inventory.factoryOptions || ''}">
                </div>
                <div class="form-group">
                    <label>标准</label>
                    <input type="text" name="standard" value="${inventory.standard || ''}">
                </div>
                <div class="form-group">
                    <label>位置</label>
                    <input type="text" name="location" value="${inventory.location || ''}">
                </div>
                <div class="form-group">
                    <label>生产日期</label>
                    <input type="date" name="productionDate" value="${inventory.productionDate || ''}">
                </div>
                <div class="form-group">
                    <label>发运日期</label>
                    <input type="date" name="shipmentDate" value="${inventory.shipmentDate || ''}">
                </div>
                <div class="form-group">
                    <label>入库日期 *</label>
                    <input type="date" name="stockDate" value="${inventory.stockDate}" required onchange="orderFunctions.calculateStockAge()">
                </div>
                <div class="form-group">
                    <label>库龄（天）</label>
                    <input type="number" name="stockAge" value="${inventory.stockAge || 0}" readonly>
                </div>
                <div class="form-group">
                    <label>指导价</label>
                    <input type="number" name="guidePrice" value="${inventory.guidePrice || ''}" step="0.01">
                </div>
                <div class="form-group" style="grid-column: 1 / -1;">
                    <label>备注</label>
                    <textarea name="notes" rows="3">${inventory.notes || ''}</textarea>
                </div>
                <div class="form-actions" style="grid-column: 1 / -1; margin-top: 20px;">
                    <button type="button" class="btn btn-primary" onclick="orderFunctions.saveInventory(${editId})">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </form>
        `;
    },

    // 渲染目标表单
    renderTargetForm: function(target, type) {
        const typeNames = {
            'delivery': '提车',
            'order': '订单',
            'retail': '零售'
        };

        return `
            <form id="target-form">
                <div class="form-group">
                    <label>年月 *</label>
                    <input type="month" name="yearMonth" value="${target.yearMonth}" required>
                </div>
                <div class="form-group">
                    <label>${typeNames[type]}目标 *</label>
                    <input type="number" name="target" value="${target.target}" required min="0" onchange="orderFunctions.calculateCompletionRate()">
                </div>
                <div class="form-group">
                    <label>实际完成</label>
                    <input type="number" name="actual" value="${target.actual}" min="0" onchange="orderFunctions.calculateCompletionRate()">
                </div>
                <div class="form-group">
                    <label>完成率（%）</label>
                    <input type="number" name="completionRate" value="${target.completionRate}" readonly step="0.01">
                </div>
                <div class="form-actions" style="margin-top: 20px;">
                    <button type="button" class="btn btn-primary" onclick="orderFunctions.saveTarget('${type}')">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </form>
        `;
    },

    // 保存订单
    saveOrder: async function(editId) {
        try {
            const form = document.getElementById('order-form');
            const formData = new FormData(form);
            const orderData = {};

            for (let [key, value] of formData.entries()) {
                orderData[key] = value;
            }

            // 处理选装件数据
            const selectedOptions = [];
            const optionRows = form.querySelectorAll('#options-tbody tr');

            optionRows.forEach(row => {
                const partSelect = row.querySelector('.option-part-select');
                const customNameInput = row.querySelector('.custom-part-name');
                const quantityInput = row.querySelector('.option-quantity');

                let partName = '';
                if (partSelect.value === 'custom' && customNameInput) {
                    partName = customNameInput.value.trim();
                } else if (partSelect.value && partSelect.value !== 'custom') {
                    partName = partSelect.value;
                }

                const quantity = parseInt(quantityInput.value) || 1;

                if (partName) {
                    // 检查是否重复
                    const isDuplicate = selectedOptions.some(option => {
                        const existingPartName = option.includes(' x') ? option.split(' x')[0] : option;
                        return existingPartName === partName;
                    });

                    if (!isDuplicate) {
                        if (quantity > 1) {
                            selectedOptions.push(`${partName} x${quantity}`);
                        } else {
                            selectedOptions.push(partName);
                        }
                    }
                }
            });

            orderData.options = selectedOptions.join(', ');

            // 验证必填字段
            if (!orderData.customerName || !orderData.idNumber || !orderData.phone1 || !orderData.orderDate) {
                alert('请填写必填字段（客户名称、身份证号码、联系手机、订单日期）');
                return;
            }

            // 验证身份证号码格式
            const idRegex = /^[0-9]{17}[0-9X]$/;
            if (!idRegex.test(orderData.idNumber)) {
                alert('身份证号码格式不正确，请输入18位身份证号码');
                return;
            }

            // 验证手机号格式
            const phoneRegex = /^[0-9]{11}$/;
            if (!phoneRegex.test(orderData.phone1)) {
                alert('联系手机格式不正确');
                return;
            }

            // 设置默认状态
            if (!editId) {
                orderData.auditStatus = '待审核';
            } else {
                // 编辑模式下，如果原状态是"驳回"，则重新设为"待审核"
                const originalOrder = await window.dbFunctions.getOrderManagementById(editId);
                if (originalOrder && originalOrder.auditStatus === '驳回') {
                    orderData.auditStatus = '待审核';
                }
            }

            if (editId !== null) {
                // 更新现有订单
                await window.dbFunctions.updateOrderManagement(editId, orderData);
                alert('订单更新成功');
            } else {
                // 新增订单，生成订单号
                const tempOrder = { ...orderData, id: Date.now() }; // 临时ID用于生成订单号
                const orderNumber = await this.ensureUniqueOrderNumber(tempOrder);
                orderData.orderNumber = orderNumber;

                await window.dbFunctions.addOrderManagement(orderData);
                alert('订单提交审核成功，订单号：' + orderNumber);
            }

            // 关闭模态框
            document.querySelector('.modal-overlay').remove();

            // 重新加载数据
            this.allOrders = await window.dbFunctions.getAllOrderManagement();
            this.switchTab('orders');

        } catch (error) {
            console.error('保存订单失败:', error);
            alert('保存订单失败: ' + error.message);
        }
    },

    // 计算库龄
    calculateStockAge: function() {
        const stockDate = document.querySelector('input[name="stockDate"]').value;
        if (stockDate) {
            const today = new Date();
            const stock = new Date(stockDate);
            const diffTime = Math.abs(today - stock);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            document.querySelector('input[name="stockAge"]').value = diffDays;
        }
    },

    // 计算完成率
    calculateCompletionRate: function() {
        const target = parseFloat(document.querySelector('input[name="target"]').value) || 0;
        const actual = parseFloat(document.querySelector('input[name="actual"]').value) || 0;

        if (target > 0) {
            const completionRate = Math.floor(actual / target * 100);
            document.querySelector('input[name="completionRate"]').value = completionRate;
        }
    },

    // 渲染已有选装件
    renderExistingOptions: function(optionsString) {
        if (!optionsString) return '';

        const options = optionsString.split(',').map(opt => opt.trim()).filter(opt => opt);
        return options.map((option, index) => {
            // 解析配件名称和数量
            let partName = option;
            let quantity = 1;

            if (option.includes(' x')) {
                const parts = option.split(' x');
                partName = parts[0].trim();
                quantity = parseInt(parts[1]) || 1;
            }

            return this.renderOptionRow(index + 1, partName, '', quantity);
        }).join('');
    },

    // 渲染选装件行
    renderOptionRow: function(index, partName = '', salePrice = '', quantity = 1) {
        return `
            <tr data-option-index="${index}">
                <td style="text-align: center; padding: 8px;">${index}</td>
                <td style="padding: 8px;">
                    <select class="option-part-select" style="width: 100%; padding: 4px;" onchange="orderFunctions.updatePartPrice(this)">
                        <option value="">请选择配件</option>
                        ${this.availableParts ? this.availableParts.map(part =>
                            `<option value="${part.partName}" data-price="${part.salePrice || part.guidePrice || 0}" ${part.partName === partName ? 'selected' : ''}>${part.partName}</option>`
                        ).join('') : ''}
                        <option value="custom" ${!this.availableParts || !this.availableParts.find(p => p.partName === partName) ? 'selected' : ''}>手动输入</option>
                    </select>
                    ${(!this.availableParts || !this.availableParts.find(p => p.partName === partName)) && partName ? `
                        <input type="text" class="custom-part-name" value="${partName}" placeholder="请输入配件名称" style="width: 100%; margin-top: 5px; padding: 4px;">
                    ` : ''}
                </td>
                <td style="text-align: right; padding: 8px;">
                    <span class="part-price" style="color: #28a745; font-weight: 500;">¥${salePrice || '0'}</span>
                </td>
                <td style="text-align: center; padding: 8px;">
                    <input type="number" class="option-quantity" value="${quantity}" min="1" max="99"
                           style="width: 60px; padding: 4px; text-align: center;">
                </td>
                <td style="text-align: center; padding: 8px;">
                    <button type="button" class="btn btn-sm btn-danger" onclick="orderFunctions.removeOptionRow(this)" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    },

    // 添加选装件行
    addOptionRow: function() {
        const tbody = document.getElementById('options-tbody');
        const currentRows = tbody.querySelectorAll('tr').length;
        const newIndex = currentRows + 1;

        tbody.insertAdjacentHTML('beforeend', this.renderOptionRow(newIndex));
        this.updateOptionIndexes();
    },

    // 删除选装件行
    removeOptionRow: function(button) {
        const row = button.closest('tr');
        row.remove();
        this.updateOptionIndexes();
    },

    // 更新选装件序号
    updateOptionIndexes: function() {
        const tbody = document.getElementById('options-tbody');
        const rows = tbody.querySelectorAll('tr');

        rows.forEach((row, index) => {
            const indexCell = row.querySelector('td:first-child');
            indexCell.textContent = index + 1;
            row.setAttribute('data-option-index', index + 1);
        });
    },

    // 更新配件价格
    updatePartPrice: function(select) {
        const row = select.closest('tr');
        const priceSpan = row.querySelector('.part-price');
        const customNameContainer = row.querySelector('.custom-part-name');

        if (select.value === 'custom') {
            // 显示手动输入框
            if (!customNameContainer) {
                select.insertAdjacentHTML('afterend', `
                    <input type="text" class="custom-part-name" placeholder="请输入配件名称" style="width: 100%; margin-top: 5px; padding: 4px;">
                `);
            }
            priceSpan.textContent = '¥0';
        } else {
            // 移除手动输入框
            if (customNameContainer) {
                customNameContainer.remove();
            }

            // 更新价格
            const selectedOption = select.options[select.selectedIndex];
            const price = selectedOption.getAttribute('data-price') || '0';
            priceSpan.textContent = '¥' + price;
        }
    },

    // 显示发票申请
    showInvoiceApplication: async function(orderId) {
        try {
            const order = await window.dbFunctions.getOrderManagementById(orderId);
            if (!order) {
                alert('订单不存在');
                return;
            }

            const modalContent = `
                <div class="invoice-application">
                    <h3><i class="fas fa-file-invoice"></i> 发票申请</h3>
                    <div style="margin: 20px 0;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                            <h4 style="margin-bottom: 10px;">订单信息</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                                <div><strong>订单号：</strong>${order.serialNumber || '-'}</div>
                                <div><strong>客户名称：</strong>${order.customerName || '-'}</div>
                                <div><strong>车型：</strong>${order.carModel || '-'}</div>
                                <div><strong>合同价：</strong>${order.contractPrice ? '¥' + order.contractPrice : '-'}</div>
                            </div>
                        </div>

                        <form id="invoice-form" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div class="form-group">
                                <label>发票类型 *</label>
                                <select name="invoiceType" required>
                                    <option value="">请选择发票类型</option>
                                    <option value="增值税专用发票">增值税专用发票</option>
                                    <option value="增值税普通发票">增值税普通发票</option>
                                    <option value="机动车销售统一发票">机动车销售统一发票</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>发票抬头 *</label>
                                <input type="text" name="invoiceTitle" value="${order.customerName || ''}" required>
                            </div>
                            <div class="form-group">
                                <label>纳税人识别号</label>
                                <input type="text" name="taxNumber" placeholder="企业发票必填">
                            </div>
                            <div class="form-group">
                                <label>发票金额 *</label>
                                <input type="number" name="invoiceAmount" value="${order.contractPrice || ''}" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label>开票日期 *</label>
                                <input type="date" name="invoiceDate" value="${new Date().toISOString().split('T')[0]}" required>
                            </div>
                            <div class="form-group">
                                <label>联系电话</label>
                                <input type="text" name="contactPhone" value="${order.phone1 || ''}">
                            </div>
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label>开票地址</label>
                                <input type="text" name="invoiceAddress" placeholder="请输入开票地址">
                            </div>
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label>备注</label>
                                <textarea name="invoiceNotes" rows="3" placeholder="请输入备注信息"></textarea>
                            </div>
                        </form>
                    </div>
                    <div style="text-align: right;">
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button class="btn btn-primary" onclick="orderFunctions.submitInvoiceApplication(${orderId})" style="margin-left: 10px;">提交申请</button>
                    </div>
                </div>
            `;

            this.showModal('发票申请', modalContent);
        } catch (error) {
            console.error('显示发票申请失败:', error);
            alert('显示发票申请失败: ' + error.message);
        }
    },

    // 提交发票申请
    submitInvoiceApplication: async function(orderId) {
        try {
            const form = document.getElementById('invoice-form');
            const formData = new FormData(form);
            const invoiceData = {};

            for (let [key, value] of formData.entries()) {
                invoiceData[key] = value;
            }

            // 验证必填字段
            if (!invoiceData.invoiceType || !invoiceData.invoiceTitle || !invoiceData.invoiceAmount || !invoiceData.invoiceDate) {
                alert('请填写必填字段');
                return;
            }

            // 验证企业发票的纳税人识别号
            if (invoiceData.invoiceType === '增值税专用发票' && !invoiceData.taxNumber) {
                alert('增值税专用发票需要填写纳税人识别号');
                return;
            }

            // 添加订单信息
            invoiceData.orderId = orderId;
            invoiceData.applicationDate = new Date().toISOString().split('T')[0];
            invoiceData.status = '待处理';

            // 这里可以保存到数据库或发送到后端
            console.log('发票申请数据:', invoiceData);

            alert('发票申请提交成功！我们将在3-5个工作日内处理您的申请。');
            const modal = document.querySelector('.modal-overlay');
            if (modal) modal.remove();

        } catch (error) {
            console.error('提交发票申请失败:', error);
            alert('提交发票申请失败: ' + error.message);
        }
    },

    // 中文姓名转拼音首字母缩写
    chineseToPinyin: function(chinese) {
        // 中文字符到拼音首字母的映射表（常见姓名字符）
        const pinyinFirstLetterMap = {
            // 常见姓氏（按拼音首字母排序）
            // A-B
            '安': 'A', '艾': 'A', '敖': 'A', '鲍': 'B', '包': 'B', '白': 'B', '毕': 'B', '卜': 'B', '卞': 'B', '边': 'B', '薄': 'B',
            // C-D
            '陈': 'C', '曹': 'C', '蔡': 'C', '崔': 'C', '程': 'C', '常': 'C', '褚': 'C', '车': 'C', '成': 'C', '池': 'C', '楚': 'C',
            '丁': 'D', '邓': 'D', '杜': 'D', '董': 'D', '戴': 'D', '段': 'D', '窦': 'D', '邸': 'D', '刁': 'D', '狄': 'D', '杜': 'D',
            // F-G
            '冯': 'F', '付': 'F', '方': 'F', '范': 'F', '费': 'F', '傅': 'F', '樊': 'F', '封': 'F', '房': 'F', '丰': 'F', '凤': 'F',
            '郭': 'G', '高': 'G', '顾': 'G', '龚': 'G', '关': 'G', '甘': 'G', '葛': 'G', '耿': 'G', '宫': 'G', '巩': 'G', '古': 'G',
            // H-J
            '黄': 'H', '何': 'H', '胡': 'H', '韩': 'H', '侯': 'H', '贺': 'H', '郝': 'H', '洪': 'H', '华': 'H', '霍': 'H', '花': 'H',
            '江': 'J', '金': 'J', '姜': 'J', '蒋': 'J', '贾': 'J', '纪': 'J', '季': 'J', '焦': 'J', '靳': 'J', '景': 'J', '居': 'J',
            // K-L
            '孔': 'K', '康': 'K', '柯': 'K', '匡': 'K', '况': 'K', '邝': 'K', '孔': 'K', '寇': 'K', '库': 'K', '蒯': 'K', '阚': 'K',
            '李': 'L', '刘': 'L', '林': 'L', '梁': 'L', '罗': 'L', '陆': 'L', '卢': 'L', '吕': 'L', '廖': 'L', '雷': 'L', '黎': 'L',
            '龙': 'L', '娄': 'L', '柳': 'L', '路': 'L', '栗': 'L', '连': 'L', '蓝': 'L', '兰': 'L', '赖': 'L', '来': 'L', '乐': 'L',
            // M-P
            '马': 'M', '毛': 'M', '孟': 'M', '莫': 'M', '苗': 'M', '穆': 'M', '梅': 'M', '米': 'M', '闵': 'M', '明': 'M', '牟': 'M',
            '倪': 'N', '聂': 'N', '牛': 'N', '宁': 'N', '南': 'N', '年': 'N', '农': 'N', '那': 'N', '能': 'N', '乜': 'N', '钮': 'N',
            '欧': 'O', '欧阳': 'O', '区': 'O', '殴': 'O', '瓯': 'O',
            '潘': 'P', '彭': 'P', '裴': 'P', '皮': 'P', '平': 'P', '蒲': 'P', '濮': 'P', '朴': 'P', '逄': 'P', '盘': 'P', '庞': 'P',

            // Q-Z
            '邱': 'Q', '秦': 'Q', '钱': 'Q', '齐': 'Q', '乔': 'Q', '覃': 'Q', '祁': 'Q', '戚': 'Q', '强': 'Q', '屈': 'Q', '曲': 'Q',
            '任': 'R', '阮': 'R', '饶': 'R', '容': 'R', '荣': 'R', '冉': 'R', '芮': 'R', '汝': 'R', '茹': 'R', '阮': 'R', '瑞': 'R',
            '孙': 'S', '沈': 'S', '宋': 'S', '苏': 'S', '石': 'S', '史': 'S', '邵': 'S', '施': 'S', '司': 'S', '申': 'S', '盛': 'S',
            '唐': 'T', '田': 'T', '陶': 'T', '谭': 'T', '汤': 'T', '滕': 'T', '童': 'T', '涂': 'T', '屠': 'T', '谈': 'T', '台': 'T',
            '万': 'W', '王': 'W', '吴': 'W', '魏': 'W', '汪': 'W', '武': 'W', '韦': 'W', '温': 'W', '文': 'W', '翁': 'W', '邬': 'W',
            '许': 'X', '谢': 'X', '徐': 'X', '薛': 'X', '萧': 'X', '夏': 'X', '熊': 'X', '向': 'X', '邢': 'X', '辛': 'X', '席': 'X',
            '杨': 'Y', '叶': 'Y', '于': 'Y', '袁': 'Y', '姚': 'Y', '余': 'Y', '尹': 'Y', '闫': 'Y', '严': 'Y', '殷': 'Y', '易': 'Y',
            '张': 'Z', '赵': 'Z', '周': 'Z', '朱': 'Z', '郑': 'Z', '钟': 'Z', '曾': 'Z', '左': 'Z', '邹': 'Z', '庄': 'Z', '卓': 'Z',

            // 常见名字字符（按拼音首字母排序）
            // 数字
            '一': 'Y', '二': 'E', '三': 'S', '四': 'S', '五': 'W', '六': 'L', '七': 'Q', '八': 'B', '九': 'J', '十': 'S',

            // A-B
            '安': 'A', '爱': 'A', '奥': 'A', '昂': 'A', '傲': 'A', '澳': 'A', '艾': 'A', '阿': 'A', '啊': 'A', '按': 'A',
            '斌': 'B', '彬': 'B', '宾': 'B', '冰': 'B', '兵': 'B', '波': 'B', '博': 'B', '柏': 'B', '白': 'B', '百': 'B',
            '宝': 'B', '保': 'B', '北': 'B', '贝': 'B', '本': 'B', '奔': 'B', '碧': 'B', '标': 'B', '彪': 'B', '表': 'B',

            // C-D
            '超': 'C', '成': 'C', '诚': 'C', '城': 'C', '晨': 'C', '辰': 'C', '春': 'C', '纯': 'C', '聪': 'C', '翠': 'C',
            '才': 'C', '财': 'C', '彩': 'C', '草': 'C', '策': 'C', '昌': 'C', '常': 'C', '长': 'C', '畅': 'C', '朝': 'C',
            '德': 'D', '东': 'D', '冬': 'D', '栋': 'D', '丹': 'D', '旦': 'D', '道': 'D', '达': 'D', '大': 'D', '代': 'D',
            '地': 'D', '第': 'D', '典': 'D', '点': 'D', '电': 'D', '定': 'D', '鼎': 'D', '顶': 'D', '丁': 'D', '冬': 'D',

            // E-F
            '恩': 'E', '儿': 'E', '尔': 'E', '二': 'E', '峨': 'E', '娥': 'E', '额': 'E', '鹅': 'E', '俄': 'E', '饿': 'E',
            '芳': 'F', '方': 'F', '飞': 'F', '非': 'F', '菲': 'F', '斐': 'F', '丰': 'F', '风': 'F', '峰': 'F', '锋': 'F',
            '福': 'F', '富': 'F', '复': 'F', '凤': 'F', '奋': 'F', '发': 'F', '法': 'F', '帆': 'F', '范': 'F', '繁': 'F',

            // G-H
            '国': 'G', '光': 'G', '广': 'G', '贵': 'G', '桂': 'G', '功': 'G', '公': 'G', '刚': 'G', '钢': 'G', '高': 'G',
            '歌': 'G', '格': 'G', '根': 'G', '更': 'G', '耕': 'G', '工': 'G', '弓': 'G', '宫': 'G', '共': 'G', '古': 'G',
            '华': 'H', '花': 'H', '辉': 'H', '慧': 'H', '惠': 'H', '会': 'H', '海': 'H', '涵': 'H', '寒': 'H', '汉': 'H',
            '和': 'H', '河': 'H', '何': 'H', '贺': 'H', '鹤': 'H', '红': 'H', '宏': 'H', '洪': 'H', '虹': 'H', '鸿': 'H',
            '火': 'H', '活': 'H', '霍': 'H', '霞': 'H', '霖': 'H', '霏': 'H', '黑': 'H', '恒': 'H', '横': 'H', '衡': 'H',
            '航': 'H', '杭': 'H', '行': 'H', '号': 'H', '好': 'H', '豪': 'H', '浩': 'H', '皓': 'H', '昊': 'H', '颢': 'H',

            // J-L
            '杰': 'J', '健': 'J', '建': 'J', '江': 'J', '金': 'J', '锦': 'J', '进': 'J', '晶': 'J', '静': 'J', '敬': 'J',
            '军': 'J', '君': 'J', '俊': 'J', '骏': 'J', '嘉': 'J', '佳': 'J', '家': 'J', '加': 'J', '甲': 'J', '价': 'J',
            '吉': 'J', '极': 'J', '及': 'J', '级': 'J', '即': 'J', '急': 'J', '集': 'J', '几': 'J', '机': 'J', '基': 'J',
            '菊': 'J', '举': 'J', '巨': 'J', '具': 'J', '剧': 'J', '据': 'J', '聚': 'J', '决': 'J', '绝': 'J', '觉': 'J',

            '康': 'K', '凯': 'K', '开': 'K', '科': 'K', '可': 'K', '克': 'K', '客': 'K', '课': 'K', '快': 'K', '宽': 'K',
            '昆': 'K', '坤': 'K', '困': 'K', '扩': 'K', '阔': 'K', '空': 'K', '孔': 'K', '控': 'K', '口': 'K', '苦': 'K',

            '丽': 'L', '利': 'L', '力': 'L', '立': 'L', '理': 'L', '礼': 'L', '李': 'L', '里': 'L', '离': 'L', '黎': 'L',
            '磊': 'L', '雷': 'L', '类': 'L', '累': 'L', '冷': 'L', '愣': 'L', '厘': 'L', '梨': 'L', '犁': 'L', '黎': 'L',
            '林': 'L', '临': 'L', '邻': 'L', '淋': 'L', '琳': 'L', '霖': 'L', '麟': 'L', '鳞': 'L', '凛': 'L', '吝': 'L',
            '龙': 'L', '隆': 'L', '陇': 'L', '垄': 'L', '拢': 'L', '楼': 'L', '娄': 'L', '搂': 'L', '篓': 'L', '漏': 'L',
            '兰': 'L', '蓝': 'L', '览': 'L', '懒': 'L', '烂': 'L', '滥': 'L', '郎': 'L', '朗': 'L', '浪': 'L', '捞': 'L',
            '洁': 'J', '结': 'J', '节': 'J', '杰': 'J', '接': 'J', '街': 'J', '阶': 'J', '皆': 'J', '揭': 'J', '借': 'J',

            // M-N
            '明': 'M', '民': 'M', '敏': 'M', '名': 'M', '命': 'M', '鸣': 'M', '铭': 'M', '茗': 'M', '冥': 'M', '溟': 'M',
            '美': 'M', '梅': 'M', '媒': 'M', '煤': 'M', '没': 'M', '每': 'M', '妹': 'M', '魅': 'M', '门': 'M', '们': 'M',
            '木': 'M', '目': 'M', '牧': 'M', '墓': 'M', '幕': 'M', '慕': 'M', '母': 'M', '亩': 'M', '牡': 'M', '拇': 'M',
            '梦': 'M', '蒙': 'M', '猛': 'M', '孟': 'M', '迷': 'M', '米': 'M', '密': 'M', '蜜': 'M', '秘': 'M', '觅': 'M',

            '娜': 'N', '那': 'N', '哪': 'N', '纳': 'N', '拿': 'N', '南': 'N', '男': 'N', '难': 'N', '囊': 'N', '挠': 'N',
            '宁': 'N', '凝': 'N', '牛': 'N', '纽': 'N', '农': 'N', '浓': 'N', '弄': 'N', '奴': 'N', '努': 'N', '怒': 'N',
            '年': 'N', '念': 'N', '娘': 'N', '鸟': 'N', '尿': 'N', '捏': 'N', '聂': 'N', '孽': 'N', '您': 'N', '柠': 'N',

            // P-R
            '平': 'P', '萍': 'P', '苹': 'P', '屏': 'P', '乒': 'P', '坪': 'P', '评': 'P', '凭': 'P', '瓶': 'P', '品': 'P',
            '鹏': 'P', '朋': 'P', '彭': 'P', '蓬': 'P', '棚': 'P', '硼': 'P', '篷': 'P', '膨': 'P', '澎': 'P', '捧': 'P',

            '奇': 'Q', '齐': 'Q', '其': 'Q', '起': 'Q', '气': 'Q', '器': 'Q', '千': 'Q', '前': 'Q', '钱': 'Q', '潜': 'Q',
            '强': 'Q', '墙': 'Q', '抢': 'Q', '枪': 'Q', '腔': 'Q', '羌': 'Q', '蔷': 'Q', '樯': 'Q', '戗': 'Q', '炝': 'Q',
            '青': 'Q', '轻': 'Q', '清': 'Q', '情': 'Q', '晴': 'Q', '氰': 'Q', '倾': 'Q', '卿': 'Q', '擎': 'Q', '琼': 'Q',
            '秋': 'Q', '求': 'Q', '球': 'Q', '区': 'Q', '曲': 'Q', '取': 'Q', '去': 'Q', '趣': 'Q', '圈': 'Q', '全': 'Q',

            '人': 'R', '仁': 'R', '任': 'R', '认': 'R', '忍': 'R', '韧': 'R', '刃': 'R', '润': 'R', '闰': 'R', '若': 'R',
            '瑞': 'R', '锐': 'R', '睿': 'R', '芮': 'R', '蕊': 'R', '蚋': 'R', '软': 'R', '阮': 'R', '蠕': 'R', '儒': 'R',
            '如': 'R', '茹': 'R', '汝': 'R', '乳': 'R', '辱': 'R', '入': 'R', '褥': 'R', '软': 'R', '锐': 'R', '瑞': 'R',

            // S-T
            '山': 'S', '水': 'S', '石': 'S', '树': 'S', '森': 'S', '松': 'S', '顺': 'S', '思': 'S', '四': 'S', '丝': 'S',
            '三': 'S', '散': 'S', '桑': 'S', '嗓': 'S', '搔': 'S', '骚': 'S', '扫': 'S', '嫂': 'S', '色': 'S', '涩': 'S',
            '深': 'S', '神': 'S', '什': 'S', '身': 'S', '呻': 'S', '伸': 'S', '申': 'S', '绅': 'S', '审': 'S', '婶': 'S',
            '生': 'S', '声': 'S', '胜': 'S', '圣': 'S', '师': 'S', '失': 'S', '狮': 'S', '施': 'S', '湿': 'S', '诗': 'S',
            '十': 'S', '石': 'S', '拾': 'S', '时': 'S', '什': 'S', '食': 'S', '蚀': 'S', '实': 'S', '识': 'S', '史': 'S',
            '世': 'S', '势': 'S', '示': 'S', '士': 'S', '氏': 'S', '市': 'S', '恃': 'S', '室': 'S', '视': 'S', '似': 'S',
            '手': 'S', '首': 'S', '守': 'S', '寿': 'S', '授': 'S', '售': 'S', '受': 'S', '兽': 'S', '蔬': 'S', '书': 'S',
            '舒': 'S', '淑': 'S', '疏': 'S', '束': 'S', '述': 'S', '树': 'S', '竖': 'S', '数': 'S', '漱': 'S', '恕': 'S',
            '双': 'S', '爽': 'S', '谁': 'S', '水': 'S', '睡': 'S', '税': 'S', '吮': 'S', '瞬': 'S', '顺': 'S', '说': 'S',
            '朔': 'S', '烁': 'S', '斯': 'S', '撕': 'S', '嘶': 'S', '思': 'S', '私': 'S', '司': 'S', '丝': 'S', '死': 'S',
            '松': 'S', '耸': 'S', '怂': 'S', '颂': 'S', '送': 'S', '宋': 'S', '讼': 'S', '诵': 'S', '搜': 'S', '艘': 'S',
            '苏': 'S', '酥': 'S', '俗': 'S', '素': 'S', '速': 'S', '粟': 'S', '僳': 'S', '塑': 'S', '溯': 'S', '宿': 'S',
            '孙': 'S', '损': 'S', '笋': 'S', '蓑': 'S', '梭': 'S', '唆': 'S', '缩': 'S', '琐': 'S', '索': 'S', '锁': 'S',

            '天': 'T', '田': 'T', '甜': 'T', '填': 'T', '挑': 'T', '条': 'T', '跳': 'T', '贴': 'T', '铁': 'T', '听': 'T',
            '庭': 'T', '停': 'T', '亭': 'T', '挺': 'T', '艇': 'T', '通': 'T', '同': 'T', '铜': 'T', '彤': 'T', '童': 'T',
            '突': 'T', '图': 'T', '徒': 'T', '途': 'T', '涂': 'T', '屠': 'T', '土': 'T', '吐': 'T', '兔': 'T', '湍': 'T',
            '团': 'T', '推': 'T', '腿': 'T', '蜕': 'T', '褪': 'T', '退': 'T', '吞': 'T', '屯': 'T', '臀': 'T', '拖': 'T',
            '涛': 'T', '滔': 'T', '绦': 'T', '萄': 'T', '桃': 'T', '逃': 'T', '淘': 'T', '陶': 'T', '讨': 'T', '套': 'T',
            '特': 'T', '藤': 'T', '腾': 'T', '疼': 'T', '誊': 'T', '梯': 'T', '剔': 'T', '踢': 'T', '锑': 'T', '提': 'T',
            '题': 'T', '蹄': 'T', '啼': 'T', '体': 'T', '替': 'T', '嚏': 'T', '惕': 'T', '涕': 'T', '剃': 'T', '屉': 'T',

            // W-Z
            '文': 'W', '武': 'W', '伟': 'W', '维': 'W', '为': 'W', '位': 'W', '味': 'W', '谓': 'W', '尾': 'W', '纬': 'W',
            '未': 'W', '蔚': 'W', '畏': 'W', '胃': 'W', '喂': 'W', '魏': 'W', '温': 'W', '蚊': 'W', '文': 'W', '闻': 'W',
            '纹': 'W', '吻': 'W', '稳': 'W', '紊': 'W', '问': 'W', '嗡': 'W', '翁': 'W', '瓮': 'W', '挝': 'W', '蜗': 'W',
            '涡': 'W', '窝': 'W', '我': 'W', '斡': 'W', '卧': 'W', '握': 'W', '沃': 'W', '巫': 'W', '呜': 'W', '钨': 'W',
            '乌': 'W', '污': 'W', '诬': 'W', '屋': 'W', '无': 'W', '芜': 'W', '梧': 'W', '吾': 'W', '吴': 'W', '毋': 'W',
            '武': 'W', '五': 'W', '捂': 'W', '午': 'W', '舞': 'W', '伍': 'W', '侮': 'W', '坞': 'W', '戊': 'W', '雾': 'W',
            '晤': 'W', '物': 'W', '勿': 'W', '务': 'W', '悟': 'W', '误': 'W', '昔': 'W', '熙': 'W', '析': 'W', '西': 'W',

            '晓': 'X', '小': 'X', '孝': 'X', '校': 'X', '肖': 'X', '消': 'X', '宵': 'X', '淆': 'X', '心': 'X', '新': 'X',
            '薪': 'X', '芯': 'X', '锌': 'X', '欣': 'X', '辛': 'X', '信': 'X', '兴': 'X', '星': 'X', '腥': 'X', '猩': 'X',
            '惺': 'X', '兴': 'X', '刑': 'X', '型': 'X', '形': 'X', '邢': 'X', '行': 'X', '醒': 'X', '幸': 'X', '杏': 'X',
            '性': 'X', '姓': 'X', '兄': 'X', '凶': 'X', '胸': 'X', '匈': 'X', '汹': 'X', '雄': 'X', '熊': 'X', '休': 'X',
            '修': 'X', '羞': 'X', '朽': 'X', '嗅': 'X', '锈': 'X', '秀': 'X', '袖': 'X', '绣': 'X', '墟': 'X', '戌': 'X',
            '需': 'X', '虚': 'X', '嘘': 'X', '须': 'X', '徐': 'X', '许': 'X', '蓄': 'X', '酗': 'X', '叙': 'X', '旭': 'X',
            '序': 'X', '畜': 'X', '恤': 'X', '絮': 'X', '婿': 'X', '绪': 'X', '续': 'X', '轩': 'X', '喧': 'X', '宣': 'X',
            '悬': 'X', '旋': 'X', '玄': 'X', '选': 'X', '癣': 'X', '眩': 'X', '绚': 'X', '靴': 'X', '薛': 'X', '学': 'X',
            '穴': 'X', '雪': 'X', '血': 'X', '勋': 'X', '熏': 'X', '循': 'X', '旬': 'X', '询': 'X', '寻': 'X', '驯': 'X',
            '巡': 'X', '殉': 'X', '汛': 'X', '训': 'X', '讯': 'X', '逊': 'X', '迅': 'X', '压': 'X', '押': 'X', '鸦': 'X',

            '雅': 'Y', '亚': 'Y', '讶': 'Y', '焉': 'Y', '咽': 'Y', '阉': 'Y', '烟': 'Y', '淹': 'Y', '盐': 'Y', '严': 'Y',
            '研': 'Y', '蜒': 'Y', '岩': 'Y', '延': 'Y', '言': 'Y', '颜': 'Y', '阎': 'Y', '炎': 'Y', '沿': 'Y', '奄': 'Y',
            '掩': 'Y', '眼': 'Y', '衍': 'Y', '演': 'Y', '艳': 'Y', '堰': 'Y', '燕': 'Y', '厌': 'Y', '砚': 'Y', '雁': 'Y',
            '唁': 'Y', '彦': 'Y', '焰': 'Y', '宴': 'Y', '谚': 'Y', '验': 'Y', '殃': 'Y', '央': 'Y', '鸯': 'Y', '秧': 'Y',
            '杨': 'Y', '扬': 'Y', '佯': 'Y', '疡': 'Y', '羊': 'Y', '洋': 'Y', '阳': 'Y', '氧': 'Y', '仰': 'Y', '痒': 'Y',
            '养': 'Y', '样': 'Y', '漾': 'Y', '邀': 'Y', '腰': 'Y', '妖': 'Y', '瑶': 'Y', '摇': 'Y', '尧': 'Y', '遥': 'Y',
            '窑': 'Y', '谣': 'Y', '姚': 'Y', '咬': 'Y', '舀': 'Y', '药': 'Y', '要': 'Y', '耀': 'Y', '椰': 'Y', '噎': 'Y',
            '耶': 'Y', '爷': 'Y', '野': 'Y', '冶': 'Y', '也': 'Y', '页': 'Y', '掖': 'Y', '业': 'Y', '叶': 'Y', '曳': 'Y',
            '腋': 'Y', '夜': 'Y', '液': 'Y', '一': 'Y', '壹': 'Y', '医': 'Y', '揖': 'Y', '铱': 'Y', '依': 'Y', '伊': 'Y',
            '衣': 'Y', '颐': 'Y', '夷': 'Y', '遗': 'Y', '移': 'Y', '仪': 'Y', '胰': 'Y', '疑': 'Y', '沂': 'Y', '宜': 'Y',
            '姨': 'Y', '彝': 'Y', '椅': 'Y', '蚁': 'Y', '倚': 'Y', '已': 'Y', '乙': 'Y', '矣': 'Y', '以': 'Y', '艺': 'Y',
            '抑': 'Y', '易': 'Y', '邑': 'Y', '屹': 'Y', '亿': 'Y', '役': 'Y', '臆': 'Y', '逸': 'Y', '肄': 'Y', '疫': 'Y',
            '亦': 'Y', '裔': 'Y', '意': 'Y', '毅': 'Y', '忆': 'Y', '义': 'Y', '益': 'Y', '溢': 'Y', '诣': 'Y', '议': 'Y',
            '谊': 'Y', '译': 'Y', '异': 'Y', '翼': 'Y', '翌': 'Y', '绎': 'Y', '茵': 'Y', '荫': 'Y', '因': 'Y', '殷': 'Y',
            '音': 'Y', '阴': 'Y', '姻': 'Y', '吟': 'Y', '银': 'Y', '淫': 'Y', '寅': 'Y', '饮': 'Y', '尹': 'Y', '引': 'Y',
            '隐': 'Y', '印': 'Y', '英': 'Y', '樱': 'Y', '婴': 'Y', '鹰': 'Y', '应': 'Y', '缨': 'Y', '莹': 'Y', '萤': 'Y',
            '营': 'Y', '荧': 'Y', '蝇': 'Y', '迎': 'Y', '赢': 'Y', '盈': 'Y', '影': 'Y', '颖': 'Y', '硬': 'Y', '映': 'Y',
            '哟': 'Y', '拥': 'Y', '佣': 'Y', '臃': 'Y', '痈': 'Y', '庸': 'Y', '雍': 'Y', '踊': 'Y', '蛹': 'Y', '咏': 'Y',
            '泳': 'Y', '涌': 'Y', '永': 'Y', '恿': 'Y', '勇': 'Y', '用': 'Y', '幽': 'Y', '优': 'Y', '悠': 'Y', '忧': 'Y',
            '尤': 'Y', '由': 'Y', '邮': 'Y', '铀': 'Y', '犹': 'Y', '油': 'Y', '游': 'Y', '酉': 'Y', '有': 'Y', '友': 'Y',
            '右': 'Y', '佑': 'Y', '釉': 'Y', '诱': 'Y', '又': 'Y', '幼': 'Y', '迂': 'Y', '淤': 'Y', '于': 'Y', '盂': 'Y',
            '榆': 'Y', '虞': 'Y', '愚': 'Y', '舆': 'Y', '余': 'Y', '俞': 'Y', '逾': 'Y', '鱼': 'Y', '愉': 'Y', '渝': 'Y',
            '渔': 'Y', '隅': 'Y', '予': 'Y', '娱': 'Y', '雨': 'Y', '与': 'Y', '屿': 'Y', '禹': 'Y', '宇': 'Y', '语': 'Y',
            '羽': 'Y', '玉': 'Y', '域': 'Y', '芋': 'Y', '郁': 'Y', '吁': 'Y', '遇': 'Y', '喻': 'Y', '峪': 'Y', '御': 'Y',
            '愈': 'Y', '欲': 'Y', '狱': 'Y', '育': 'Y', '誉': 'Y', '浴': 'Y', '寓': 'Y', '裕': 'Y', '预': 'Y', '豫': 'Y',
            '驭': 'Y', '鸳': 'Y', '渊': 'Y', '冤': 'Y', '元': 'Y', '垣': 'Y', '袁': 'Y', '原': 'Y', '援': 'Y', '辕': 'Y',
            '园': 'Y', '员': 'Y', '圆': 'Y', '猿': 'Y', '源': 'Y', '缘': 'Y', '远': 'Y', '苑': 'Y', '愿': 'Y', '怨': 'Y',
            '院': 'Y', '曰': 'Y', '约': 'Y', '越': 'Y', '跃': 'Y', '钥': 'Y', '岳': 'Y', '粤': 'Y', '月': 'Y', '悦': 'Y',
            '阅': 'Y', '耘': 'Y', '云': 'Y', '郧': 'Y', '匀': 'Y', '陨': 'Y', '允': 'Y', '运': 'Y', '蕴': 'Y', '酝': 'Y',
            '晕': 'Y', '韵': 'Y', '孕': 'Y', '匝': 'Y', '砸': 'Y', '杂': 'Y', '栽': 'Y', '哉': 'Y', '灾': 'Y', '宰': 'Y',
            '载': 'Y', '再': 'Y', '在': 'Y', '咱': 'Y', '攒': 'Y', '暂': 'Y', '赞': 'Y', '瑜': 'Y', '榆': 'Y', '愚': 'Y',
            '瑶': 'Y', '璐': 'L', '婷': 'T', '雯': 'W', '时': 'S', '棠': 'T',

            '志': 'Z', '智': 'Z', '制': 'Z', '治': 'Z', '中': 'Z', '忠': 'Z', '钟': 'Z', '终': 'Z', '种': 'Z', '重': 'Z',
            '众': 'Z', '周': 'Z', '州': 'Z', '洲': 'Z', '舟': 'Z', '竹': 'Z', '逐': 'Z', '主': 'Z', '住': 'Z', '助': 'Z',
            '注': 'Z', '驻': 'Z', '抓': 'Z', '爪': 'Z', '拽': 'Z', '专': 'Z', '砖': 'Z', '转': 'Z', '撰': 'Z', '赚': 'Z',
            '庄': 'Z', '装': 'Z', '妆': 'Z', '撞': 'Z', '壮': 'Z', '状': 'Z', '椎': 'Z', '锥': 'Z', '追': 'Z', '赘': 'Z',
            '坠': 'Z', '缀': 'Z', '谆': 'Z', '准': 'Z', '捉': 'Z', '拙': 'Z', '卓': 'Z', '桌': 'Z', '琢': 'Z', '茁': 'Z',
            '酌': 'Z', '啄': 'Z', '着': 'Z', '灼': 'Z', '浊': 'Z', '兹': 'Z', '咨': 'Z', '资': 'Z', '姿': 'Z', '滋': 'Z',
            '淄': 'Z', '孜': 'Z', '紫': 'Z', '仔': 'Z', '籽': 'Z', '滓': 'Z', '子': 'Z', '自': 'Z', '渍': 'Z', '字': 'Z',
            '鬃': 'Z', '棕': 'Z', '踪': 'Z', '宗': 'Z', '综': 'Z', '总': 'Z', '纵': 'Z', '邹': 'Z', '走': 'Z', '奏': 'Z',
            '揍': 'Z', '租': 'Z', '足': 'Z', '卒': 'Z', '族': 'Z', '祖': 'Z', '诅': 'Z', '阻': 'Z', '组': 'Z', '钻': 'Z',
            '纂': 'Z', '嘴': 'Z', '醉': 'Z', '最': 'Z', '罪': 'Z', '尊': 'Z', '遵': 'Z', '昨': 'Z', '左': 'Z', '佐': 'Z',
            '柞': 'Z', '做': 'Z', '作': 'Z', '坐': 'Z', '座': 'Z', '正': 'Z', '政': 'Z', '之': 'Z', '知': 'Z', '织': 'Z',
            '直': 'Z', '执': 'Z', '值': 'Z', '职': 'Z', '植': 'Z', '殖': 'Z', '止': 'Z', '指': 'Z', '只': 'Z', '旨': 'Z',
            '纸': 'Z', '至': 'Z', '致': 'Z', '置': 'Z', '帜': 'Z', '峙': 'Z', '制': 'Z', '智': 'Z', '秩': 'Z', '稚': 'Z',
            '质': 'Z', '炙': 'Z', '痔': 'Z', '滞': 'Z', '治': 'Z', '窒': 'Z', '中': 'Z', '盅': 'Z', '忠': 'Z', '钟': 'Z',
            '衷': 'Z', '终': 'Z', '种': 'Z', '肿': 'Z', '重': 'Z', '仲': 'Z', '众': 'Z', '舟': 'Z', '周': 'Z', '洲': 'Z',
            '诌': 'Z', '粥': 'Z', '轴': 'Z', '肘': 'Z', '帚': 'Z', '咒': 'Z', '皱': 'Z', '宙': 'Z', '昼': 'Z', '骤': 'Z',
            '珠': 'Z', '株': 'Z', '蛛': 'Z', '朱': 'Z', '猪': 'Z', '诸': 'Z', '诛': 'Z', '逐': 'Z', '竹': 'Z', '烛': 'Z',
            '煮': 'Z', '拄': 'Z', '瞩': 'Z', '嘱': 'Z', '主': 'Z', '著': 'Z', '柱': 'Z', '助': 'Z', '蛀': 'Z', '贮': 'Z',
            '铸': 'Z', '筑': 'Z', '住': 'Z', '注': 'Z', '祝': 'Z', '驻': 'Z', '抓': 'Z', '爪': 'Z', '拽': 'Z', '专': 'Z'
        };

        if (!chinese) return 'XXX';

        let result = '';
        // 处理姓名，通常取前3个字符（姓+名的前两个字）
        for (let i = 0; i < Math.min(chinese.length, 3); i++) {
            const char = chinese[i];
            if (pinyinFirstLetterMap[char]) {
                result += pinyinFirstLetterMap[char];
            } else if (/[A-Za-z]/.test(char)) {
                // 如果是英文字母，直接使用大写
                result += char.toUpperCase();
            } else {
                // 未知字符使用X代替
                result += 'X';
            }
        }

        // 确保结果始终为3位，不足的用X填充
        return result.padEnd(3, 'X').substring(0, 3);
    },

    // 测试拼音转换功能（用于验证）
    testPinyinConversion: function() {
        const testNames = ['张三', '李四', '王五', '赵小明', '陈建国', '刘小红', '杨志强', '黄美丽'];
        console.log('=== 拼音转换测试 ===');

        const expectedResults = {
            '张三': 'ZSX',
            '李四': 'LSX',
            '王五': 'WWX',
            '赵小明': 'ZXM',
            '陈建国': 'CJG',
            '刘小红': 'LXH',
            '杨志强': 'YZQ',
            '黄美丽': 'HML'
        };

        const results = [];
        testNames.forEach(name => {
            const pinyin = this.chineseToPinyin(name);
            const expected = expectedResults[name];
            const isCorrect = pinyin === expected;

            console.log(`${name} → ${pinyin} (预期: ${expected}) ${isCorrect ? '✓' : '✗'}`);
            results.push({
                name: name,
                actual: pinyin,
                expected: expected,
                correct: isCorrect
            });
        });

        // 测试订单号生成格式
        console.log('\n=== 订单号格式测试 ===');
        const testCases = [
            { advisor: '张三', date: '2025-07-26' },
            { advisor: '李四', date: '2025-07-26' },
            { advisor: '王五', date: '2025-07-26' },
            { advisor: '赵小明', date: '2025-07-26' }
        ];

        const orderExamples = [];
        testCases.forEach(testCase => {
            const orderDate = new Date(testCase.date);
            const year = orderDate.getFullYear().toString().slice(-2);
            const month = (orderDate.getMonth() + 1).toString().padStart(2, '0');
            const day = orderDate.getDate().toString().padStart(2, '0');
            const advisorCode = this.chineseToPinyin(testCase.advisor);
            const mockOrderNumber = `${year}${month}${day}${advisorCode}01`;

            console.log(`${testCase.advisor} (${testCase.date}) → ${mockOrderNumber} (${mockOrderNumber.length}位)`);
            orderExamples.push({
                advisor: testCase.advisor,
                date: testCase.date,
                orderNumber: mockOrderNumber,
                length: mockOrderNumber.length
            });
        });

        return {
            pinyinResults: results,
            orderExamples: orderExamples,
            allCorrect: results.every(r => r.correct)
        };
    },

    // 生成订单号
    generateOrderNumber: async function(order, index) {
        if (order.orderNumber) {
            return order.orderNumber;
        }

        // 格式：YYMMDDXXX01（年月日+销售顾问姓名拼音缩写+2位递增序号）
        const orderDate = new Date(order.orderDate || new Date());
        const year = orderDate.getFullYear().toString().slice(-2);
        const month = (orderDate.getMonth() + 1).toString().padStart(2, '0');
        const day = orderDate.getDate().toString().padStart(2, '0');

        // 获取销售顾问姓名拼音缩写
        const advisorCode = this.chineseToPinyin(order.salesAdvisor || '');

        // 计算同一天同一销售顾问的订单序号
        const dateStr = `${year}${month}${day}`;
        const prefix = `${dateStr}${advisorCode}`;

        try {
            // 从数据库获取所有订单来计算序号
            const allOrders = await window.dbFunctions.getAllOrderManagement();
            const sameAdvisorOrders = allOrders.filter(o => {
                if (!o.orderDate || !o.salesAdvisor || !o.orderNumber) return false;
                return o.orderNumber.startsWith(prefix);
            });

            // 计算下一个序号
            let nextSequence = 1;
            if (sameAdvisorOrders.length > 0) {
                const existingSequences = sameAdvisorOrders.map(o => {
                    const seqStr = o.orderNumber.slice(-2);
                    return parseInt(seqStr) || 0;
                });
                nextSequence = Math.max(...existingSequences) + 1;
            }

            const sequence = nextSequence.toString().padStart(2, '0');
            const orderNumber = `${prefix}${sequence}`;

            return orderNumber;
        } catch (error) {
            console.error('生成订单号失败:', error);
            // 降级方案：使用时间戳后2位
            const timestamp = Date.now().toString().slice(-2);
            return `${prefix}${timestamp}`;
        }
    },

    // 确保订单号唯一性
    ensureUniqueOrderNumber: async function(order) {
        let orderNumber = await this.generateOrderNumber(order, 0);
        let counter = 1;

        // 检查数据库中是否已存在相同订单号
        while (true) {
            const existingOrder = await window.dbFunctions.getOrderByOrderNumber(orderNumber);
            if (!existingOrder || existingOrder.id === order.id) {
                break;
            }
            // 如果存在重复，增加计数器
            const baseNumber = orderNumber.substring(0, orderNumber.length - 2);
            orderNumber = baseNumber + counter.toString().padStart(2, '0');
            counter++;

            // 防止无限循环
            if (counter > 99) {
                orderNumber = baseNumber + Date.now().toString().slice(-2);
                break;
            }
        }

        return orderNumber;
    },

    // 按月份筛选订单
    filterOrdersByMonth: function(monthValue) {
        if (!monthValue) {
            // 显示所有订单
            this.switchTab('orders');
            return;
        }

        const [year, month] = monthValue.split('-');
        const filteredOrders = this.allOrders.filter(order => {
            if (!order.orderDate) return false;
            const orderDate = new Date(order.orderDate);
            return orderDate.getFullYear() == year && (orderDate.getMonth() + 1) == month;
        });

        // 更新表格显示
        const tableContainer = document.querySelector('#orders-list .table-container');
        if (tableContainer) {
            tableContainer.innerHTML = this.renderOrdersTable(filteredOrders);
        }
    },

    // 导出订单数据 - 统一化导出功能
    exportOrdersToExcel: function() {
        try {
            if (!this.allOrders || this.allOrders.length === 0) {
                alert('没有可导出的订单数据');
                return;
            }

            // 显示导出选项模态框
            const modalContent = `
                <div class="export-modal">
                    <h3><i class="fas fa-download"></i> 导出订单数据</h3>
                    <div style="margin: 20px 0;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出格式：</label>
                            <label style="margin-right: 20px;">
                                <input type="radio" name="export-format" value="csv" checked> CSV格式
                            </label>
                            <label>
                                <input type="radio" name="export-format" value="excel"> Excel格式
                            </label>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出字段：</label>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                <label><input type="checkbox" value="orderNumber" checked> 订单号</label>
                                <label><input type="checkbox" value="auditStatus" checked> 审核状态</label>
                                <label><input type="checkbox" value="orderDate" checked> 订单日期</label>
                                <label><input type="checkbox" value="customerName" checked> 客户名称</label>
                                <label><input type="checkbox" value="idNumber" checked> 身份证号码</label>
                                <label><input type="checkbox" value="phone1" checked> 联系手机</label>
                                <label><input type="checkbox" value="address"> 联系地址</label>
                                <label><input type="checkbox" value="salesAdvisor" checked> 销售顾问</label>
                                <label><input type="checkbox" value="vin" checked> VIN</label>
                                <label><input type="checkbox" value="carModel" checked> 车型</label>
                                <label><input type="checkbox" value="configuration" checked> 配置</label>
                                <label><input type="checkbox" value="exteriorColor" checked> 外色</label>
                                <label><input type="checkbox" value="interiorColor" checked> 内饰</label>
                                <label><input type="checkbox" value="options" checked> 选装件</label>
                                <label><input type="checkbox" value="deliveryDate"> 交付日期</label>
                                <label><input type="checkbox" value="deposit" checked> 定金</label>
                                <label><input type="checkbox" value="contractPrice" checked> 合同价</label>
                                <label><input type="checkbox" value="deliveryStatus" checked> 交付状态</label>
                            </div>
                            <div style="margin-top: 10px;">
                                <button type="button" class="btn btn-sm btn-outline" onclick="orderFunctions.selectAllOrderFields(true)">全选</button>
                                <button type="button" class="btn btn-sm btn-outline" onclick="orderFunctions.selectAllOrderFields(false)">全不选</button>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button class="btn btn-primary" id="confirm-export-btn">确认导出</button>
                    </div>
                </div>
            `;

            this.showModal('导出数据', modalContent);

            // 绑定导出确认事件
            document.getElementById('confirm-export-btn').addEventListener('click', () => {
                const format = document.querySelector('input[name="export-format"]:checked').value;
                const selectedFields = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                    .map(cb => cb.value);

                if (selectedFields.length === 0) {
                    alert('请至少选择一个字段');
                    return;
                }

                this.performOrderExport(format, selectedFields);
                const modal = document.querySelector('.modal-overlay');
                if (modal) modal.remove();
            });

        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败: ' + error.message);
        }
    },

    // 执行订单导出
    performOrderExport: function(format, selectedFields) {
        try {
            // 字段名映射
            const fieldNames = {
                orderNumber: '订单号',
                auditStatus: '审核状态',
                orderDate: '订单日期',
                customerName: '客户名称',
                idNumber: '身份证号码',
                phone1: '联系手机',
                address: '联系地址',
                salesAdvisor: '销售顾问',
                vin: 'VIN',
                carModel: '车型',
                configuration: '配置',
                exteriorColor: '外色',
                interiorColor: '内饰',
                options: '选装件',
                deliveryDate: '交付日期',
                deposit: '定金',
                contractPrice: '合同价',
                deliveryStatus: '交付状态'
            };

            // 准备导出数据
            const exportData = this.allOrders.map((order, index) => {
                const row = {};
                selectedFields.forEach(field => {
                    let value = order[field] || '';

                    // 特殊处理订单号字段
                    if (field === 'orderNumber') {
                        value = order.orderNumber || '待生成';
                    }

                    row[fieldNames[field]] = value;
                });
                return row;
            });

            if (format === 'csv') {
                this.exportOrdersToCSV(exportData);
            } else {
                this.exportOrdersToExcel_New(exportData);
            }

        } catch (error) {
            console.error('导出处理失败:', error);
            alert('导出处理失败: ' + error.message);
        }
    },

    // 全选/全不选订单导出字段
    selectAllOrderFields: function(selectAll) {
        const checkboxes = document.querySelectorAll('input[type="checkbox"][value]');
        checkboxes.forEach(cb => {
            cb.checked = selectAll;
        });
    },

    // 导出订单到CSV
    exportOrdersToCSV: function(data) {
        if (data.length === 0) return;

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');

        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `订单数据_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('CSV文件导出成功！');
    },

    // 导出订单到Excel（新版本）
    exportOrdersToExcel_New: function(data) {
        if (data.length === 0) return;

        try {
            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Orders');

            // 设置列宽
            const colWidths = Object.keys(data[0]).map(() => ({ wch: 15 }));
            worksheet['!cols'] = colWidths;

            XLSX.writeFile(workbook, `订单数据_${new Date().toISOString().split('T')[0]}.xlsx`);
            alert('Excel文件导出成功！');

        } catch (error) {
            console.error('Excel导出失败:', error);
            alert('Excel导出失败，请确保已加载XLSX库: ' + error.message);
        }
    },

    // 从Excel导入订单
    importOrdersFromExcel: function() {
        const modalContent = `
            <div class="import-modal">
                <h3><i class="fas fa-upload"></i> 导入订单数据</h3>
                <div style="margin: 20px 0;">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导入格式：</label>
                        <label style="margin-right: 20px;">
                            <input type="radio" name="order-import-format" value="excel" checked> Excel格式 (.xlsx, .xls)
                        </label>
                        <label>
                            <input type="radio" name="order-import-format" value="csv"> CSV格式 (.csv)
                        </label>
                    </div>
                    <p style="color: #6c757d; margin-bottom: 15px;">请确保文件包含以下字段：</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                        <strong>必需字段：</strong> 客户名称, 联系手机, 销售顾问, 车型<br>
                        <strong>可选字段：</strong> 订单日期, VIN, 配置, 外色, 内饰, 选装件, 定金, 合同价
                    </div>
                    <input type="file" id="order-file-input" accept=".xlsx,.xls,.csv" style="margin-bottom: 15px; width: 100%;">
                    <div id="import-progress" style="display: none; margin-bottom: 15px;">
                        <div style="background: #e9ecef; border-radius: 10px; overflow: hidden;">
                            <div id="import-progress-bar" style="background: #4361ee; height: 20px; width: 0%; transition: width 0.3s;"></div>
                        </div>
                        <div id="import-status" style="margin-top: 5px; font-size: 14px;"></div>
                    </div>
                </div>
                <div style="text-align: right;">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" id="confirm-import-btn" disabled>确认导入</button>
                </div>
            </div>
        `;

        this.showModal('批量导入', modalContent);

        // 绑定格式选择事件
        document.querySelectorAll('input[name="order-import-format"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                const fileInput = document.getElementById('order-file-input');
                if (e.target.value === 'excel') {
                    fileInput.accept = '.xlsx,.xls';
                } else {
                    fileInput.accept = '.csv';
                }
                // 清空已选择的文件
                fileInput.value = '';
                document.getElementById('confirm-import-btn').disabled = true;
            });
        });

        // 绑定文件选择事件
        const fileInput = document.getElementById('order-file-input');
        const confirmBtn = document.getElementById('confirm-import-btn');

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                confirmBtn.disabled = false;
            }
        });

        confirmBtn.addEventListener('click', () => {
            const file = fileInput.files[0];
            if (file) {
                this.processOrderImportFile(file);
            }
        });
    },

    // 处理订单导入文件
    processOrderImportFile: async function(file) {
        const progressDiv = document.getElementById('import-progress');
        const progressBar = document.getElementById('import-progress-bar');
        const statusDiv = document.getElementById('import-status');

        progressDiv.style.display = 'block';
        progressBar.style.width = '0%';
        statusDiv.textContent = '正在处理文件...';

        try {
            const fileName = file.name.toLowerCase();
            const reader = new FileReader();

            reader.onload = async (e) => {
                try {
                    let data;

                    if (fileName.endsWith('.csv')) {
                        // 处理CSV文件
                        const text = e.target.result;
                        const lines = text.split('\n').filter(line => line.trim() !== '');

                        if (lines.length < 2) {
                            throw new Error('CSV文件格式不正确，至少需要标题行和一行数据');
                        }

                        // 解析标题行
                        const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''));

                        // 解析数据行
                        data = [];
                        for (let i = 1; i < lines.length; i++) {
                            const values = lines[i].split(',').map(value => value.trim().replace(/"/g, ''));
                            const row = {};

                            headers.forEach((header, index) => {
                                row[header] = values[index] || '';
                            });

                            data.push(row);
                        }
                    } else {
                        // 处理Excel文件
                        const workbook = XLSX.read(e.target.result, { type: 'binary' });
                        const sheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[sheetName];
                        data = XLSX.utils.sheet_to_json(worksheet, {
                            defval: '',
                            raw: false
                        });
                    }

                    await this.importOrderData(data, progressBar, statusDiv);
                } catch (error) {
                    console.error('导入失败:', error);
                    statusDiv.textContent = '导入失败: ' + error.message;
                    statusDiv.style.color = 'red';
                }
            };

            if (fileName.endsWith('.csv')) {
                reader.readAsText(file, 'UTF-8');
            } else {
                reader.readAsBinaryString(file);
            }
        } catch (error) {
            console.error('文件处理失败:', error);
            statusDiv.textContent = '文件处理失败: ' + error.message;
            statusDiv.style.color = 'red';
        }
    },

    // 导入订单数据
    importOrderData: async function(data, progressBar, statusDiv) {
        const total = data.length;
        let successful = 0;
        let failed = 0;
        const errors = [];

        statusDiv.textContent = `正在导入 ${total} 条记录...`;

        for (let i = 0; i < total; i++) {
            try {
                const row = data[i];

                // 数据验证和转换
                const orderData = this.validateAndTransformOrderData(row);

                if (orderData) {
                    // 生成订单号
                    const tempOrder = { ...orderData, id: Date.now() + i }; // 临时ID用于生成订单号
                    const orderNumber = await this.ensureUniqueOrderNumber(tempOrder);
                    orderData.orderNumber = orderNumber;

                    await window.dbFunctions.addOrderManagement(orderData);
                    successful++;
                } else {
                    failed++;
                    errors.push(`第 ${i + 1} 行: 缺少必需字段`);
                }
            } catch (error) {
                failed++;
                errors.push(`第 ${i + 1} 行: ${error.message}`);
            }

            // 更新进度
            const progress = ((i + 1) / total) * 100;
            progressBar.style.width = progress + '%';
            statusDiv.textContent = `正在导入... ${i + 1}/${total}`;

            // 让UI有时间更新
            await new Promise(resolve => setTimeout(resolve, 10));
        }

        // 显示结果
        statusDiv.innerHTML = `
            导入完成！<br>
            成功: ${successful} 条<br>
            失败: ${failed} 条
            ${errors.length > 0 ? '<br><br>错误详情:<br>' + errors.slice(0, 5).join('<br>') : ''}
            ${errors.length > 5 ? '<br>...(还有 ' + (errors.length - 5) + ' 个错误)' : ''}
        `;

        if (successful > 0) {
            statusDiv.style.color = 'green';
            // 刷新订单列表
            setTimeout(() => {
                this.loadOrders();
                const modal = document.querySelector('.modal-overlay');
                if (modal) modal.remove();
            }, 2000);
        } else {
            statusDiv.style.color = 'red';
        }
    },

    // 验证和转换订单数据
    validateAndTransformOrderData: function(row) {
        // 字段映射（支持多种可能的字段名）
        const fieldMap = {
            '客户名称': 'customerName',
            '客户姓名': 'customerName',
            '姓名': 'customerName',
            '身份证号码': 'idNumber',
            '身份证号': 'idNumber',
            '身份证': 'idNumber',
            '联系手机': 'phone1',
            '手机': 'phone1',
            '电话': 'phone1',
            '手机号': 'phone1',
            '联系地址': 'address',
            '地址': 'address',
            '家庭地址': 'address',
            '销售顾问': 'salesAdvisor',
            '顾问': 'salesAdvisor',
            '车型': 'carModel',
            '车型名称': 'carModel',
            '订单日期': 'orderDate',
            '下单日期': 'orderDate',
            '日期': 'orderDate',
            'VIN': 'vin',
            'vin': 'vin',
            '车架号': 'vin',
            '配置': 'configuration',
            '车辆配置': 'configuration',
            '外色': 'exteriorColor',
            '外观颜色': 'exteriorColor',
            '车身颜色': 'exteriorColor',
            '内饰': 'interiorColor',
            '内饰颜色': 'interiorColor',
            '选装件': 'options',
            '选装': 'options',
            '选配': 'options',
            '选装配置': 'options',
            '交付日期': 'deliveryDate',
            '交车日期': 'deliveryDate',
            '提车日期': 'deliveryDate',
            '定金': 'deposit',
            '订金': 'deposit',
            '合同价': 'contractPrice',
            '成交价': 'contractPrice',
            '总价': 'contractPrice'
        };

        const orderData = {
            auditStatus: '待审核',
            orderDate: new Date().toISOString().split('T')[0],
            customerName: '',
            idNumber: '',
            phone1: '',
            address: '',
            salesAdvisor: '',
            vin: '',
            carModel: '',
            configuration: '',
            exteriorColor: '',
            interiorColor: '',
            options: '',
            deliveryStatus: '待交付',
            deliveryDate: '',
            deposit: '',
            contractPrice: ''
        };

        // 转换数据
        for (const [key, value] of Object.entries(row)) {
            const mappedField = fieldMap[key];
            if (mappedField && value !== null && value !== undefined && String(value).trim() !== '') {
                let processedValue = String(value).trim();

                // 特殊字段处理
                switch (mappedField) {
                    case 'options':
                        // 处理选装件字段，支持多种分隔符
                        processedValue = this.processOptionsField(processedValue);
                        break;
                    case 'deposit':
                    case 'contractPrice':
                        // 处理金额字段
                        processedValue = this.processAmountField(processedValue);
                        break;
                    case 'orderDate':
                    case 'deliveryDate':
                        // 处理日期字段
                        processedValue = this.processDateField(processedValue);
                        break;
                    case 'phone1':
                        // 处理手机号字段
                        processedValue = this.processPhoneField(processedValue);
                        break;
                }

                orderData[mappedField] = processedValue;
            }
        }

        // 验证必需字段
        if (!orderData.customerName || !orderData.idNumber || !orderData.phone1 || !orderData.salesAdvisor || !orderData.carModel) {
            return null;
        }

        // 验证身份证号码格式
        const idRegex = /^[0-9]{17}[0-9X]$/;
        if (!idRegex.test(orderData.idNumber)) {
            console.warn(`身份证号码格式不正确: ${orderData.idNumber}`);
            return null;
        }

        // 验证手机号格式
        const phoneRegex = /^[0-9]{11}$/;
        if (!phoneRegex.test(orderData.phone1)) {
            console.warn(`手机号格式不正确: ${orderData.phone1}`);
            return null;
        }

        return orderData;
    },

    // 处理选装件字段
    processOptionsField: function(value) {
        if (!value) return '';

        // 支持多种分隔符：逗号、分号、换行符、竖线等
        const separators = [',', '，', ';', '；', '\n', '\r\n', '|', '、'];
        let options = [value];

        // 依次使用各种分隔符分割
        for (const separator of separators) {
            const newOptions = [];
            for (const option of options) {
                newOptions.push(...option.split(separator));
            }
            options = newOptions;
        }

        // 清理和去重
        const cleanedOptions = options
            .map(option => option.trim())
            .filter(option => option !== '')
            .filter((option, index, arr) => arr.indexOf(option) === index); // 去重

        return cleanedOptions.join(', ');
    },

    // 处理金额字段
    processAmountField: function(value) {
        if (!value) return '';

        // 移除货币符号、千位分隔符等
        let cleanValue = String(value)
            .replace(/[￥¥$,，]/g, '') // 移除货币符号和千位分隔符
            .replace(/\s+/g, '') // 移除空格
            .trim();

        // 检查是否为有效数字
        if (!/^\d+(\.\d{1,2})?$/.test(cleanValue)) {
            console.warn(`金额格式不正确: ${value}`);
            return '';
        }

        return cleanValue;
    },

    // 处理日期字段
    processDateField: function(value) {
        if (!value) return '';

        const dateStr = String(value).trim();

        // 支持多种日期格式
        const dateFormats = [
            /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
            /^\d{4}\/\d{2}\/\d{2}$/, // YYYY/MM/DD
            /^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
            /^\d{2}-\d{2}-\d{4}$/, // MM-DD-YYYY
            /^\d{4}\.\d{2}\.\d{2}$/, // YYYY.MM.DD
            /^\d{4}年\d{1,2}月\d{1,2}日$/ // YYYY年MM月DD日
        ];

        // 检查是否匹配任何格式
        const isValidFormat = dateFormats.some(format => format.test(dateStr));

        if (!isValidFormat) {
            console.warn(`日期格式不正确: ${value}`);
            return '';
        }

        try {
            let date;

            if (/^\d{4}年\d{1,2}月\d{1,2}日$/.test(dateStr)) {
                // 处理中文日期格式
                const match = dateStr.match(/^(\d{4})年(\d{1,2})月(\d{1,2})日$/);
                if (match) {
                    const [, year, month, day] = match;
                    date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
                }
            } else if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr) || /^\d{2}-\d{2}-\d{4}$/.test(dateStr)) {
                // 处理MM/DD/YYYY或MM-DD-YYYY格式
                const parts = dateStr.split(/[\/\-]/);
                date = new Date(parseInt(parts[2]), parseInt(parts[0]) - 1, parseInt(parts[1]));
            } else {
                // 其他格式直接解析
                date = new Date(dateStr.replace(/[\/\.]/g, '-'));
            }

            if (isNaN(date.getTime())) {
                console.warn(`日期解析失败: ${value}`);
                return '';
            }

            // 返回标准格式 YYYY/M/DD
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}/${month}/${day}`;
        } catch (error) {
            console.warn(`日期处理错误: ${value}`, error);
            return '';
        }
    },

    // 处理手机号字段
    processPhoneField: function(value) {
        if (!value) return '';

        // 移除所有非数字字符
        const cleanPhone = String(value).replace(/\D/g, '');

        // 检查长度
        if (cleanPhone.length === 11 && cleanPhone.startsWith('1')) {
            return cleanPhone;
        } else if (cleanPhone.length === 13 && cleanPhone.startsWith('86')) {
            // 处理+86开头的手机号
            return cleanPhone.substring(2);
        }

        console.warn(`手机号格式不正确: ${value}`);
        return '';
    },

    // 保存库存
    saveInventory: async function(editId) {
        try {
            const form = document.getElementById('inventory-form');
            const formData = new FormData(form);
            const inventoryData = {};

            for (let [key, value] of formData.entries()) {
                inventoryData[key] = value;
            }

            // 验证必填字段
            if (!inventoryData.carModel || !inventoryData.stockDate) {
                alert('请填写必填字段');
                return;
            }

            if (editId !== null) {
                await window.dbFunctions.updateInventoryManagement(editId, inventoryData);
                alert('库存更新成功');
            } else {
                // 新增库存时默认设置状态为"可售"
                inventoryData.inventoryStatus = '可售';
                await window.dbFunctions.addInventoryManagement(inventoryData);
                alert('库存添加成功');
            }

            // 关闭模态框
            document.querySelector('.modal-overlay').remove();

            // 重新加载数据
            this.allInventory = await window.dbFunctions.getAllInventoryManagement();
            this.switchTab('inventory');

        } catch (error) {
            console.error('保存库存失败:', error);
            alert('保存库存失败: ' + error.message);
        }
    },

    // 显示库存详情（只读预览）
    showInventoryDetails: async function(inventoryId) {
        try {
            const inventory = await window.dbFunctions.getInventoryManagementById(inventoryId);
            if (!inventory) {
                alert('库存记录不存在');
                return;
            }

            this.showModal('库存详情', this.renderInventoryDetailsReadOnly(inventory));
        } catch (error) {
            console.error('显示库存详情失败:', error);
            alert('显示库存详情失败: ' + error.message);
        }
    },

    // 渲染库存详情（只读预览）
    renderInventoryDetailsReadOnly: function(inventory) {
        // 计算库龄
        let stockAge = 0;
        if (inventory.stockDate) {
            stockAge = Math.floor((new Date() - new Date(inventory.stockDate)) / (1000 * 60 * 60 * 24));
        }

        return `
            <div class="inventory-details" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="detail-group">
                    <label>订单号：</label>
                    <span>${inventory.serialNumber || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>库存状态：</label>
                    <span class="status inventory-${inventory.inventoryStatus}">${inventory.inventoryStatus || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>车型：</label>
                    <span>${inventory.carModel || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>版本：</label>
                    <span>${inventory.version || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>车架号：</label>
                    <span>${inventory.vin || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>外色：</label>
                    <span>${inventory.exteriorColor || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>内饰：</label>
                    <span>${inventory.interiorColor || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>原厂选装：</label>
                    <span>${inventory.factoryOptions || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>标准：</label>
                    <span>${inventory.standard || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>位置：</label>
                    <span>${inventory.location || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>生产日期：</label>
                    <span>${inventory.productionDate || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>发运日期：</label>
                    <span>${inventory.shipmentDate || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>入库日期：</label>
                    <span>${inventory.stockDate || '-'}</span>
                </div>
                <div class="detail-group">
                    <label>库龄：</label>
                    <span>${stockAge}天</span>
                </div>
                <div class="detail-group">
                    <label>指导价：</label>
                    <span>${inventory.guidePrice ? '¥' + inventory.guidePrice : '-'}</span>
                </div>
                <div class="detail-group" style="grid-column: 1 / -1;">
                    <label>备注：</label>
                    <span>${inventory.notes || '-'}</span>
                </div>

                <div class="detail-actions" style="grid-column: 1 / -1; margin-top: 20px; text-align: center;">
                    <button type="button" class="btn btn-primary" onclick="orderFunctions.editInventoryDetails(${inventory.id})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()" style="margin-left: 10px;">
                        <i class="fas fa-times"></i> 关闭
                    </button>
                </div>
            </div>
        `;
    },

    // 编辑库存详情（切换为可编辑状态）
    editInventoryDetails: async function(inventoryId) {
        try {
            const inventory = await window.dbFunctions.getInventoryManagementById(inventoryId);
            if (!inventory) {
                alert('库存记录不存在');
                return;
            }

            // 更新模态框内容为可编辑表单
            const modalContent = document.querySelector('.modal-content');
            if (modalContent) {
                modalContent.innerHTML = this.renderInventoryDetails(inventory);
            }
        } catch (error) {
            console.error('编辑库存详情失败:', error);
            alert('编辑库存详情失败: ' + error.message);
        }
    },

    // 渲染库存详情（具备编辑功能的表单）
    renderInventoryDetails: function(inventory) {
        // 计算库龄
        let stockAge = 0;
        if (inventory.stockDate) {
            stockAge = Math.floor((new Date() - new Date(inventory.stockDate)) / (1000 * 60 * 60 * 24));
        }

        return `
            <form id="inventory-details-form" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div class="form-group">
                    <label>订单号：</label>
                    <input type="text" name="serialNumber" value="${inventory.serialNumber || ''}" placeholder="自动生成">
                </div>
                <div class="form-group">
                    <label>库存状态：</label>
                    <select name="inventoryStatus">
                        <option value="可售" ${inventory.inventoryStatus === '可售' ? 'selected' : ''}>可售</option>
                        <option value="已配车" ${inventory.inventoryStatus === '已配车' ? 'selected' : ''}>已配车</option>
                        <option value="已交车" ${inventory.inventoryStatus === '已交车' ? 'selected' : ''}>已交车</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>车型 *：</label>
                    <input type="text" name="carModel" value="${inventory.carModel || ''}" required>
                </div>
                <div class="form-group">
                    <label>版本：</label>
                    <input type="text" name="version" value="${inventory.version || ''}">
                </div>
                <div class="form-group">
                    <label>车架号：</label>
                    <input type="text" name="vin" value="${inventory.vin || ''}">
                </div>
                <div class="form-group">
                    <label>外色：</label>
                    <input type="text" name="exteriorColor" value="${inventory.exteriorColor || ''}">
                </div>
                <div class="form-group">
                    <label>内饰：</label>
                    <input type="text" name="interiorColor" value="${inventory.interiorColor || ''}">
                </div>
                <div class="form-group">
                    <label>原厂选装：</label>
                    <input type="text" name="factoryOptions" value="${inventory.factoryOptions || ''}">
                </div>
                <div class="form-group">
                    <label>标准：</label>
                    <input type="text" name="standard" value="${inventory.standard || ''}">
                </div>
                <div class="form-group">
                    <label>位置：</label>
                    <input type="text" name="location" value="${inventory.location || ''}">
                </div>
                <div class="form-group">
                    <label>生产日期：</label>
                    <input type="date" name="productionDate" value="${inventory.productionDate || ''}">
                </div>
                <div class="form-group">
                    <label>发运日期：</label>
                    <input type="date" name="shipmentDate" value="${inventory.shipmentDate || ''}">
                </div>
                <div class="form-group">
                    <label>入库日期 *：</label>
                    <input type="date" name="stockDate" value="${inventory.stockDate || ''}" required>
                </div>
                <div class="form-group">
                    <label>库龄：</label>
                    <input type="text" value="${stockAge}天" readonly style="background-color: #f8f9fa;">
                </div>
                <div class="form-group">
                    <label>指导价：</label>
                    <input type="number" name="guidePrice" value="${inventory.guidePrice || ''}" step="0.01">
                </div>
                <div class="form-group" style="grid-column: 1 / -1;">
                    <label>备注：</label>
                    <textarea name="notes" rows="3" style="width: 100%; resize: vertical;">${inventory.notes || ''}</textarea>
                </div>

                <div class="detail-actions" style="grid-column: 1 / -1; margin-top: 20px; text-align: center;">
                    <button type="button" class="btn btn-primary" onclick="orderFunctions.saveInventoryDetails(${inventory.id})">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()" style="margin-left: 10px;">
                        <i class="fas fa-times"></i> 关闭
                    </button>
                </div>
            </form>
        `;
    },

    // 保存库存详情
    saveInventoryDetails: async function(inventoryId) {
        try {
            const form = document.getElementById('inventory-details-form');
            const formData = new FormData(form);
            const inventoryData = {};

            for (let [key, value] of formData.entries()) {
                inventoryData[key] = value;
            }

            // 验证必填字段
            if (!inventoryData.carModel || !inventoryData.stockDate) {
                alert('请填写必填字段');
                return;
            }

            await window.dbFunctions.updateInventoryManagement(inventoryId, inventoryData);
            alert('库存更新成功');

            // 关闭模态框
            document.querySelector('.modal-overlay').remove();

            // 重新加载数据
            this.allInventory = await window.dbFunctions.getAllInventoryManagement();
            this.switchTab('inventory');

        } catch (error) {
            console.error('保存库存详情失败:', error);
            alert('保存库存详情失败: ' + error.message);
        }
    },

    // 保存目标
    saveTarget: async function(type) {
        try {
            const form = document.getElementById('target-form');
            const formData = new FormData(form);
            const targetData = {};

            for (let [key, value] of formData.entries()) {
                targetData[key] = value;
            }

            // 验证必填字段
            if (!targetData.yearMonth || !targetData.target) {
                alert('请填写必填字段');
                return;
            }

            // 重新计算完成率，确保使用Math.floor
            const target = parseFloat(targetData.target) || 0;
            const actual = parseFloat(targetData.actual) || 0;
            if (target > 0) {
                targetData.completionRate = Math.floor(actual / target * 100);
            } else {
                targetData.completionRate = 0;
            }

            // 根据类型调用不同的保存函数
            let saveFunction;
            switch(type) {
                case 'delivery':
                    saveFunction = 'addDeliveryTarget';
                    break;
                case 'order':
                    saveFunction = 'addOrderTarget';
                    break;
                case 'retail':
                    saveFunction = 'addRetailTarget';
                    break;
                default:
                    throw new Error('未知的目标类型');
            }

            await window.dbFunctions[saveFunction](targetData);
            alert('目标保存成功');

            // 关闭模态框
            document.querySelector('.modal-overlay').remove();

            // 重新加载数据
            this.allDeliveryTargets = await window.dbFunctions.getAllDeliveryTargets();
            this.allOrderTargets = await window.dbFunctions.getAllOrderTargets();
            this.allRetailTargets = await window.dbFunctions.getAllRetailTargets();
            this.switchTab('targets');

        } catch (error) {
            console.error('保存目标失败:', error);
            alert('保存目标失败: ' + error.message);
        }
    },

    // 审核通过订单
    approveOrder: async function(orderId) {
        try {
            // 获取订单详情
            const order = await window.dbFunctions.getOrderManagementById(orderId);
            if (!order) {
                alert('订单不存在');
                return;
            }

            // 更新订单状态为已审核
            await window.dbFunctions.updateOrderManagement(orderId, {auditStatus: '已审核'});

            // 自动生成出库管理记录
            await this.generateOutboundRecords(order);

            // 刷新配件库存计算
            if (window.partsFunctions && window.partsFunctions.refreshStockCalculations) {
                await window.partsFunctions.refreshStockCalculations();
            }

            alert('订单审核通过，已自动生成出库记录');
            await this.loadOrders();
            this.switchTab('audit');
        } catch (error) {
            console.error('审核订单失败:', error);
            alert('审核订单失败: ' + error.message);
        }
    },

    // 自动生成出库管理记录
    generateOutboundRecords: async function(order) {
        try {
            if (!order.options) return; // 没有选装件则不生成出库记录

            // 解析选装件
            const options = order.options.split(',').map(opt => opt.trim()).filter(opt => opt);

            for (const option of options) {
                let partName = option;
                let quantity = 1;

                // 解析数量
                if (option.includes(' x')) {
                    const parts = option.split(' x');
                    partName = parts[0].trim();
                    quantity = parseInt(parts[1]) || 1;
                }

                // 查找配件信息
                const partInfo = await window.dbFunctions.getPartsInboundByCode(partName);
                let salePrice = 0;
                let guidePrice = 0;
                let insurancePrice = 0;

                if (partInfo && partInfo.length > 0) {
                    const part = partInfo[0];
                    salePrice = part.salePrice || 0;
                    guidePrice = part.guidePrice || 0;
                    insurancePrice = part.insurancePrice || 0;
                }

                // 生成出库记录
                const outboundData = {
                    outboundTime: new Date().toISOString(),
                    personnel: order.salesAdvisor || '系统自动',
                    customerName: order.customerName,
                    vin: order.vin || '',
                    type: '精品',
                    partCode: '', // 如果有配件代码可以在这里设置
                    partName: partName,
                    quantity: quantity,
                    salePrice: salePrice,
                    guidePrice: guidePrice,
                    insurancePrice: insurancePrice,
                    status: '已出库'
                };

                await window.dbFunctions.addPartsOutbound(outboundData);
            }

            console.log(`订单 ${order.serialNumber} 的选装件已自动生成出库记录`);
        } catch (error) {
            console.error('生成出库记录失败:', error);
            // 不抛出错误，避免影响订单审核流程
        }
    },

    // 显示订单管理列设置
    showOrderColumnSettings: function() {
        const modalContent = `
            <div class="column-settings-modal">
                <h3><i class="fas fa-columns"></i> 订单管理列设置</h3>
                <div style="margin: 20px 0;">
                    <p style="color: #6c757d; margin-bottom: 15px;">选择要显示的列：</p>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; max-height: 300px; overflow-y: auto;">
                        ${this.renderOrderColumnCheckboxes()}
                    </div>
                </div>
                <div style="text-align: right;">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" onclick="orderFunctions.applyOrderColumnSettings()" style="margin-left: 10px;">应用设置</button>
                </div>
            </div>
        `;

        this.showModal('列设置', modalContent);
    },

    // 渲染订单列复选框
    renderOrderColumnCheckboxes: function() {
        const fieldNames = {
            orderNumber: '订单号',
            auditStatus: '审核状态',
            orderDate: '订单日期',
            customerName: '客户名称',
            idNumber: '身份证号码',
            phone1: '联系手机',
            address: '联系地址',
            salesAdvisor: '销售顾问',
            vin: 'VIN',
            carModel: '车型',
            configuration: '配置',
            exteriorColor: '外色',
            interiorColor: '内饰',
            options: '选装件',
            deliveryDate: '交付日期',
            deposit: '定金',
            contractPrice: '合同价'
        };

        return Object.entries(fieldNames).map(([field, label]) => `
            <label style="display: flex; align-items: center; padding: 8px; border: 1px solid #e9ecef; border-radius: 4px; cursor: pointer;">
                <input type="checkbox" value="${field}" ${this.orderColumnSettings[field] ? 'checked' : ''} style="margin-right: 8px;">
                <span>${label}</span>
            </label>
        `).join('');
    },

    // 应用订单列设置
    applyOrderColumnSettings: function() {
        const checkboxes = document.querySelectorAll('.column-settings-modal input[type="checkbox"]');

        // 重置所有设置为false
        Object.keys(this.orderColumnSettings).forEach(key => {
            this.orderColumnSettings[key] = false;
        });

        // 根据勾选状态更新设置
        checkboxes.forEach(checkbox => {
            this.orderColumnSettings[checkbox.value] = checkbox.checked;
        });

        // 保存设置到localStorage
        localStorage.setItem('orderColumnSettings', JSON.stringify(this.orderColumnSettings));

        // 重新渲染订单列表
        const orderListContainer = document.getElementById('all-orders');
        if (orderListContainer) {
            orderListContainer.innerHTML = this.renderOrderList(this.allOrders);
        }

        // 关闭模态框
        document.querySelector('.modal-overlay').remove();

        alert('列设置已应用');
    },

    // 加载列设置
    loadOrderColumnSettings: function() {
        const saved = localStorage.getItem('orderColumnSettings');
        if (saved) {
            try {
                this.orderColumnSettings = { ...this.orderColumnSettings, ...JSON.parse(saved) };
            } catch (error) {
                console.error('加载列设置失败:', error);
            }
        }
    },

    // 驳回订单
    rejectOrder: async function(orderId) {
        if (confirm('确定要驳回这个订单吗？\n驳回后订单将自动返回待审批状态。')) {
            try {
                await window.dbFunctions.updateOrderManagement(orderId, {auditStatus: '待审核'});
                alert('订单已驳回，已自动返回待审批状态');
                await this.loadOrders();
                this.switchTab('audit');
            } catch (error) {
                console.error('驳回订单失败:', error);
                alert('驳回订单失败: ' + error.message);
            }
        }
    },

    // 返审核功能 - 将已审核订单返回到待审核状态
    returnToAudit: async function(orderId) {
        try {
            const order = await window.dbFunctions.getOrderManagementById(orderId);
            if (!order) {
                alert('订单不存在');
                return;
            }

            if (order.auditStatus !== '已审核') {
                alert('只有已审核状态的订单才能返审核');
                return;
            }

            // 检查订单是否已经配车
            if (order.auditStatus === '已配车' || order.auditStatus === '已交车') {
                alert('该订单已配车或已交车，无法返审核');
                return;
            }

            if (confirm(`确定要将订单返审核吗？\n客户：${order.customerName}\n订单将从"已审核"状态回退到"待审核"状态`)) {
                await window.dbFunctions.updateOrderManagement(orderId, {auditStatus: '待审核'});
                alert('订单已返审核，状态已回退到待审核');

                await this.loadOrders();
                this.switchTab('audit');
            }
        } catch (error) {
            console.error('返审核失败:', error);
            alert('返审核失败: ' + error.message);
        }
    },

    // 退回重审（保留原有功能）
    returnToReview: async function(orderId) {
        if (confirm('确定要将此已审批订单退回重审吗？\n退回后订单将返回待审批状态。')) {
            try {
                await window.dbFunctions.updateOrderManagement(orderId, {auditStatus: '待审核'});
                alert('订单已退回重审，已返回待审批状态');

                // 关闭当前模态框
                const modal = document.querySelector('.modal-overlay');
                if (modal) modal.remove();

                // 重新加载数据
                await this.loadOrders();
                this.switchTab('orders');
            } catch (error) {
                console.error('退回重审失败:', error);
                alert('退回重审失败: ' + error.message);
            }
        }
    },

    // 查看订单详情
    viewOrderDetails: async function(orderId) {
        try {
            const order = await window.dbFunctions.getOrderManagementById(orderId);
            if (order) {
                this.showOrderForm(orderId);
            }
        } catch (error) {
            console.error('查看订单详情失败:', error);
            alert('查看订单详情失败: ' + error.message);
        }
    },

    // 同步已审核订单到配车交付
    syncOrdersToDelivery: async function() {
        try {
            const approvedOrders = this.allOrders.filter(order => order.auditStatus === '已审核');
            let syncCount = 0;

            for (const order of approvedOrders) {
                // 检查是否已存在（优先使用订单ID，降级使用序号）
                const existing = this.allDeliveryManagement.find(d =>
                    d.orderId === order.id || d.serialNumber === order.serialNumber
                );
                if (!existing) {
                    const deliveryData = {
                        orderId: order.id, // 添加订单ID字段，确保准确关联
                        serialNumber: order.serialNumber,
                        auditStatus: '已审核', // 同步时设置为已审核状态，等待配车
                        orderDate: order.orderDate,
                        deliveryDate: order.deliveryDate,
                        customerName: order.customerName,
                        phone1: order.phone1,
                        phone2: order.phone2 || '',
                        salesAdvisor: order.salesAdvisor,
                        carModel: order.carModel,
                        configuration: order.configuration,
                        exteriorColor: order.exteriorColor,
                        interiorColor: order.interiorColor,
                        vin: order.vin,
                        // 同步财务和配置信息
                        options: order.options || '',
                        deposit: order.deposit || 0,
                        contractPrice: order.contractPrice || 0
                    };

                    await window.dbFunctions.addDeliveryManagement(deliveryData);
                    syncCount++;
                }
            }

            alert(`同步完成，新增 ${syncCount} 条配车交付记录`);
            this.allDeliveryManagement = await window.dbFunctions.getAllDeliveryManagement();
            this.switchTab('delivery');

        } catch (error) {
            console.error('同步订单失败:', error);
            alert('同步订单失败: ' + error.message);
        }
    },

    // 配车
    assignVehicle: async function(deliveryId) {
        try {
            const delivery = await window.dbFunctions.getDeliveryManagementById(deliveryId);
            if (!delivery) return;

            // 查找匹配的库存（根据车型、配置、外色、内饰）
            const matchingInventory = this.allInventory.filter(inv =>
                inv.carModel === delivery.carModel &&
                inv.inventoryStatus === '可售' &&
                (!delivery.configuration || inv.version === delivery.configuration) &&
                (!delivery.exteriorColor || inv.exteriorColor === delivery.exteriorColor) &&
                (!delivery.interiorColor || inv.interiorColor === delivery.interiorColor)
            );

            if (matchingInventory.length === 0) {
                alert('没有找到匹配的库存车辆\n匹配条件：\n车型：' + delivery.carModel +
                      '\n配置：' + (delivery.configuration || '任意') +
                      '\n外色：' + (delivery.exteriorColor || '任意') +
                      '\n内饰：' + (delivery.interiorColor || '任意'));
                return;
            }

            // 如果有多个匹配的库存，显示选择列表
            let selectedInventory;
            if (matchingInventory.length === 1) {
                selectedInventory = matchingInventory[0];
            } else {
                // 显示选择弹窗
                const inventoryOptions = matchingInventory.map((inv, index) => {
                    // 计算库龄
                    let stockAge = 0;
                    if (inv.stockDate) {
                        stockAge = Math.floor((new Date() - new Date(inv.stockDate)) / (1000 * 60 * 60 * 24));
                    }

                    return `${index + 1}. VIN: ${inv.vin} | 版本: ${inv.version || '-'} | 外色: ${inv.exteriorColor || '-'} | 内饰: ${inv.interiorColor || '-'} | 位置: ${inv.location || '-'} | 库龄: ${stockAge}天`;
                }).join('\n');

                const choice = prompt(`找到 ${matchingInventory.length} 辆匹配的车辆，请选择：\n${inventoryOptions}\n\n请输入序号 (1-${matchingInventory.length}):`);

                if (!choice || isNaN(choice) || choice < 1 || choice > matchingInventory.length) {
                    alert('取消配车操作');
                    return;
                }

                selectedInventory = matchingInventory[choice - 1];
            }

            // 确认配车
            let stockAge = 0;
            if (selectedInventory.stockDate) {
                stockAge = Math.floor((new Date() - new Date(selectedInventory.stockDate)) / (1000 * 60 * 60 * 24));
            }

            const confirmMsg = `确认配车？\n客户：${delivery.customerName}\nVIN：${selectedInventory.vin}\n车型：${selectedInventory.carModel}\n版本：${selectedInventory.version || '-'}\n位置：${selectedInventory.location || '-'}\n库龄：${stockAge}天`;
            if (!confirm(confirmMsg)) {
                return;
            }

            // 更新配车交付状态
            await window.dbFunctions.updateDeliveryManagement(deliveryId, {
                auditStatus: '已配车',
                vin: selectedInventory.vin
            });

            // 更新库存状态为"已配车"
            await window.dbFunctions.updateInventoryManagement(selectedInventory.id, {
                inventoryStatus: '已配车'
            });

            // 同时更新原订单的审核状态
            if (delivery.orderId) {
                // 使用订单ID进行精确匹配
                await window.dbFunctions.updateOrderManagement(delivery.orderId, {
                    auditStatus: '已配车',
                    vin: selectedInventory.vin
                });
            } else {
                // 降级方案：使用序号查找（为了兼容旧数据）
                const originalOrder = this.allOrders.find(order => order.serialNumber === delivery.serialNumber);
                if (originalOrder) {
                    await window.dbFunctions.updateOrderManagement(originalOrder.id, {
                        auditStatus: '已配车',
                        vin: selectedInventory.vin
                    });
                }
            }

            alert('配车成功！\n车辆已从普通库存移动到已配车库存');
            await this.loadOrders();
            this.switchTab('delivery');

        } catch (error) {
            console.error('配车失败:', error);
            alert('配车失败: ' + error.message);
        }
    },

    // 交付
    deliverVehicle: async function(deliveryId) {
        try {
            const delivery = await window.dbFunctions.getDeliveryManagementById(deliveryId);
            if (!delivery) return;

            // 确认交付
            if (!confirm(`确认交付车辆？\n客户：${delivery.customerName}\nVIN：${delivery.vin || '未配车'}`)) {
                return;
            }

            // 更新配车交付状态
            await window.dbFunctions.updateDeliveryManagement(deliveryId, {
                auditStatus: '已交车',
                deliveryDate: new Date().toISOString().split('T')[0]
            });

            // 如果有VIN，更新对应库存状态为"已交车"
            if (delivery.vin) {
                const inventory = this.allInventory.find(inv => inv.vin === delivery.vin);
                if (inventory) {
                    await window.dbFunctions.updateInventoryManagement(inventory.id, {
                        inventoryStatus: '已交车'
                    });
                }
            }

            // 同时更新原订单的审核状态
            if (delivery.orderId) {
                // 使用订单ID进行精确匹配
                await window.dbFunctions.updateOrderManagement(delivery.orderId, {
                    auditStatus: '已交车',
                    deliveryDate: new Date().toISOString().split('T')[0]
                });
            } else {
                // 降级方案：使用序号查找（为了兼容旧数据）
                const originalOrder = this.allOrders.find(order => order.serialNumber === delivery.serialNumber);
                if (originalOrder) {
                    await window.dbFunctions.updateOrderManagement(originalOrder.id, {
                        auditStatus: '已交车',
                        deliveryDate: new Date().toISOString().split('T')[0]
                    });
                }
            }

            alert('交付成功！\n车辆已移动到已交车状态');
            await this.loadOrders();
            this.switchTab('delivery');

        } catch (error) {
            console.error('交付失败:', error);
            alert('交付失败: ' + error.message);
        }
    },

    // 返配车
    unassignVehicle: async function(deliveryId) {
        try {
            const delivery = await window.dbFunctions.getDeliveryManagementById(deliveryId);
            if (!delivery) return;

            // 确认返配车
            if (!confirm(`确认返配车？\n客户：${delivery.customerName}\nVIN：${delivery.vin || '未知'}\n\n车辆将返回到可售状态`)) {
                return;
            }

            // 如果有VIN，将库存状态改回"可售"
            if (delivery.vin) {
                const inventory = this.allInventory.find(inv => inv.vin === delivery.vin);
                if (inventory) {
                    await window.dbFunctions.updateInventoryManagement(inventory.id, {
                        inventoryStatus: '可售'
                    });
                }
            }

            // 更新配车交付状态
            await window.dbFunctions.updateDeliveryManagement(deliveryId, {
                auditStatus: '已审核', // 返配车后回到已审核状态，等待重新配车
                vin: '' // 清空VIN
            });

            // 同时更新原订单的审核状态，确保数据同步
            if (delivery.orderId) {
                // 使用订单ID进行精确匹配
                await window.dbFunctions.updateOrderManagement(delivery.orderId, {
                    auditStatus: '已审核', // 返配车后回到已审核状态
                    vin: '' // 清空VIN
                });
                console.log(`订单 ${delivery.orderId} 审核状态已更新为：已审核`);
            } else {
                // 降级方案：使用序号查找（为了兼容旧数据）
                const originalOrder = this.allOrders.find(order => order.serialNumber === delivery.serialNumber);
                if (originalOrder) {
                    await window.dbFunctions.updateOrderManagement(originalOrder.id, {
                        auditStatus: '已审核', // 返配车后回到已审核状态
                        vin: '' // 清空VIN
                    });
                    console.log(`订单 ${originalOrder.serialNumber} 审核状态已更新为：已审核`);
                }
            }

            alert('返配车成功！\n车辆已返回到可售库存\n相关订单状态已同步更新');

            // 重新加载所有相关数据，确保各模块数据同步
            await this.loadOrders();

            this.switchTab('delivery');

        } catch (error) {
            console.error('返配车失败:', error);
            alert('返配车失败: ' + error.message);
        }
    },

    // 返交车
    returnDelivery: async function(deliveryId) {
        try {
            const delivery = await window.dbFunctions.getDeliveryManagementById(deliveryId);
            if (!delivery) return;

            // 确认返交车
            if (!confirm(`确认返交车？\n客户：${delivery.customerName}\nVIN：${delivery.vin || '未知'}\n\n车辆将从已交车状态回滚到已配车状态`)) {
                return;
            }

            // 如果有VIN，将库存状态改回"已配车"
            if (delivery.vin) {
                const inventory = this.allInventory.find(inv => inv.vin === delivery.vin);
                if (inventory) {
                    await window.dbFunctions.updateInventoryManagement(inventory.id, {
                        inventoryStatus: '已配车'
                    });
                }
            }

            // 更新配车交付状态
            await window.dbFunctions.updateDeliveryManagement(deliveryId, {
                auditStatus: '已配车', // 返交车后回到已配车状态
                deliveryDate: '' // 清空交付日期
            });

            // 同时更新原订单的审核状态
            if (delivery.orderId) {
                // 使用订单ID进行精确匹配
                await window.dbFunctions.updateOrderManagement(delivery.orderId, {
                    auditStatus: '已配车', // 返交车后回到已配车状态
                    deliveryDate: '' // 清空交付日期
                });
            } else {
                // 降级方案：使用序号查找（为了兼容旧数据）
                const originalOrder = this.allOrders.find(order => order.serialNumber === delivery.serialNumber);
                if (originalOrder) {
                    await window.dbFunctions.updateOrderManagement(originalOrder.id, {
                        auditStatus: '已配车', // 返交车后回到已配车状态
                        deliveryDate: '' // 清空交付日期
                    });
                }
            }

            alert('返交车成功！\n车辆状态已从已交车回滚到已配车');
            await this.loadOrders();
            this.switchTab('delivery');

        } catch (error) {
            console.error('返交车失败:', error);
            alert('返交车失败: ' + error.message);
        }
    },

    // 删除配车交付记录
    deleteDelivery: async function(deliveryId) {
        try {
            const delivery = await window.dbFunctions.getDeliveryManagementById(deliveryId);
            if (!delivery) {
                alert('配车交付记录不存在');
                return;
            }

            // 检查是否可以删除
            if (delivery.auditStatus === '已交车') {
                alert('已交车的订单不能删除，请先返交车');
                return;
            }

            if (!confirm(`确定要删除这条配车交付记录吗？\n客户：${delivery.customerName}\n车型：${delivery.carModel || '未知'}\n\n删除后将无法恢复！`)) {
                return;
            }

            // 如果已配车，需要释放库存
            if (delivery.auditStatus === '已配车' && delivery.vin) {
                const inventory = this.allInventory.find(inv => inv.vin === delivery.vin);
                if (inventory) {
                    await window.dbFunctions.updateInventoryManagement(inventory.id, {
                        inventoryStatus: '可售'
                    });
                }
            }

            // 删除配车交付记录
            await window.dbFunctions.deleteDeliveryManagement(deliveryId);

            // 同时更新原订单状态回到已审核
            if (delivery.orderId) {
                // 使用订单ID进行精确匹配
                await window.dbFunctions.updateOrderManagement(delivery.orderId, {
                    auditStatus: '已审核', // 删除配车交付记录后回到已审核状态
                    vin: '' // 清空VIN
                });
            } else {
                // 降级方案：使用序号查找（为了兼容旧数据）
                const originalOrder = this.allOrders.find(order => order.serialNumber === delivery.serialNumber);
                if (originalOrder) {
                    await window.dbFunctions.updateOrderManagement(originalOrder.id, {
                        auditStatus: '已审核', // 删除配车交付记录后回到已审核状态
                        vin: '' // 清空VIN
                    });
                }
            }

            alert('配车交付记录删除成功！');
            await this.loadOrders();
            this.switchTab('delivery');

        } catch (error) {
            console.error('删除配车交付记录失败:', error);
            alert('删除配车交付记录失败: ' + error.message);
        }
    },

    // 查看配车交付详情
    showDeliveryDetails: async function(deliveryId) {
        try {
            const delivery = await window.dbFunctions.getDeliveryManagementById(deliveryId);
            if (!delivery) {
                alert('配车交付记录不存在');
                return;
            }

            // 查找对应的订单以获取完整的财务信息
            let originalOrder = null;
            if (delivery.orderId) {
                // 使用订单ID进行精确匹配
                originalOrder = this.allOrders.find(order => order.id === delivery.orderId);
            } else if (delivery.serialNumber) {
                // 降级方案：使用序号查找（为了兼容旧数据）
                originalOrder = this.allOrders.find(order => order.serialNumber === delivery.serialNumber);
            }

            // 如果配车交付记录中没有数据，从原订单中获取
            const deposit = delivery.deposit || (originalOrder ? originalOrder.deposit : null);
            const contractPrice = delivery.contractPrice || (originalOrder ? originalOrder.contractPrice : null);
            const options = delivery.options || (originalOrder ? originalOrder.options : null);

            const detailsHtml = `
                <div style="padding: 20px;">
                    <h3 style="margin-bottom: 20px; color: #333;">配车交付详情</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                        <div><strong>订单号：</strong>${delivery.serialNumber || '-'}</div>
                        <div><strong>审核状态：</strong>${this.renderAuditStatusBadge(delivery.auditStatus)}</div>
                        <div><strong>订单日期：</strong>${this.formatDate(delivery.orderDate) || '-'}</div>
                        <div><strong>交付日期：</strong>${this.formatDate(delivery.deliveryDate) || '-'}</div>
                        <div><strong>客户名称：</strong>${delivery.customerName || '-'}</div>
                        <div><strong>联系手机：</strong>${delivery.phone1 || '-'}</div>
                        <div><strong>销售顾问：</strong>${delivery.salesAdvisor || '-'}</div>
                        <div><strong>车型：</strong>${delivery.carModel || '-'}</div>
                        <div><strong>配置：</strong>${delivery.configuration || '-'}</div>
                        <div><strong>外色：</strong>${delivery.exteriorColor || '-'}</div>
                        <div><strong>内饰：</strong>${delivery.interiorColor || '-'}</div>
                        <div><strong>VIN：</strong>${delivery.vin || '-'}</div>
                        <div style="grid-column: 1 / -1;"><strong>选装件：</strong>${options || '-'}</div>
                        <div><strong>定金：</strong>${deposit ? '¥' + deposit : '-'}</div>
                        <div><strong>合同价：</strong>${contractPrice ? '¥' + contractPrice : '-'}</div>
                    </div>

                    ${originalOrder ? `
                        <div style="margin-top: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; font-size: 12px; color: #6c757d;">
                            <strong>数据来源：</strong>财务信息来自订单 ${originalOrder.serialNumber || originalOrder.id}
                        </div>
                    ` : ''}

                    <div style="text-align: right; margin-top: 20px;">
                        <button type="button" class="btn btn-secondary" onclick="document.querySelector('.modal-overlay').remove()">关闭</button>
                    </div>
                </div>
            `;

            this.showModal('配车交付详情', detailsHtml);

        } catch (error) {
            console.error('查看配车交付详情失败:', error);
            alert('查看配车交付详情失败: ' + error.message);
        }
    },

    // 编辑配车交付
    editDelivery: async function(deliveryId) {
        alert('编辑配车交付功能开发中...');
    },

    // 按车型筛选库存
    filterInventoryByModel: function(selectedModel) {
        // 过滤掉已配车状态的车辆
        const availableInventory = this.allInventory.filter(inv =>
            inv.inventoryStatus !== '已配车'
        );

        if (!selectedModel) {
            this.filteredInventory = availableInventory.sort((a, b) => (a.carModel || '').localeCompare(b.carModel || ''));
        } else {
            this.filteredInventory = availableInventory.filter(inv => inv.carModel === selectedModel);
        }

        // 更新显示
        const inventoryContainer = document.getElementById('all-inventory');
        if (inventoryContainer) {
            inventoryContainer.innerHTML = this.renderInventoryList(this.filteredInventory);
        }
    },

    // 按车型筛选已配车库存
    filterPairedInventoryByModel: function(selectedModel) {
        const pairedInventory = this.allInventory.filter(inv =>
            inv.inventoryStatus === '已配车'
        );
        let filteredData;

        if (!selectedModel) {
            filteredData = pairedInventory.sort((a, b) => (a.carModel || '').localeCompare(b.carModel || ''));
        } else {
            filteredData = pairedInventory.filter(inv => inv.carModel === selectedModel);
        }

        // 更新显示
        const inventoryContainer = document.getElementById('paired-inventory-list');
        if (inventoryContainer) {
            inventoryContainer.innerHTML = this.renderInventoryList(filteredData);
        }
    },

    // 按车型筛选已交车
    filterDeliveredInventoryByModel: function(selectedModel) {
        const deliveredInventory = this.allInventory.filter(inv => inv.inventoryStatus === '已交车');
        let filteredData;

        if (!selectedModel) {
            filteredData = deliveredInventory.sort((a, b) => (a.carModel || '').localeCompare(b.carModel || ''));
        } else {
            filteredData = deliveredInventory.filter(inv => inv.carModel === selectedModel);
        }

        // 更新显示
        const inventoryContainer = document.getElementById('delivered-inventory-list');
        if (inventoryContainer) {
            inventoryContainer.innerHTML = this.renderInventoryList(filteredData);
        }
    }
};

// 全局函数
function editOrder(orderId) {
    window.orderFunctions.showOrderForm(orderId);
}

function deleteOrder(orderId) {
    if (confirm('确定要删除这个订单吗？')) {
        window.dbFunctions.deleteOrderManagement(orderId)
            .then(() => {
                alert('订单删除成功');
                window.orderFunctions.loadOrders();
            })
            .catch(error => {
                console.error('删除订单失败:', error);
                alert('删除订单失败: ' + error.message);
            });
    }
}

function editInventory(inventoryId) {
    window.orderFunctions.showInventoryForm(inventoryId);
}

function deleteInventory(inventoryId) {
    if (confirm('确定要删除这个库存记录吗？')) {
        window.dbFunctions.deleteInventoryManagement(inventoryId)
            .then(() => {
                alert('库存删除成功');
                window.orderFunctions.loadOrders();
            })
            .catch(error => {
                console.error('删除库存失败:', error);
                alert('删除库存失败: ' + error.message);
            });
    }
}

function editTarget(targetId, type) {
    alert(`编辑${type}目标功能开发中...`);
}

function deleteTarget(targetId, type) {
    if (confirm('确定要删除这个目标吗？')) {
        let deleteFunction;
        switch(type) {
            case 'delivery':
                deleteFunction = 'deleteDeliveryTarget';
                break;
            case 'order':
                deleteFunction = 'deleteOrderTarget';
                break;
            case 'retail':
                deleteFunction = 'deleteRetailTarget';
                break;
            default:
                alert('未知的目标类型');
                return;
        }

        window.dbFunctions[deleteFunction](targetId)
            .then(() => {
                alert('目标删除成功');
                window.orderFunctions.loadOrders();
            })
            .catch(error => {
                console.error('删除目标失败:', error);
                alert('删除目标失败: ' + error.message);
            });
    }
}

// 查看订单详情
function viewOrder(orderId) {
    window.orderFunctions.showOrderDetails(orderId);
}

// 库存管理导入导出功能扩展
window.orderFunctions.showImportInventoryModal = function() {
    const modalContent = `
        <div class="import-modal">
            <h3><i class="fas fa-upload"></i> 批量导入库存数据</h3>
            <div style="margin: 20px 0;">
                <p style="color: #6c757d; margin-bottom: 15px;">支持CSV和Excel文件格式，请确保文件包含以下字段：</p>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                    <strong>必需字段：</strong> 车型, 入库日期<br>
                    <strong>可选字段：</strong> 库存状态, 版本, 车架号, 外色, 内饰, 原厂选装, 标准, 位置, 生产日期, 发运日期, 指导价, 备注
                </div>
                <input type="file" id="inventory-file-input" accept=".csv,.xlsx,.xls" style="margin-bottom: 15px; width: 100%;">
                <div id="import-progress" style="display: none; margin-bottom: 15px;">
                    <div style="background: #e9ecef; border-radius: 10px; overflow: hidden;">
                        <div id="import-progress-bar" style="background: #4361ee; height: 20px; width: 0%; transition: width 0.3s;"></div>
                    </div>
                    <div id="import-status" style="margin-top: 5px; font-size: 14px;"></div>
                </div>
                <div id="import-preview" style="display: none; max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin-bottom: 15px;">
                    <h4>数据预览：</h4>
                    <div id="preview-content"></div>
                </div>
            </div>
            <div style="text-align: right;">
                <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                <button class="btn btn-primary" id="confirm-import-btn" disabled>确认导入</button>
            </div>
        </div>
    `;

    this.showModal('批量导入', modalContent);

    // 绑定文件选择事件
    const fileInput = document.getElementById('inventory-file-input');
    const confirmBtn = document.getElementById('confirm-import-btn');

    fileInput.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            this.previewImportFile(file, confirmBtn);
        }
    });

    confirmBtn.addEventListener('click', () => {
        const file = fileInput.files[0];
        if (file) {
            this.processImportFile(file);
        }
    });
};

window.orderFunctions.previewImportFile = function(file, confirmBtn) {
    const reader = new FileReader();
    const fileName = file.name.toLowerCase();

    reader.onload = (e) => {
        try {
            let data;
            if (fileName.endsWith('.csv')) {
                data = this.parseCSV(e.target.result);
            } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
                const workbook = XLSX.read(e.target.result, { type: 'binary' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                data = XLSX.utils.sheet_to_json(worksheet);
            }

            if (data && data.length > 0) {
                this.showImportPreview(data);
                confirmBtn.disabled = false;
            } else {
                alert('文件中没有找到有效数据');
            }
        } catch (error) {
            console.error('文件解析失败:', error);
            alert('文件解析失败: ' + error.message);
        }
    };

    if (fileName.endsWith('.csv')) {
        reader.readAsText(file, 'UTF-8');
    } else {
        reader.readAsBinaryString(file);
    }
};

window.orderFunctions.parseCSV = function(csvText) {
    const lines = csvText.split('\n');
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = [];

    for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim()) {
            const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
            const row = {};
            headers.forEach((header, index) => {
                row[header] = values[index] || '';
            });
            data.push(row);
        }
    }

    return data;
};

window.orderFunctions.showImportPreview = function(data) {
    const previewDiv = document.getElementById('import-preview');
    const previewContent = document.getElementById('preview-content');

    if (data.length > 0) {
        const sampleData = data.slice(0, 5); // 显示前5条数据
        let tableHTML = '<table class="table" style="font-size: 12px;"><thead><tr>';

        // 表头
        const headers = Object.keys(sampleData[0]);
        headers.forEach(header => {
            tableHTML += `<th>${header}</th>`;
        });
        tableHTML += '</tr></thead><tbody>';

        // 数据行
        sampleData.forEach(row => {
            tableHTML += '<tr>';
            headers.forEach(header => {
                tableHTML += `<td>${row[header] || ''}</td>`;
            });
            tableHTML += '</tr>';
        });

        tableHTML += '</tbody></table>';
        tableHTML += `<p style="margin-top: 10px; color: #6c757d;">共 ${data.length} 条记录，显示前 ${Math.min(5, data.length)} 条</p>`;

        previewContent.innerHTML = tableHTML;
        previewDiv.style.display = 'block';
    }
};

window.orderFunctions.processImportFile = async function(file) {
    const progressDiv = document.getElementById('import-progress');
    const progressBar = document.getElementById('import-progress-bar');
    const statusDiv = document.getElementById('import-status');

    progressDiv.style.display = 'block';
    progressBar.style.width = '0%';
    statusDiv.textContent = '正在处理文件...';

    try {
        const reader = new FileReader();
        const fileName = file.name.toLowerCase();

        reader.onload = async (e) => {
            try {
                let data;
                if (fileName.endsWith('.csv')) {
                    data = this.parseCSV(e.target.result);
                } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
                    const workbook = XLSX.read(e.target.result, { type: 'binary' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    data = XLSX.utils.sheet_to_json(worksheet);
                }

                await this.importInventoryData(data, progressBar, statusDiv);
            } catch (error) {
                console.error('导入失败:', error);
                statusDiv.textContent = '导入失败: ' + error.message;
                statusDiv.style.color = 'red';
            }
        };

        if (fileName.endsWith('.csv')) {
            reader.readAsText(file, 'UTF-8');
        } else {
            reader.readAsBinaryString(file);
        }
    } catch (error) {
        console.error('文件处理失败:', error);
        statusDiv.textContent = '文件处理失败: ' + error.message;
        statusDiv.style.color = 'red';
    }
};

window.orderFunctions.importInventoryData = async function(data, progressBar, statusDiv) {
    const total = data.length;
    let successful = 0;
    let failed = 0;
    const errors = [];

    statusDiv.textContent = `正在导入 ${total} 条记录...`;

    for (let i = 0; i < total; i++) {
        try {
            const row = data[i];

            // 数据验证和转换
            const inventoryData = this.validateAndTransformInventoryData(row);

            if (inventoryData) {
                await window.dbFunctions.addInventoryManagement(inventoryData);
                successful++;
            } else {
                failed++;
                errors.push(`第 ${i + 1} 行: 缺少必需字段`);
            }
        } catch (error) {
            failed++;
            errors.push(`第 ${i + 1} 行: ${error.message}`);
        }

        // 更新进度
        const progress = ((i + 1) / total) * 100;
        progressBar.style.width = progress + '%';
        statusDiv.textContent = `正在导入... ${i + 1}/${total}`;

        // 让UI有时间更新
        await new Promise(resolve => setTimeout(resolve, 10));
    }

    // 显示结果
    statusDiv.innerHTML = `
        导入完成！<br>
        成功: ${successful} 条<br>
        失败: ${failed} 条
        ${errors.length > 0 ? '<br><br>错误详情:<br>' + errors.slice(0, 5).join('<br>') : ''}
        ${errors.length > 5 ? '<br>...(还有 ' + (errors.length - 5) + ' 个错误)' : ''}
    `;

    if (successful > 0) {
        statusDiv.style.color = 'green';
        // 刷新库存列表
        setTimeout(() => {
            this.loadOrders();
            const modal = document.querySelector('.modal-overlay');
            if (modal) modal.remove();
        }, 2000);
    } else {
        statusDiv.style.color = 'red';
    }
};

window.orderFunctions.validateAndTransformInventoryData = function(row) {
    // 字段映射（支持中英文字段名）
    const fieldMap = {
        '车型': 'carModel',
        '库存状态': 'inventoryStatus',
        '版本': 'version',
        '车架号': 'vin',
        '外色': 'exteriorColor',
        '内饰': 'interiorColor',
        '原厂选装': 'factoryOptions',
        '标准': 'standard',
        '位置': 'location',
        '生产日期': 'productionDate',
        '发运日期': 'shipmentDate',
        '入库日期': 'stockDate',
        '指导价': 'guidePrice',
        '备注': 'notes',
        // 英文字段名
        'carModel': 'carModel',
        'inventoryStatus': 'inventoryStatus',
        'version': 'version',
        'vin': 'vin',
        'exteriorColor': 'exteriorColor',
        'interiorColor': 'interiorColor',
        'factoryOptions': 'factoryOptions',
        'standard': 'standard',
        'location': 'location',
        'productionDate': 'productionDate',
        'shipmentDate': 'shipmentDate',
        'stockDate': 'stockDate',
        'guidePrice': 'guidePrice',
        'notes': 'notes'
    };

    const inventoryData = {
        serialNumber: '',
        inventoryStatus: '可售',
        carModel: '',
        version: '',
        vin: '',
        exteriorColor: '',
        interiorColor: '',
        factoryOptions: '',
        standard: '',
        location: '',
        productionDate: '',
        shipmentDate: '',
        stockDate: new Date().toISOString().split('T')[0],
        stockAge: 0,
        guidePrice: '',
        notes: ''
    };

    // 转换数据
    for (const [key, value] of Object.entries(row)) {
        const mappedField = fieldMap[key];
        if (mappedField && value) {
            inventoryData[mappedField] = String(value).trim();
        }
    }

    // 验证必需字段
    if (!inventoryData.carModel) {
        return null;
    }

    // 日期格式处理
    if (inventoryData.productionDate) {
        inventoryData.productionDate = this.formatDate(inventoryData.productionDate);
    }
    if (inventoryData.shipmentDate) {
        inventoryData.shipmentDate = this.formatDate(inventoryData.shipmentDate);
    }
    if (inventoryData.stockDate) {
        inventoryData.stockDate = this.formatDate(inventoryData.stockDate);
    }

    return inventoryData;
};



window.orderFunctions.exportInventoryData = function() {
    try {
        if (!this.allInventory || this.allInventory.length === 0) {
            alert('没有可导出的库存数据');
            return;
        }

        // 显示导出选项模态框
        const modalContent = `
            <div class="export-modal">
                <h3><i class="fas fa-download"></i> 导出库存数据</h3>
                <div style="margin: 20px 0;">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出格式：</label>
                        <label style="margin-right: 20px;">
                            <input type="radio" name="export-format" value="csv" checked> CSV格式
                        </label>
                        <label>
                            <input type="radio" name="export-format" value="excel"> Excel格式
                        </label>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出字段：</label>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                            <label><input type="checkbox" value="carModel" checked> 车型</label>
                            <label><input type="checkbox" value="inventoryStatus" checked> 库存状态</label>
                            <label><input type="checkbox" value="version" checked> 版本</label>
                            <label><input type="checkbox" value="vin" checked> 车架号</label>
                            <label><input type="checkbox" value="exteriorColor" checked> 外色</label>
                            <label><input type="checkbox" value="interiorColor" checked> 内饰</label>
                            <label><input type="checkbox" value="factoryOptions"> 原厂选装</label>
                            <label><input type="checkbox" value="standard"> 标准</label>
                            <label><input type="checkbox" value="location"> 位置</label>
                            <label><input type="checkbox" value="productionDate"> 生产日期</label>
                            <label><input type="checkbox" value="shipmentDate"> 发运日期</label>
                            <label><input type="checkbox" value="stockDate" checked> 入库日期</label>
                            <label><input type="checkbox" value="stockAge" checked> 库龄</label>
                            <label><input type="checkbox" value="guidePrice"> 指导价</label>
                            <label><input type="checkbox" value="notes"> 备注</label>
                        </div>
                        <div style="margin-top: 10px;">
                            <button type="button" class="btn btn-sm btn-outline" onclick="orderFunctions.selectAllInventoryFields(true)">全选</button>
                            <button type="button" class="btn btn-sm btn-outline" onclick="orderFunctions.selectAllInventoryFields(false)">全不选</button>
                        </div>
                    </div>
                </div>
                <div style="text-align: right;">
                    <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                    <button class="btn btn-primary" id="confirm-export-btn">确认导出</button>
                </div>
            </div>
        `;

        this.showModal('导出数据', modalContent);

        // 绑定导出确认事件
        document.getElementById('confirm-export-btn').addEventListener('click', () => {
            const format = document.querySelector('input[name="export-format"]:checked').value;
            const selectedFields = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                .map(cb => cb.value);

            if (selectedFields.length === 0) {
                alert('请至少选择一个字段');
                return;
            }

            this.performExport(format, selectedFields);
            const modal = document.querySelector('.modal-overlay');
            if (modal) modal.remove();
        });

    } catch (error) {
        console.error('导出失败:', error);
        alert('导出失败: ' + error.message);
    }
};

window.orderFunctions.performExport = function(format, selectedFields) {
    try {
        // 字段名映射
        const fieldNames = {
            carModel: '车型',
            inventoryStatus: '库存状态',
            version: '版本',
            vin: '车架号',
            exteriorColor: '外色',
            interiorColor: '内饰',
            factoryOptions: '原厂选装',
            standard: '标准',
            location: '位置',
            productionDate: '生产日期',
            shipmentDate: '发运日期',
            stockDate: '入库日期',
            stockAge: '库龄',
            guidePrice: '指导价',
            notes: '备注'
        };

        // 准备导出数据
        const exportData = this.allInventory.map(item => {
            const row = {};
            selectedFields.forEach(field => {
                let value = item[field] || '';

                // 特殊处理库龄字段
                if (field === 'stockAge' && item.stockDate) {
                    const stockAge = Math.floor((new Date() - new Date(item.stockDate)) / (1000 * 60 * 60 * 24));
                    value = stockAge + '天';
                }

                row[fieldNames[field]] = value;
            });
            return row;
        });

        if (format === 'csv') {
            this.exportToCSV(exportData);
        } else {
            this.exportToExcel(exportData);
        }

    } catch (error) {
        console.error('导出处理失败:', error);
        alert('导出处理失败: ' + error.message);
    }
};

// 全选/全不选库存导出字段
window.orderFunctions.selectAllInventoryFields = function(selectAll) {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][value]');
    checkboxes.forEach(cb => {
        cb.checked = selectAll;
    });
};

window.orderFunctions.exportToCSV = function(data) {
    if (data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');

    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `库存数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    alert('CSV文件导出成功！');
};

window.orderFunctions.exportToExcel = function(data) {
    if (data.length === 0) return;

    try {
        const worksheet = XLSX.utils.json_to_sheet(data);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Inventory');

        // 设置列宽
        const colWidths = Object.keys(data[0]).map(() => ({ wch: 15 }));
        worksheet['!cols'] = colWidths;

        XLSX.writeFile(workbook, `inventory_data_${new Date().toISOString().split('T')[0]}.xlsx`);
        alert('Excel文件导出成功！');

    } catch (error) {
        console.error('Excel导出失败:', error);
        alert('Excel导出失败，请确保已加载XLSX库: ' + error.message);
    }
}; // 关闭 window.orderFunctions 对象
