// 大括号匹配检查脚本

const fs = require('fs');

console.log('=== 检查orderModule.js大括号匹配 ===');
console.log('');

try {
    const content = fs.readFileSync('orderModule.js', 'utf8');
    const lines = content.split('\n');
    
    let openBraces = 0;
    let closeBraces = 0;
    let braceStack = [];
    
    console.log('逐行检查大括号匹配情况:');
    console.log('');
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const lineNum = i + 1;
        
        // 计算这一行的大括号
        const openCount = (line.match(/\{/g) || []).length;
        const closeCount = (line.match(/\}/g) || []).length;
        
        openBraces += openCount;
        closeBraces += closeCount;
        
        // 记录大括号变化
        for (let j = 0; j < openCount; j++) {
            braceStack.push(lineNum);
        }
        
        for (let j = 0; j < closeCount; j++) {
            if (braceStack.length > 0) {
                braceStack.pop();
            } else {
                console.log(`第${lineNum}行: 多余的闭合大括号 }`);
                console.log(`内容: ${line.trim()}`);
            }
        }
        
        // 显示有大括号变化的行
        if (openCount > 0 || closeCount > 0) {
            const balance = openBraces - closeBraces;
            console.log(`第${lineNum}行: +${openCount} -{closeCount} (余额:${balance}) ${line.trim().substring(0, 50)}${line.trim().length > 50 ? '...' : ''}`);
        }
        
        // 检查最后几行
        if (lineNum >= 4645) {
            console.log(`第${lineNum}行详细: "${line}"`);
        }
    }
    
    console.log('');
    console.log('=== 统计结果 ===');
    console.log(`总开放大括号: ${openBraces}`);
    console.log(`总闭合大括号: ${closeBraces}`);
    console.log(`差值: ${openBraces - closeBraces}`);
    
    if (braceStack.length > 0) {
        console.log(`未匹配的开放大括号在第: ${braceStack.join(', ')}行`);
    }
    
    if (openBraces === closeBraces) {
        console.log('✓ 大括号匹配正确');
    } else {
        console.log('✗ 大括号不匹配');
    }
    
} catch (error) {
    console.log('错误:', error.message);
}

console.log('');
console.log('=== 检查完成 ===');
