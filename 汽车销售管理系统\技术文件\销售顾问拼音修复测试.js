// 销售顾问拼音转换修复测试脚本

function chineseToPinyin(chinese) {
    // 中文字符到拼音首字母的映射表（常见姓名字符）
    const pinyinFirstLetterMap = {
        // 常见姓氏
        '张': 'Z', '王': 'W', '李': 'L', '赵': 'Z', '陈': 'C', '刘': 'L', '杨': 'Y', '黄': 'H',
        '周': 'Z', '吴': 'W', '徐': 'X', '孙': 'S', '马': 'M', '朱': 'Z', '胡': 'H', '郭': 'G',
        '何': 'H', '高': 'G', '林': 'L', '罗': 'L', '郑': 'Z', '梁': 'L', '谢': 'X', '宋': 'S',
        '唐': 'T', '许': 'X', '韩': 'H', '冯': 'F', '邓': 'D', '曹': 'C', '彭': 'P', '曾': 'Z',
        '萧': 'X', '田': 'T', '董': 'D', '袁': 'Y', '潘': 'P', '于': 'Y', '蒋': 'J', '蔡': 'C',
        '余': 'Y', '杜': 'D', '叶': 'Y', '程': 'C', '魏': 'W', '苏': 'S', '吕': 'L', '丁': 'D',
        '任': 'R', '沈': 'S', '姚': 'Y', '卢': 'L', '姜': 'J', '崔': 'C', '钟': 'Z', '谭': 'T',
        '陆': 'L', '汪': 'W', '范': 'F', '金': 'J', '石': 'S', '廖': 'L', '贾': 'J', '夏': 'X',
        '韦': 'W', '付': 'F', '方': 'F', '白': 'B', '邹': 'Z', '孟': 'M', '熊': 'X', '秦': 'Q',
        '邱': 'Q', '江': 'J', '尹': 'Y', '薛': 'X', '闫': 'Y', '段': 'D', '雷': 'L', '侯': 'H',
        '龙': 'L', '史': 'S', '陶': 'T', '黎': 'L', '贺': 'H', '顾': 'G', '毛': 'M', '郝': 'H',
        '龚': 'G', '邵': 'S', '万': 'W', '钱': 'Q', '严': 'Y', '覃': 'Q', '武': 'W', '戴': 'D',
        '莫': 'M', '孔': 'K', '向': 'X', '汤': 'T', '常': 'C', '温': 'W', '康': 'K', '施': 'S',

        // 常见名字字符
        '一': 'Y', '二': 'E', '三': 'S', '四': 'S', '五': 'W', '六': 'L', '七': 'Q', '八': 'B', '九': 'J', '十': 'S',
        '伟': 'W', '芳': 'F', '娜': 'N', '敏': 'M', '静': 'J', '丽': 'L', '强': 'Q', '磊': 'L', '军': 'J', '洋': 'Y',
        '勇': 'Y', '艳': 'Y', '杰': 'J', '涛': 'T', '明': 'M', '超': 'C', '秀': 'X', '英': 'Y', '华': 'H', '慧': 'H',
        '嘉': 'J', '欣': 'X', '雨': 'Y', '雪': 'X', '梅': 'M', '美': 'M', '兰': 'L', '竹': 'Z', '菊': 'J', '松': 'S', '柏': 'B',
        '建': 'J', '国': 'G', '民': 'M', '文': 'W', '武': 'W', '德': 'D', '福': 'F', '贵': 'G', '富': 'F', '康': 'K',
        '安': 'A', '平': 'P', '和': 'H', '顺': 'S', '祥': 'X', '瑞': 'R', '吉': 'J', '利': 'L', '成': 'C', '功': 'G',
        '东': 'D', '南': 'N', '西': 'X', '北': 'B', '中': 'Z', '正': 'Z', '天': 'T', '地': 'D', '人': 'R', '心': 'X',
        '志': 'Z', '远': 'Y', '达': 'D', '通': 'T', '广': 'G', '深': 'S', '长': 'C', '高': 'G', '大': 'D', '小': 'X',
        '新': 'X', '老': 'L', '青': 'Q', '红': 'H', '绿': 'L', '蓝': 'L', '紫': 'Z', '黑': 'H', '白': 'B', '灰': 'H',
        '金': 'J', '银': 'Y', '铜': 'T', '铁': 'T', '钢': 'G', '玉': 'Y', '珠': 'Z', '宝': 'B', '石': 'S', '山': 'S',
        '水': 'S', '火': 'H', '土': 'T', '木': 'M', '花': 'H', '草': 'C', '树': 'S', '林': 'L', '森': 'S', '江': 'J',
        '河': 'H', '湖': 'H', '海': 'H', '洋': 'Y', '波': 'B', '浪': 'L', '云': 'Y', '风': 'F', '雷': 'L', '电': 'D',
        '忍': 'R', '斌': 'B', '诚': 'C', '健': 'J', '洁': 'J', '晓': 'X', '瑜': 'Y', '佳': 'J', '颖': 'Y'
    };

    if (!chinese) return 'XXX';

    let result = '';
    // 处理姓名，通常取前3个字符（姓+名的前两个字）
    for (let i = 0; i < Math.min(chinese.length, 3); i++) {
        const char = chinese[i];
        if (pinyinFirstLetterMap[char]) {
            result += pinyinFirstLetterMap[char];
        } else if (/[A-Za-z]/.test(char)) {
            // 如果是英文字母，直接使用大写
            result += char.toUpperCase();
        } else {
            // 未知字符使用X代替
            result += 'X';
        }
    }

    // 确保结果始终为3位，不足的用X填充
    return result.padEnd(3, 'X').substring(0, 3);
}

function generateOrderNumber(advisorName, orderDate) {
    const date = new Date(orderDate);
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const advisorCode = chineseToPinyin(advisorName);
    
    // 模拟序号（实际应用中从数据库获取）
    const sequence = '01';
    
    return `${year}${month}${day}${advisorCode}${sequence}`;
}

// 测试用例 - 重点测试问题销售顾问
console.log('=== 销售顾问拼音转换修复测试 ===');
console.log('');

const problemCases = [
    { 
        name: '沈洁娜', 
        expected: 'SJN', 
        description: '修复前: SXN → 修复后: SJN',
        issues: ['洁: X→J']
    },
    { 
        name: '石晓瑜', 
        expected: 'SXY', 
        description: '修复前: SXX → 修复后: SXY',
        issues: ['晓: X→X (映射缺失)', '瑜: X→Y']
    },
    { 
        name: '许佳颖', 
        expected: 'XJY', 
        description: '修复前: XXX → 修复后: XJY',
        issues: ['佳: X→J', '颖: X→Y']
    }
];

const testDate = '2025-07-26';

console.log('问题销售顾问拼音转换修复验证:');
console.log('姓名\t\t实际结果\t预期结果\t状态\t\t修复说明');
console.log('----------------------------------------------------------------');

let allCorrect = true;
problemCases.forEach(testCase => {
    const result = chineseToPinyin(testCase.name);
    const isCorrect = result === testCase.expected;
    const status = isCorrect ? '✓ 正确' : '✗ 错误';
    
    if (!isCorrect) allCorrect = false;
    
    console.log(`${testCase.name}\t\t${result}\t\t${testCase.expected}\t\t${status}\t\t${testCase.description}`);
});

console.log('');
console.log(`修复结果: ${allCorrect ? '✓ 全部修复成功' : '✗ 仍存在问题'}`);
console.log('');

// 逐字符分析
console.log('逐字符拼音转换分析:');
console.log('');

problemCases.forEach(testCase => {
    console.log(`${testCase.name} 字符分析:`);
    for (let i = 0; i < testCase.name.length; i++) {
        const char = testCase.name[i];
        const pinyin = chineseToPinyin(char);
        console.log(`  ${char} → ${pinyin}`);
    }
    console.log(`  完整结果: ${chineseToPinyin(testCase.name)}`);
    console.log('');
});

// 订单号生成测试
console.log('订单号生成测试:');
console.log('姓名\t\t拼音代码\t订单号\t\t\t长度\t状态');
console.log('--------------------------------------------------------');

problemCases.forEach(testCase => {
    const pinyinCode = chineseToPinyin(testCase.name);
    const orderNumber = generateOrderNumber(testCase.name, testDate);
    const expectedOrderNumber = `250726${testCase.expected}01`;
    const isCorrect = orderNumber === expectedOrderNumber && orderNumber.length === 11;
    const status = isCorrect ? '✓ 正确' : '✗ 错误';
    
    console.log(`${testCase.name}\t\t${pinyinCode}\t\t${orderNumber}\t${orderNumber.length}位\t${status}`);
});

console.log('');

// 新增字符映射验证
console.log('新增字符映射验证:');
const newCharacters = [
    { char: '洁', expected: 'J', description: 'jie的首字母' },
    { char: '晓', expected: 'X', description: 'xiao的首字母' },
    { char: '瑜', expected: 'Y', description: 'yu的首字母' },
    { char: '佳', expected: 'J', description: 'jia的首字母' },
    { char: '颖', expected: 'Y', description: 'ying的首字母' }
];

console.log('字符\t映射结果\t预期结果\t状态\t\t说明');
console.log('------------------------------------------------');

newCharacters.forEach(item => {
    const result = chineseToPinyin(item.char);
    const isCorrect = result.startsWith(item.expected);
    const status = isCorrect ? '✓ 正确' : '✗ 错误';
    
    console.log(`${item.char}\t${result}\t\t${item.expected}XX\t\t${status}\t\t${item.description}`);
});

console.log('');
console.log('=== 测试完成 ===');
