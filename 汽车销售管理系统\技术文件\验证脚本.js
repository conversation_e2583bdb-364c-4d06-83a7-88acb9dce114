// 展厅录入和线索录入功能验证脚本
// 在浏览器控制台中运行此脚本来验证修复的功能

console.log('开始验证展厅录入和线索录入功能修复...');

// 1. 验证分页配置
function verifyPaginationConfig() {
    console.log('\n=== 验证分页配置 ===');
    
    if (typeof customerFunctions === 'undefined') {
        console.error('❌ customerFunctions 未定义');
        return false;
    }
    
    // 检查分页配置
    if (!customerFunctions.showroomPagination) {
        console.error('❌ 展厅录入分页配置缺失');
        return false;
    }
    
    if (!customerFunctions.leadsPagination) {
        console.error('❌ 线索录入分页配置缺失');
        return false;
    }
    
    if (customerFunctions.showroomPagination.pageSize !== 50) {
        console.error('❌ 展厅录入分页大小配置错误');
        return false;
    }
    
    if (customerFunctions.leadsPagination.pageSize !== 50) {
        console.error('❌ 线索录入分页大小配置错误');
        return false;
    }
    
    console.log('✅ 分页配置验证通过');
    return true;
}

// 2. 验证分页函数
function verifyPaginationFunctions() {
    console.log('\n=== 验证分页函数 ===');
    
    const requiredFunctions = [
        'renderShowroomPagination',
        'renderLeadsPagination', 
        'goToShowroomPage',
        'goToLeadsPage',
        'jumpToShowroomPage',
        'jumpToLeadsPage',
        'refreshShowroomList',
        'refreshLeadsList'
    ];
    
    const missingFunctions = requiredFunctions.filter(func => 
        typeof customerFunctions[func] !== 'function'
    );
    
    if (missingFunctions.length > 0) {
        console.error('❌ 分页函数缺失:', missingFunctions);
        return false;
    }
    
    console.log('✅ 分页函数验证通过');
    return true;
}

// 3. 验证导出功能
function verifyExportFunctions() {
    console.log('\n=== 验证导出功能 ===');
    
    const exportFunctions = [
        'exportShowroomToExcel',
        'exportLeadsToExcel',
        'performShowroomExport',
        'performLeadsExport',
        'selectAllShowroomFields',
        'selectAllLeadsFields'
    ];
    
    const missingFunctions = exportFunctions.filter(func => 
        typeof customerFunctions[func] !== 'function'
    );
    
    if (missingFunctions.length > 0) {
        console.error('❌ 导出函数缺失:', missingFunctions);
        return false;
    }
    
    console.log('✅ 导出功能验证通过');
    return true;
}

// 4. 验证导入功能
function verifyImportFunctions() {
    console.log('\n=== 验证导入功能 ===');
    
    const importFunctions = [
        'handleShowroomImport',
        'handleLeadsImport',
        'mapImportFields'
    ];
    
    const missingFunctions = importFunctions.filter(func => 
        typeof customerFunctions[func] !== 'function'
    );
    
    if (missingFunctions.length > 0) {
        console.error('❌ 导入函数缺失:', missingFunctions);
        return false;
    }
    
    console.log('✅ 导入功能验证通过');
    return true;
}

// 5. 验证字段映射
function verifyFieldMapping() {
    console.log('\n=== 验证字段映射 ===');
    
    // 测试展厅录入字段映射
    const showroomTestRow = {
        '客户名称': '测试客户',
        '电话': '13800138000',
        '来店渠道': '网络平台',
        '性别': '男',
        '意向车型': '测试车型',
        '现有车型': '现有车型',
        '对比车型': '对比车型',
        '置换': '是',
        '试驾': '否',
        '备注': '测试备注'
    };
    
    try {
        const showroomMapped = customerFunctions.mapImportFields(showroomTestRow, 'showroomEntries');
        
        const requiredShowroomFields = ['customerName', 'phone', 'channel', 'gender', 'intendedModels', 'currentModel', 'competitorModel', 'tradeIn', 'testDrive', 'notes'];
        const missingShowroomFields = requiredShowroomFields.filter(field => !showroomMapped.hasOwnProperty(field));
        
        if (missingShowroomFields.length > 0) {
            console.error('❌ 展厅录入字段映射缺失:', missingShowroomFields);
            return false;
        }
        
        console.log('✅ 展厅录入字段映射验证通过');
    } catch (error) {
        console.error('❌ 展厅录入字段映射测试失败:', error.message);
        return false;
    }
    
    // 测试线索录入字段映射（包含新增的isDuplicate字段）
    const leadsTestRow = {
        '录入日期': '2025-01-22',
        '是否有效': '是',
        '是否重复': '否',
        '客户名称': '测试客户',
        '电话': '13800138000',
        '线索ID': '123456',
        '智慧号': '789012'
    };
    
    try {
        const leadsMapped = customerFunctions.mapImportFields(leadsTestRow, 'leadEntries');
        
        const requiredLeadsFields = ['entryDate', 'isValid', 'isDuplicate', 'customerName', 'phone', 'leadId', 'smartNumber'];
        const missingLeadsFields = requiredLeadsFields.filter(field => !leadsMapped.hasOwnProperty(field));
        
        if (missingLeadsFields.length > 0) {
            console.error('❌ 线索录入字段映射缺失:', missingLeadsFields);
            return false;
        }
        
        // 验证isDuplicate字段类型
        if (typeof leadsMapped.isDuplicate !== 'boolean') {
            console.error('❌ isDuplicate字段类型错误，应为boolean');
            return false;
        }
        
        console.log('✅ 线索录入字段映射验证通过（包含新增isDuplicate字段）');
    } catch (error) {
        console.error('❌ 线索录入字段映射测试失败:', error.message);
        return false;
    }
    
    return true;
}

// 6. 验证列配置
function verifyColumnSettings() {
    console.log('\n=== 验证列配置 ===');
    
    if (!customerFunctions.leadsColumnSettings) {
        console.error('❌ 线索录入列配置缺失');
        return false;
    }
    
    if (!customerFunctions.leadsColumnSettings.hasOwnProperty('isDuplicate')) {
        console.error('❌ 线索录入列配置缺少isDuplicate字段');
        return false;
    }
    
    console.log('✅ 列配置验证通过（包含新增isDuplicate字段）');
    return true;
}

// 7. 验证数据库版本
async function verifyDatabaseVersion() {
    console.log('\n=== 验证数据库版本 ===');
    
    try {
        if (typeof db === 'undefined') {
            console.error('❌ 数据库未初始化');
            return false;
        }
        
        // 检查数据库是否包含isDuplicate字段
        const testEntry = {
            entryDate: '2025-01-22',
            isValid: true,
            isDuplicate: false,
            customerName: '测试客户',
            phone: '13800138000'
        };
        
        // 尝试添加测试数据
        const id = await db.leadEntries.add(testEntry);
        
        // 读取数据验证字段
        const savedEntry = await db.leadEntries.get(id);
        
        if (!savedEntry.hasOwnProperty('isDuplicate')) {
            console.error('❌ 数据库表结构缺少isDuplicate字段');
            return false;
        }
        
        // 清理测试数据
        await db.leadEntries.delete(id);
        
        console.log('✅ 数据库版本验证通过（包含isDuplicate字段）');
        return true;
    } catch (error) {
        console.error('❌ 数据库版本验证失败:', error.message);
        return false;
    }
}

// 主验证函数
async function runAllVerifications() {
    console.log('🚀 开始全面验证...\n');
    
    const results = {
        pagination: verifyPaginationConfig() && verifyPaginationFunctions(),
        export: verifyExportFunctions(),
        import: verifyImportFunctions(),
        fieldMapping: verifyFieldMapping(),
        columnSettings: verifyColumnSettings(),
        database: await verifyDatabaseVersion()
    };
    
    console.log('\n=== 验证结果汇总 ===');
    console.log('分页功能:', results.pagination ? '✅ 通过' : '❌ 失败');
    console.log('导出功能:', results.export ? '✅ 通过' : '❌ 失败');
    console.log('导入功能:', results.import ? '✅ 通过' : '❌ 失败');
    console.log('字段映射:', results.fieldMapping ? '✅ 通过' : '❌ 失败');
    console.log('列配置:', results.columnSettings ? '✅ 通过' : '❌ 失败');
    console.log('数据库版本:', results.database ? '✅ 通过' : '❌ 失败');
    
    const allPassed = Object.values(results).every(result => result === true);
    
    if (allPassed) {
        console.log('\n🎉 所有验证都通过！功能修复成功！');
    } else {
        console.log('\n⚠️ 部分验证失败，请检查相关功能。');
    }
    
    return results;
}

// 导出验证函数供外部调用
if (typeof window !== 'undefined') {
    window.verifyFunctions = {
        runAllVerifications,
        verifyPaginationConfig,
        verifyPaginationFunctions,
        verifyExportFunctions,
        verifyImportFunctions,
        verifyFieldMapping,
        verifyColumnSettings,
        verifyDatabaseVersion
    };
}

// 如果在Node.js环境中，直接运行验证
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        runAllVerifications,
        verifyPaginationConfig,
        verifyPaginationFunctions,
        verifyExportFunctions,
        verifyImportFunctions,
        verifyFieldMapping,
        verifyColumnSettings,
        verifyDatabaseVersion
    };
}

console.log('验证脚本加载完成。在浏览器控制台中运行 verifyFunctions.runAllVerifications() 来开始验证。');
