From: <Saved by Blink>
Snapshot-Content-Location: file:///D:/Users/<USER>/Desktop/V1.1FJ/salesanalytics.html
Subject: =?utf-8?Q?=E9=94=80=E5=94=AE=E5=88=86=E6=9E=90=20-=20=E6=B1=BD=E8=BD=A6?=
 =?utf-8?Q?=E9=94=80=E5=94=AE=E6=BC=8F=E6=96=97=E7=AE=A1=E7=90=86=E7=B3=BB?=
 =?utf-8?Q?=E7=BB=9F?=
Date: Fri, 1 Aug 2025 13:59:40 +0800
MIME-Version: 1.0
Content-Type: multipart/related;
	type="text/html";
	boundary="----MultipartBoundary--tCdgLTemwq0jS7q9jNLfyJgUywxjTTSi7GqPcLUxlP----"


------MultipartBoundary--tCdgLTemwq0jS7q9jNLfyJgUywxjTTSi7GqPcLUxlP----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: file:///D:/Users/<USER>/Desktop/V1.1FJ/salesanalytics.html

<!DOCTYPE html><html lang=3D"zh-CN"><head><meta http-equiv=3D"Content-Type"=
 content=3D"text/html; charset=3DUTF-8"><link rel=3D"stylesheet" type=3D"te=
xt/css" href=3D"cid:<EMAIL>" /=
>
   =20
    <meta name=3D"viewport" content=3D"width=3Ddevice-width, initial-scale=
=3D1">
    <title>=E9=94=80=E5=94=AE=E5=88=86=E6=9E=90 - =E6=B1=BD=E8=BD=A6=E9=94=
=80=E5=94=AE=E6=BC=8F=E6=96=97=E7=AE=A1=E7=90=86=E7=B3=BB=E7=BB=9F</title>
    <link rel=3D"stylesheet" href=3D"https://cdnjs.cloudflare.com/ajax/libs=
/font-awesome/6.4.0/css/all.min.css">
   =20
   =20
   =20
    <link rel=3D"stylesheet" href=3D"file:///D:/Users/<USER>/Desktop/=
V1.1FJ/styles.css">
   =20
</head>
<body style=3D"height: 100vh; margin: 0;">
    <div class=3D"container" style=3D"flex: 1; display: flex; flex-directio=
n: column; height: 100%;">
        <header style=3D"background: linear-gradient(135deg, #4361ee, #3f37=
c9); color: white; padding: 15px 30px; display: flex; justify-content: spac=
e-between; align-items: center; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);=
">
            <div class=3D"logo" style=3D"display: flex; align-items: center=
; gap: 15px;">
                <i class=3D"fas fa-chart-line" style=3D"font-size: 28px;"><=
/i>
                <h1 style=3D"font-size: 22px; font-weight: 600;">=E9=94=80=
=E5=94=AE=E5=88=86=E6=9E=90</h1>
            </div>
            <div class=3D"user-info" style=3D"display: flex; align-items: c=
enter; gap: 12px;">
                <div class=3D"user-avatar" style=3D"width: 42px; height: 42=
px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex=
; align-items: center; justify-content: center; font-weight: bold; font-siz=
e: 18px;">=E5=BC=A0</div>
                <div>
                    <div>=E5=BC=A0=E7=BB=8F=E7=90=86</div>
                    <div style=3D"font-size: 13px; opacity: 0.8;">=E9=94=80=
=E5=94=AE=E4=B8=BB=E7=AE=A1</div>
                </div>
            </div>
        </header>

        <div class=3D"main-content" style=3D"flex: 1; display: flex; overfl=
ow: hidden;">
            <div class=3D"sidebar" style=3D"width: 260px; background: white=
; padding: 25px 20px; display: flex; flex-direction: column; box-shadow: 0 =
4px 12px rgba(0, 0, 0, 0.08); z-index: 10; height: 100%; overflow-y: auto;"=
>
                <div style=3D"text-align: center; margin-bottom: 20px;">
                    <div style=3D"font-size: 13px; color: #6c757d; margin-b=
ottom: 5px;">=E6=95=B0=E6=8D=AE=E6=A6=82=E8=A7=88</div>
                    <div style=3D"font-size: 28px; font-weight: 700; color:=
 #3f37c9;" id=3D"total-data-points">0</div>
                    <div style=3D"font-size: 14px; color: #6c757d;">=E5=88=
=86=E6=9E=90=E7=BB=B4=E5=BA=A6</div>
                </div>

                <ul class=3D"nav-menu" style=3D"list-style: none; margin-to=
p: 20px;">
                    <li style=3D"margin-bottom: 8px;"><a href=3D"file:///D:=
/Users/<USER>/Desktop/V1.1FJ/index.html" style=3D"display: flex; alig=
n-items: center; padding: 12px 15px; text-decoration: none; color: #212529;=
 border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weigh=
t: 500;"><i class=3D"fas fa-home" style=3D"margin-right: 12px; width: 20px;=
 text-align: center; color: #6c757d;"></i> =E9=A6=96=E9=A1=B5</a></li>
                    <li style=3D"margin-bottom: 8px;"><a href=3D"file:///D:=
/Users/<USER>/Desktop/V1.1FJ/salesanalytics.html" class=3D"active" st=
yle=3D"display: flex; align-items: center; padding: 12px 15px; text-decorat=
ion: none; color: #4361ee; border-radius: 8px; transition: all 0.3s ease; f=
ont-size: 15px; font-weight: 500;"><i class=3D"fas fa-chart-line" style=3D"=
margin-right: 12px; width: 20px; text-align: center; color: #4361ee;"></i> =
=E9=94=80=E5=94=AE=E5=88=86=E6=9E=90</a></li>
                    <li style=3D"margin-bottom: 8px;"><a href=3D"file:///D:=
/Users/<USER>/Desktop/V1.1FJ/customers.html" style=3D"display: flex; =
align-items: center; padding: 12px 15px; text-decoration: none; color: #212=
529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-w=
eight: 500;"><i class=3D"fas fa-users" style=3D"margin-right: 12px; width: =
20px; text-align: center; color: #6c757d;"></i> =E5=AE=A2=E6=88=B7=E7=AE=A1=
=E7=90=86</a></li>
                    <li style=3D"margin-bottom: 8px;"><a href=3D"file:///D:=
/Users/<USER>/Desktop/V1.1FJ/order.html" style=3D"display: flex; alig=
n-items: center; padding: 12px 15px; text-decoration: none; color: #212529;=
 border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weigh=
t: 500;"><i class=3D"fas fa-shopping-cart" style=3D"margin-right: 12px; wid=
th: 20px; text-align: center; color: #6c757d;"></i> =E8=AE=A2=E5=8D=95=E7=
=AE=A1=E7=90=86</a></li>
                    <li style=3D"margin-bottom: 8px;"><a href=3D"file:///D:=
/Users/<USER>/Desktop/V1.1FJ/parts.html" style=3D"display: flex; alig=
n-items: center; padding: 12px 15px; text-decoration: none; color: #212529;=
 border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weigh=
t: 500;"><i class=3D"fas fa-boxes" style=3D"margin-right: 12px; width: 20px=
; text-align: center; color: #6c757d;"></i> =E9=85=8D=E4=BB=B6=E5=BA=93=E7=
=AE=A1=E7=90=86</a></li>
                    <li style=3D"margin-bottom: 8px;"><a href=3D"file:///D:=
/Users/<USER>/Desktop/V1.1FJ/settings.html" style=3D"display: flex; a=
lign-items: center; padding: 12px 15px; text-decoration: none; color: #2125=
29; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-we=
ight: 500;"><i class=3D"fas fa-cog" style=3D"margin-right: 12px; width: 20p=
x; text-align: center; color: #6c757d;"></i> =E7=B3=BB=E7=BB=9F=E8=AE=BE=E7=
=BD=AE</a></li>
                    <li style=3D"margin-bottom: 8px;"><a href=3D"file:///D:=
/Users/<USER>/Desktop/V1.1FJ/user.html" style=3D"display: flex; align=
-items: center; padding: 12px 15px; text-decoration: none; color: #212529; =
border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight=
: 500;"><i class=3D"fas fa-users-cog" style=3D"margin-right: 12px; width: 2=
0px; text-align: center; color: #6c757d;"></i> =E7=94=A8=E6=88=B7=E7=AE=A1=
=E7=90=86</a></li>
                </ul>

                <div style=3D"margin-top: 30px; background: #f8f9fa; paddin=
g: 15px; border-radius: 8px;">
                    <h3 style=3D"font-size: 15px; margin-bottom: 10px; disp=
lay: flex; align-items: center; gap: 10px;">
                        <i class=3D"fas fa-calendar-alt" style=3D"color: #3=
f37c9;"></i> =E5=BF=AB=E9=80=9F=E7=AD=9B=E9=80=89
                    </h3>
                    <div style=3D"display: flex; flex-direction: column; ga=
p: 8px;">
                        <button class=3D"btn btn-outline quick-filter-btn" =
data-period=3D"today">=E4=BB=8A=E6=97=A5=E6=95=B0=E6=8D=AE</button>
                        <button class=3D"btn btn-outline quick-filter-btn" =
data-period=3D"week">=E6=9C=AC=E5=91=A8=E6=95=B0=E6=8D=AE</button>
                        <button class=3D"btn btn-outline quick-filter-btn" =
data-period=3D"month">=E6=9C=AC=E6=9C=88=E6=95=B0=E6=8D=AE</button>
                        <button class=3D"btn btn-outline quick-filter-btn" =
data-period=3D"quarter">=E6=9C=AC=E5=AD=A3=E5=BA=A6=E6=95=B0=E6=8D=AE</butt=
on>
                    </div>
                </div>
            </div>

            <div class=3D"content-area" style=3D"flex: 1; padding: 20px; ov=
erflow-y: auto; background: #f5f7fb;">
                <div id=3D"analytics-content">
                    <!-- =E9=94=80=E5=94=AE=E5=88=86=E6=9E=90=E5=86=85=E5=
=AE=B9 -->
                    <div class=3D"analytics-dashboard">
                        <!-- =E6=97=B6=E9=97=B4=E7=AD=9B=E9=80=89=E5=8C=BA=
=E5=9F=9F -->
                        <div class=3D"time-filter-section">
                            <div class=3D"filter-controls">
                                <label>=E6=97=B6=E9=97=B4=E8=8C=83=E5=9B=B4=
=EF=BC=9A</label>
                                <select id=3D"time-range-select">
                                    <option value=3D"=E6=9C=AC=E6=9C=88">=
=E6=9C=AC=E6=9C=88</option>
                                    <option value=3D"=E4=B8=8A=E6=9C=88">=
=E4=B8=8A=E6=9C=88</option>
                                    <option value=3D"=E6=9C=AC=E5=AD=A3=E5=
=BA=A6">=E6=9C=AC=E5=AD=A3=E5=BA=A6</option>
                                    <option value=3D"=E6=9C=AC=E5=B9=B4=E5=
=BA=A6">=E6=9C=AC=E5=B9=B4=E5=BA=A6</option>
                                </select>
                            </div>
                        </div>

                        <!-- =E7=AC=AC=E4=B8=80=E8=A1=8C=EF=BC=9A=E5=B1=95=
=E5=8E=85=E5=AE=A2=E6=B5=81=E3=80=81=E9=94=80=E5=94=AE=E9=A1=BE=E9=97=AE=E6=
=8E=92=E5=90=8D=E3=80=81=E9=94=80=E5=94=AE=E9=A1=BE=E9=97=AE=E6=88=90=E6=9E=
=9C=E5=88=86=E6=9E=90 -->
                        <div class=3D"analytics-row" style=3D"display: grid=
; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <!-- =E5=B1=95=E5=8E=85=E5=AE=A2=E6=B5=81 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-users"></i> =E5=
=B1=95=E5=8E=85=E5=AE=A2=E6=B5=81</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"showroom-traffic-table">
            <table style=3D"width: 100%; border-collapse: collapse; font-si=
ze: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style=3D"background: #f1f3f4; color: #333;">
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: left; font-weight: 600;">=E5=AE=A2=E6=B5=81=E7=B1=BB=E5=9E=
=8B</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E4=BB=8A=E6=97=A5</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E6=9C=AC=E6=9C=88</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E4=B8=8A=E6=9C=88</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E7=8E=AF=E6=AF=94</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E5=85=A8=E5=B9=B4</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E6=9C=88=E5=8D=A0=E6=AF=94</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style=3D"background: white;">
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; color: #333; font-weight: 600;">=E9=A6=96=E6=AC=A1=E5=88=B0=E5=BA=97</t=
d>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333; font-weight: 600;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #666;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #28a745;">
                            +0%
                        </td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333;">0%</td>
                    </tr>
                    <tr style=3D"background: #fafafa;">
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; color: #333; font-weight: 600;">=E5=86=8D=E6=AC=A1=E5=88=B0=E5=BA=97</t=
d>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333; font-weight: 600;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #666;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #28a745;">
                            +0%
                        </td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333;">0%</td>
                    </tr>
                    <tr style=3D"background: white; border-top: 2px solid #=
ddd;">
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; color: #333; font-weight: 700;">=E6=80=BB=E8=AE=A1</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333; font-weight: 600;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333; font-weight: 700;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #666;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #28a745; font-weight: 600;">
                            +0%
                        </td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333; font-weight: 600;">0</td>
                        <td style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; color: #333;">100%</td>
                    </tr>
                </tbody>
            </table>
            <div style=3D"margin-top: 10px; padding: 8px; background: #f8f9=
fa; border-radius: 4px;">
                <div style=3D"display: grid; grid-template-columns: repeat(=
3, 1fr); gap: 8px; font-size: 10px;">
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E9=
=A6=96=E6=AC=A1=E5=AE=A2=E6=B5=81=E8=BD=AC=E5=8C=96=E7=8E=87</div>
                        <div style=3D"color: #333; font-weight: 600;">0%</d=
iv>
                    </div>
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E5=
=86=8D=E6=AC=A1=E5=AE=A2=E6=B5=81=E8=BD=AC=E5=8C=96=E7=8E=87</div>
                        <div style=3D"color: #333; font-weight: 600;">0%</d=
iv>
                    </div>
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E6=
=9C=88=E5=BA=A6=E6=80=BB=E5=A2=9E=E9=95=BF</div>
                        <div style=3D"color: #28a745; font-weight: 600;">
                            +0%
                        </div>
                    </div>
                </div>
                <div style=3D"margin-top: 8px; padding-top: 8px; border-top=
: 1px solid #ddd; font-size: 10px; color: #666; text-align: center;">
                    =E6=95=B0=E6=8D=AE=E6=9B=B4=E6=96=B0=E6=97=B6=E9=97=B4:=
 2025/8/1 13:58:33
                </div>
            </div>
        </div>
                                </div>
                            </div>

                            <!-- =E9=94=80=E5=94=AE=E9=A1=BE=E9=97=AE=E6=8E=
=92=E5=90=8D -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-trophy"></i> =E9=
=94=80=E5=94=AE=E9=A1=BE=E9=97=AE=E6=8E=92=E5=90=8D</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div class=3D"chart-container">
                                        <canvas id=3D"advisor-ranking-chart=
"></canvas>
                                    </div>
                                </div>
                            </div>

                            <!-- =E9=94=80=E5=94=AE=E9=A1=BE=E9=97=AE=E6=88=
=90=E6=9E=9C=E5=88=86=E6=9E=90 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-chart-pie"></i> =
=E9=94=80=E5=94=AE=E9=A1=BE=E9=97=AE=E6=88=90=E6=9E=9C=E5=88=86=E6=9E=90</h=
3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"advisor-performance-table">
            <table style=3D"width: 100%; border-collapse: collapse; font-si=
ze: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style=3D"background: #f1f3f4; color: #333;">
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: left; font-weight: 600;">=E9=94=80=E5=94=AE=E9=A1=BE=E9=97=
=AE</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">A</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">B</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">C</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">F</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E5=90=88=E8=AE=A1</th>
                    </tr>
                </thead>
                <tbody>
                   =20
                        <tr style=3D"background: white;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333;">=E6=B2=88=E6=B4=81=E5=A8=9C</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">58</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">54</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">65</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333; font-weight: 600;">177</td>
                        </tr>
                   =20
                        <tr style=3D"background: #fafafa;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333;">=E7=9F=B3=E6=99=93=E7=91=9C</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">108</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">12</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">38</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333; font-weight: 600;">158</td>
                        </tr>
                   =20
                        <tr style=3D"background: white;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333;">=E6=9E=97=E5=BF=8D=E6=96=8C</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">69</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">48</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">38</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333; font-weight: 600;">155</td>
                        </tr>
                   =20
                        <tr style=3D"background: #fafafa;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333;">=E5=85=B6=E4=BB=96</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">47</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">18</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">3</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">25</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333; font-weight: 600;">93</td>
                        </tr>
                   =20
                        <tr style=3D"background: white;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333;">=E8=AE=B8=E4=BD=B3=E9=A2=96</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">24</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">23</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">16</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333; font-weight: 600;">63</td>
                        </tr>
                   =20
                </tbody>
            </table>
            <div style=3D"margin-top: 10px; padding: 8px; background: #f8f9=
fa; border-radius: 4px;">
                <div style=3D"display: grid; grid-template-columns: repeat(=
6, 1fr); gap: 8px; font-size: 10px;">
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E9=
=94=80=E5=94=AE=E9=A1=BE=E9=97=AE</div>
                        <div style=3D"color: #333; font-weight: 600;">=E9=
=A6=96=E6=AC=A1</div>
                        <div style=3D"color: #333;">=E5=86=8D=E6=AC=A1</div=
>
                        <div style=3D"color: #333;">=E7=8E=8B=E4=BA=94</div=
>
                    </div>
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E9=
=A6=96=E6=AC=A1=E5=88=B0=E5=BA=97</div>
                        <div style=3D"color: #333; font-weight: 600;">H</di=
v>
                        <div style=3D"color: #333;">=E7=A7=91=E5=AE=A4</div=
>
                        <div style=3D"color: #333;">=E9=85=8D=E8=BD=A6</div=
>
                    </div>
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E6=
=8E=A5=E5=BE=85=E6=97=B6=E9=97=B4</div>
                        <div style=3D"color: #333; font-weight: 600;">A</di=
v>
                        <div style=3D"color: #333;">=E9=85=8D=E8=BD=A6=E7=
=B1=BB=E5=9E=8B</div>
                        <div style=3D"color: #333;">=E6=84=8F=E5=90=91=E8=
=BD=A6</div>
                    </div>
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E6=
=84=8F=E5=90=91=E8=BD=A6</div>
                        <div style=3D"color: #333; font-weight: 600;">B</di=
v>
                        <div style=3D"color: #333;">=E8=AF=95=E9=A9=BE=E8=
=BD=A6</div>
                        <div style=3D"color: #333;">=E6=88=90=E4=BA=A4=E7=
=8E=87</div>
                    </div>
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E8=
=AF=95=E9=A9=BE=E8=BD=A6</div>
                        <div style=3D"color: #333; font-weight: 600;">C</di=
v>
                        <div style=3D"color: #333;">=E8=AF=95=E9=A9=BE=E7=
=8E=87</div>
                        <div style=3D"color: #333;">=E6=88=90=E4=BA=A4=E7=
=8E=87</div>
                    </div>
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E6=
=88=90=E4=BA=A4=E7=8E=87</div>
                        <div style=3D"color: #333; font-weight: 600;">=E5=
=90=88=E8=AE=A1</div>
                        <div style=3D"color: #333;">=E6=88=90=E4=BA=A4=E7=
=8E=87</div>
                        <div style=3D"color: #333;">=E6=88=90=E4=BA=A4=E7=
=8E=87</div>
                    </div>
                </div>
            </div>
        </div>
                                </div>
                            </div>
                        </div>

                        <!-- =E7=AC=AC=E4=BA=8C=E8=A1=8C=EF=BC=9A=E9=94=80=
=E5=94=AE=E6=BC=8F=E6=96=97=E6=A6=82=E8=A7=88=E3=80=81=E7=BA=BF=E7=B4=A2=E6=
=BC=8F=E6=96=97=E6=A6=82=E8=A7=88=E3=80=81=E7=BA=BF=E7=B4=A2=E6=B8=A0=E9=81=
=93 -->
                        <div class=3D"analytics-row" style=3D"display: grid=
; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <!-- =E9=94=80=E5=94=AE=E6=BC=8F=E6=96=97=E6=A6=
=82=E8=A7=88 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-filter"></i> =E9=
=94=80=E5=94=AE=E6=BC=8F=E6=96=97=E6=A6=82=E8=A7=88</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"sales-funnel-container"></di=
v>
                                </div>
                            </div>

                            <!-- =E7=BA=BF=E7=B4=A2=E6=BC=8F=E6=96=97=E6=A6=
=82=E8=A7=88 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-filter"></i> =E7=
=BA=BF=E7=B4=A2=E6=BC=8F=E6=96=97=E6=A6=82=E8=A7=88</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"lead-funnel-container"></div=
>
                                </div>
                            </div>

                            <!-- =E7=BA=BF=E7=B4=A2=E6=B8=A0=E9=81=93 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-chart-bar"></i> =
=E7=BA=BF=E7=B4=A2=E6=B8=A0=E9=81=93</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div class=3D"chart-container">
                                        <canvas id=3D"lead-channel-chart"><=
/canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- =E7=AC=AC=E4=B8=89=E8=A1=8C=EF=BC=9A=E9=94=80=
=E5=94=AE=E8=B6=8B=E5=8A=BF=E3=80=81=E7=BA=BF=E7=B4=A2=E8=B6=8B=E5=8A=BF --=
>
                        <div class=3D"analytics-row" style=3D"display: grid=
; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- =E9=94=80=E5=94=AE=E8=B6=8B=E5=8A=BF -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-chart-line"></i>=
 =E9=94=80=E5=94=AE=E8=B6=8B=E5=8A=BF</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div class=3D"chart-container">
                                        <canvas id=3D"sales-trend-chart"></=
canvas>
                                    </div>
                                </div>
                            </div>

                            <!-- =E7=BA=BF=E7=B4=A2=E8=B6=8B=E5=8A=BF -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-chart-line"></i>=
 =E7=BA=BF=E7=B4=A2=E8=B6=8B=E5=8A=BF</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div class=3D"chart-container">
                                        <canvas id=3D"lead-trend-chart"></c=
anvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- =E7=AC=AC=E5=9B=9B=E8=A1=8C=EF=BC=9A=E8=BD=A6=
=E5=9E=8B=E6=84=8F=E5=90=91=E5=88=86=E6=9E=90=E3=80=81=E8=AF=95=E9=A9=BE=E7=
=8E=87=E5=88=86=E6=9E=90 -->
                        <div class=3D"analytics-row" style=3D"display: grid=
; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- =E8=BD=A6=E5=9E=8B=E6=84=8F=E5=90=91=E5=88=
=86=E6=9E=90 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-car"></i> =E8=BD=
=A6=E5=9E=8B=E6=84=8F=E5=90=91=E5=88=86=E6=9E=90</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"vehicle-intention-table">
            <table style=3D"width: 100%; border-collapse: collapse; font-si=
ze: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style=3D"background: #f1f3f4; color: #333;">
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: left; font-weight: 600;">=E8=BD=A6=E5=9E=8B</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E4=BB=8A=E6=97=A5</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E6=9C=AC=E6=9C=88</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E5=85=A8=E5=B9=B4</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E8=AF=95=E9=A9=BE=E7=8E=87</th>
                    </tr>
                </thead>
                <tbody>
                   =20
                        <tr style=3D"background: white;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333; font-weight: 600;">=E5=86=92=E9=99=A9=E5=AE=B6</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">157</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #dc3545; font-weight: 600;">
                                0%
                            </td>
                        </tr>
                   =20
                        <tr style=3D"background: #fafafa;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333; font-weight: 600;">=E6=9E=97=E8=82=AFZ</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">209</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #dc3545; font-weight: 600;">
                                0%
                            </td>
                        </tr>
                   =20
                        <tr style=3D"background: white;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333; font-weight: 600;">=E8=88=AA=E6=B5=B7=E5=AE=B6</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">248</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #dc3545; font-weight: 600;">
                                0%
                            </td>
                        </tr>
                   =20
                        <tr style=3D"background: #fafafa;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333; font-weight: 600;">=E9=A3=9E=E8=A1=8C=E5=AE=B6</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">49</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #dc3545; font-weight: 600;">
                                0%
                            </td>
                        </tr>
                   =20
                        <tr style=3D"background: white;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333; font-weight: 600;">=E9=A2=86=E8=88=AA=E5=91=98</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">4</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #dc3545; font-weight: 600;">
                                0%
                            </td>
                        </tr>
                   =20
                </tbody>
            </table>
            <div style=3D"margin-top: 10px; padding: 8px; background: #f8f9=
fa; border-radius: 4px;">
                <div style=3D"display: grid; grid-template-columns: repeat(=
3, 1fr); gap: 8px; font-size: 10px;">
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E7=
=83=AD=E9=97=A8=E8=BD=A6=E5=9E=8B</div>
                        <div style=3D"color: #333; font-weight: 600;">=E5=
=86=92=E9=99=A9=E5=AE=B6</div>
                    </div>
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E5=
=B9=B3=E5=9D=87=E8=AF=95=E9=A9=BE=E7=8E=87</div>
                        <div style=3D"color: #333; font-weight: 600;">
                            0%
                        </div>
                    </div>
                    <div style=3D"text-align: center;">
                        <div style=3D"color: #666; margin-bottom: 2px;">=E8=
=BD=A6=E5=9E=8B=E6=80=BB=E6=95=B0</div>
                        <div style=3D"color: #333; font-weight: 600;">5</di=
v>
                    </div>
                </div>
                <div style=3D"margin-top: 8px; padding-top: 8px; border-top=
: 1px solid #ddd; font-size: 10px; color: #666; text-align: center;">
                    =E6=95=B0=E6=8D=AE=E6=9B=B4=E6=96=B0=E6=97=B6=E9=97=B4:=
 2025/8/1 13:58:33
                </div>
            </div>
        </div>
                                </div>
                            </div>

                            <!-- =E8=AF=95=E9=A9=BE=E7=8E=87=E5=88=86=E6=9E=
=90 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-key"></i> =E8=AF=
=95=E9=A9=BE=E7=8E=87=E5=88=86=E6=9E=90</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"test-drive-analysis">
            <div style=3D"padding: 20px;">
                <!-- =E8=AF=95=E9=A9=BE=E7=8E=87=E6=A6=82=E8=A7=88 -->
                <div style=3D"display: grid; grid-template-columns: 1fr 1fr=
; gap: 20px; margin-bottom: 20px;">
                    <div style=3D"text-align: center; padding: 15px; backgr=
ound: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px=
; color: white;">
                        <div style=3D"font-size: 24px; font-weight: bold; m=
argin-bottom: 5px;">0%</div>
                        <div style=3D"font-size: 12px; opacity: 0.9;">=E6=
=9C=AC=E6=9C=88=E8=AF=95=E9=A9=BE=E7=8E=87</div>
                        <div style=3D"font-size: 10px; opacity: 0.8; margin=
-top: 5px;">0/0</div>
                    </div>
                    <div style=3D"text-align: center; padding: 15px; backgr=
ound: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 8px=
; color: white;">
                        <div style=3D"font-size: 24px; font-weight: bold; m=
argin-bottom: 5px;">28%</div>
                        <div style=3D"font-size: 12px; opacity: 0.9;">=E5=
=85=A8=E5=B9=B4=E8=AF=95=E9=A9=BE=E7=8E=87</div>
                        <div style=3D"font-size: 10px; opacity: 0.8; margin=
-top: 5px;">185/667</div>
                    </div>
                </div>

                <!-- =E8=AF=95=E9=A9=BE=E7=8E=87=E5=88=86=E6=9E=90=E8=A1=A8=
=E6=A0=BC -->
                <table style=3D"width: 100%; border-collapse: collapse; fon=
t-size: 11px; font-family: Arial, sans-serif;">
                    <thead>
                        <tr style=3D"background: #f1f3f4; color: #333;">
                            <th style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: left; font-weight: 600;">=E6=8C=87=E6=A0=87</th>
                            <th style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; font-weight: 600;">=E6=9C=AC=E6=9C=88</th>
                            <th style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; font-weight: 600;">=E5=85=A8=E5=B9=B4</th>
                            <th style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; font-weight: 600;">=E5=AE=8C=E6=88=90=E7=8E=87<=
/th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style=3D"background: white;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333; font-weight: 600;">=E6=80=BB=E5=AE=A2=E6=B5=81=E9=87=
=8F</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">667</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #666;">-</td>
                        </tr>
                        <tr style=3D"background: #fafafa;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333; font-weight: 600;">=E8=AF=95=E9=A9=BE=E5=AE=A2=E6=88=
=B7</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">0</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #333;">185</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #666;">-</td>
                        </tr>
                        <tr style=3D"background: white;">
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; color: #333; font-weight: 600;">=E8=AF=95=E9=A9=BE=E8=BD=AC=E5=8C=
=96=E7=8E=87</td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #dc3545; font-weight: 600;">
                                0%
                            </td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #dc3545; font-weight: 600;">
                                28%
                            </td>
                            <td style=3D"padding: 8px 6px; border: 1px soli=
d #ddd; text-align: center; color: #666;">
                                =E5=BE=85=E6=8F=90=E5=8D=87
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div style=3D"margin-top: 15px; padding: 10px; background: =
#f8f9fa; border-radius: 4px; font-size: 10px; color: #666;">
                    <div style=3D"margin-bottom: 5px;"><strong>=E8=AF=95=E9=
=A9=BE=E7=8E=87=E8=AF=84=E4=BC=B0=E6=A0=87=E5=87=86=EF=BC=9A</strong></div>
                    <div style=3D"display: grid; grid-template-columns: rep=
eat(3, 1fr); gap: 10px;">
                        <div><span style=3D"color: #28a745;">=E2=97=8F</spa=
n> =E4=BC=98=E7=A7=80=EF=BC=9A=E2=89=A540%</div>
                        <div><span style=3D"color: #ffc107;">=E2=97=8F</spa=
n> =E8=89=AF=E5=A5=BD=EF=BC=9A25-39%</div>
                        <div><span style=3D"color: #dc3545;">=E2=97=8F</spa=
n> =E5=BE=85=E6=8F=90=E5=8D=87=EF=BC=9A&lt;25%</div>
                    </div>
                </div>
            </div>
        </div>
                                </div>
                            </div>
                        </div>

                        <!-- =E7=AC=AC=E4=BA=94=E8=A1=8C=EF=BC=9A=E9=94=80=
=E5=94=AE=E9=A1=BE=E9=97=AE=E8=AE=A2=E5=8D=95=E6=8E=92=E5=90=8D=E3=80=81=E4=
=BA=A4=E8=BD=A6=E7=BB=9F=E8=AE=A1 -->
                        <div class=3D"analytics-row" style=3D"display: grid=
; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- =E9=94=80=E5=94=AE=E9=A1=BE=E9=97=AE=E8=AE=
=A2=E5=8D=95=E6=8E=92=E5=90=8D -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-trophy"></i> =E9=
=94=80=E5=94=AE=E9=A1=BE=E9=97=AE=E8=AE=A2=E5=8D=95=E6=8E=92=E5=90=8D</h3>
                                    <div class=3D"time-selector">
                                        <select id=3D"order-ranking-time-se=
lector" class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                            <option value=3D"=E6=9C=AC=E5=
=B9=B4">=E6=9C=AC=E5=B9=B4</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"order-ranking-table">
                <div style=3D"text-align: center; padding: 20px; color: #66=
6;">
                    <i class=3D"fas fa-info-circle"></i> =E6=9A=82=E6=97=A0=
=E8=AE=A2=E5=8D=95=E6=95=B0=E6=8D=AE
                </div>
            </div>
                                </div>
                            </div>

                            <!-- =E4=BA=A4=E8=BD=A6=E7=BB=9F=E8=AE=A1 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-handshake"></i> =
=E4=BA=A4=E8=BD=A6=E7=BB=9F=E8=AE=A1</h3>
                                    <div class=3D"time-selector">
                                        <select id=3D"delivery-stats-time-s=
elector" class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                            <option value=3D"=E6=9C=AC=E5=
=B9=B4">=E6=9C=AC=E5=B9=B4</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"delivery-statistics-table">
                <div style=3D"text-align: center; padding: 20px; color: #66=
6;">
                    <i class=3D"fas fa-info-circle"></i> =E6=9A=82=E6=97=A0=
=E4=BA=A4=E8=BD=A6=E6=95=B0=E6=8D=AE
                </div>
            </div>
                                </div>
                            </div>
                        </div>

                        <!-- =E7=AC=AC=E5=85=AD=E8=A1=8C=EF=BC=9A=E6=84=8F=
=E5=90=91=E5=AE=A2=E6=88=B7=E7=BB=9F=E8=AE=A1=E3=80=81=E9=94=80=E5=94=AE=E9=
=A1=BE=E9=97=AE=E5=85=B3=E9=94=AE=E6=8C=87=E6=A0=87 -->
                        <div class=3D"analytics-row" style=3D"display: grid=
; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- =E6=84=8F=E5=90=91=E5=AE=A2=E6=88=B7=E7=BB=
=9F=E8=AE=A1 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-heart"></i> =E6=
=84=8F=E5=90=91=E5=AE=A2=E6=88=B7=E7=BB=9F=E8=AE=A1</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"intention-customer-stats">
            <!-- =E6=84=8F=E5=90=91=E5=AE=A2=E6=88=B7=E6=A6=82=E8=A7=88 -->
            <div style=3D"display: grid; grid-template-columns: repeat(3, 1=
fr); gap: 10px; margin-bottom: 20px;">
                <div style=3D"text-align: center; padding: 12px; background=
: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 6px; co=
lor: white;">
                    <div style=3D"font-size: 18px; font-weight: bold; margi=
n-bottom: 3px;">0</div>
                    <div style=3D"font-size: 10px; opacity: 0.9;">=E4=BB=8A=
=E6=97=A5=E6=84=8F=E5=90=91</div>
                </div>
                <div style=3D"text-align: center; padding: 12px; background=
: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 6px; co=
lor: white;">
                    <div style=3D"font-size: 18px; font-weight: bold; margi=
n-bottom: 3px;">0</div>
                    <div style=3D"font-size: 10px; opacity: 0.9;">=E6=9C=AC=
=E6=9C=88=E6=84=8F=E5=90=91</div>
                </div>
                <div style=3D"text-align: center; padding: 12px; background=
: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 6px; co=
lor: white;">
                    <div style=3D"font-size: 18px; font-weight: bold; margi=
n-bottom: 3px;">665</div>
                    <div style=3D"font-size: 10px; opacity: 0.9;">=E5=85=A8=
=E5=B9=B4=E6=84=8F=E5=90=91</div>
                </div>
            </div>

            <!-- =E6=84=8F=E5=90=91=E5=AE=A2=E6=88=B7=E5=88=86=E7=BA=A7=E8=
=A1=A8=E6=A0=BC -->
            <table style=3D"width: 100%; border-collapse: collapse; font-si=
ze: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style=3D"background: #f1f3f4; color: #333;">
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: left; font-weight: 600;">=E6=84=8F=E5=90=91=E7=AD=89=E7=BA=
=A7</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E4=BB=8A=E6=97=A5</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E6=9C=AC=E6=9C=88</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E5=85=A8=E5=B9=B4</th>
                        <th style=3D"padding: 8px 6px; border: 1px solid #d=
dd; text-align: center; font-weight: 600;">=E6=9C=88=E5=8D=A0=E6=AF=94</th>
                    </tr>
                </thead>
                <tbody>
                   =20
                            <tr style=3D"background: white;">
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; color: #dc3545; font-weight: 600;">
                                    =F0=9F=94=A5 =E9=AB=98=E6=84=8F=E5=90=
=91
                                </td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333;">0</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333;">315</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #dc3545; font-weight: 600;">
                                    0%
                                </td>
                            </tr>
                       =20
                            <tr style=3D"background: #fafafa;">
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; color: #ffc107; font-weight: 600;">
                                    =E2=AD=90 =E4=B8=AD=E6=84=8F=E5=90=91
                                </td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333;">0</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333;">163</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #ffc107; font-weight: 600;">
                                    0%
                                </td>
                            </tr>
                       =20
                            <tr style=3D"background: white;">
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; color: #28a745; font-weight: 600;">
                                    =F0=9F=92=A1 =E4=BD=8E=E6=84=8F=E5=90=
=91
                                </td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333;">0</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333;">3</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #28a745; font-weight: 600;">
                                    0%
                                </td>
                            </tr>
                       =20
                            <tr style=3D"background: #fafafa;">
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; color: #333; font-weight: 600;">
                                    =F0=9F=93=8B =E5=85=B6=E4=BB=96
                                </td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333;">0</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333;">184</td>
                                <td style=3D"padding: 8px 6px; border: 1px =
solid #ddd; text-align: center; color: #333; font-weight: 600;">
                                    0%
                                </td>
                            </tr>
                       =20
                </tbody>
            </table>

            <div style=3D"margin-top: 15px; padding: 10px; background: #f8f=
9fa; border-radius: 4px; font-size: 10px; color: #666;">
                <div style=3D"margin-bottom: 5px;"><strong>=E6=84=8F=E5=90=
=91=E7=AD=89=E7=BA=A7=E8=AF=B4=E6=98=8E=EF=BC=9A</strong></div>
                <div style=3D"display: grid; grid-template-columns: repeat(=
2, 1fr); gap: 8px;">
                    <div><span style=3D"color: #dc3545;">=F0=9F=94=A5</span=
> =E9=AB=98=E6=84=8F=E5=90=91=EF=BC=9A=E8=B4=AD=E4=B9=B0=E6=84=8F=E6=84=BF=
=E5=BC=BA=E7=83=88</div>
                    <div><span style=3D"color: #ffc107;">=E2=AD=90</span> =
=E4=B8=AD=E6=84=8F=E5=90=91=EF=BC=9A=E6=9C=89=E8=B4=AD=E4=B9=B0=E8=80=83=E8=
=99=91</div>
                    <div><span style=3D"color: #28a745;">=F0=9F=92=A1</span=
> =E4=BD=8E=E6=84=8F=E5=90=91=EF=BC=9A=E5=88=9D=E6=AD=A5=E4=BA=86=E8=A7=A3=
=E9=98=B6=E6=AE=B5</div>
                    <div><span style=3D"color: #333;">=F0=9F=93=8B</span> =
=E5=85=B6=E4=BB=96=EF=BC=9A=E5=BE=85=E5=88=86=E7=B1=BB=E5=AE=A2=E6=88=B7</d=
iv>
                </div>
            </div>
        </div>
                                </div>
                            </div>

                            <!-- =E9=94=80=E5=94=AE=E9=A1=BE=E9=97=AE=E5=85=
=B3=E9=94=AE=E6=8C=87=E6=A0=87 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-chart-bar"></i> =
=E9=94=80=E5=94=AE=E9=A1=BE=E9=97=AE=E5=85=B3=E9=94=AE=E6=8C=87=E6=A0=87</h=
3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"sales-advisor-kpis">
                <div style=3D"text-align: center; padding: 20px; color: #66=
6;">
                    <i class=3D"fas fa-info-circle"></i> =E6=9A=82=E6=97=A0=
=E9=94=80=E5=94=AE=E9=A1=BE=E9=97=AE=E6=95=B0=E6=8D=AE
                </div>
            </div>
                                </div>
                            </div>
                        </div>

                        <!-- =E7=AC=AC=E4=B8=83=E8=A1=8C=EF=BC=9A=E9=94=80=
=E5=94=AE=E6=BC=8F=E6=96=97=E5=88=86=E6=9E=90 -->
                        <div class=3D"analytics-row" style=3D"display: grid=
; grid-template-columns: 1fr; gap: 20px;">
                            <!-- =E9=94=80=E5=94=AE=E6=BC=8F=E6=96=97=E5=88=
=86=E6=9E=90 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-filter"></i> =E9=
=94=80=E5=94=AE=E6=BC=8F=E6=96=97=E5=88=86=E6=9E=90</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"sales-funnel-analysis">
            <div style=3D"padding: 20px;">
                <!-- =E6=BC=8F=E6=96=97=E6=A6=82=E8=A7=88 -->
                <div style=3D"display: grid; grid-template-columns: repeat(=
4, 1fr); gap: 15px; margin-bottom: 30px;">
                    <div style=3D"text-align: center; padding: 20px; backgr=
ound: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10p=
x; color: white; position: relative;">
                        <div style=3D"font-size: 28px; font-weight: bold; m=
argin-bottom: 8px;">0</div>
                        <div style=3D"font-size: 14px; opacity: 0.9; margin=
-bottom: 5px;">=E6=80=BB=E5=AE=A2=E6=B5=81</div>
                        <div style=3D"font-size: 12px; opacity: 0.8;">100%<=
/div>
                        <div style=3D"position: absolute; top: 10px; right:=
 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px=
; border-radius: 10px;">
                            -100%
                        </div>
                    </div>
                    <div style=3D"text-align: center; padding: 20px; backgr=
ound: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 10p=
x; color: white; position: relative;">
                        <div style=3D"font-size: 28px; font-weight: bold; m=
argin-bottom: 8px;">0</div>
                        <div style=3D"font-size: 14px; opacity: 0.9; margin=
-bottom: 5px;">=E8=AF=95=E9=A9=BE=E5=AE=A2=E6=88=B7</div>
                        <div style=3D"font-size: 12px; opacity: 0.8;">0%</d=
iv>
                        <div style=3D"position: absolute; top: 10px; right:=
 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px=
; border-radius: 10px;">
                            -100%
                        </div>
                    </div>
                    <div style=3D"text-align: center; padding: 20px; backgr=
ound: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 10p=
x; color: white; position: relative;">
                        <div style=3D"font-size: 28px; font-weight: bold; m=
argin-bottom: 8px;">0</div>
                        <div style=3D"font-size: 14px; opacity: 0.9; margin=
-bottom: 5px;">=E6=8A=A5=E4=BB=B7=E5=AE=A2=E6=88=B7</div>
                        <div style=3D"font-size: 12px; opacity: 0.8;">0%</d=
iv>
                        <div style=3D"position: absolute; top: 10px; right:=
 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px=
; border-radius: 10px;">
                            +0%
                        </div>
                    </div>
                    <div style=3D"text-align: center; padding: 20px; backgr=
ound: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 10p=
x; color: white; position: relative;">
                        <div style=3D"font-size: 28px; font-weight: bold; m=
argin-bottom: 8px;">0</div>
                        <div style=3D"font-size: 14px; opacity: 0.9; margin=
-bottom: 5px;">=E6=88=90=E4=BA=A4=E5=AE=A2=E6=88=B7</div>
                        <div style=3D"font-size: 12px; opacity: 0.8;">0%</d=
iv>
                        <div style=3D"position: absolute; top: 10px; right:=
 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px=
; border-radius: 10px;">
                            -100%
                        </div>
                    </div>
                </div>

                <!-- =E6=BC=8F=E6=96=97=E8=BD=AC=E5=8C=96=E8=AF=A6=E7=BB=86=
=E5=88=86=E6=9E=90 -->
                <div style=3D"display: grid; grid-template-columns: 1fr 1fr=
; gap: 30px;">
                    <!-- =E8=BD=AC=E5=8C=96=E7=8E=87=E5=AF=B9=E6=AF=94=E8=
=A1=A8=E6=A0=BC -->
                    <div>
                        <h4 style=3D"margin-bottom: 15px; color: #333; font=
-size: 14px;">
                            <i class=3D"fas fa-chart-line"></i> =E8=BD=AC=
=E5=8C=96=E7=8E=87=E5=AF=B9=E6=AF=94=E5=88=86=E6=9E=90
                        </h4>
                        <table style=3D"width: 100%; border-collapse: colla=
pse; font-size: 11px; font-family: Arial, sans-serif;">
                            <thead>
                                <tr style=3D"background: #f1f3f4; color: #3=
33;">
                                    <th style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: left; font-weight: 600;">=E8=BD=AC=E5=8C=96=E7=
=8E=AF=E8=8A=82</th>
                                    <th style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; font-weight: 600;">=E6=9C=AC=E6=9C=88<=
/th>
                                    <th style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; font-weight: 600;">=E4=B8=8A=E6=9C=88<=
/th>
                                    <th style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; font-weight: 600;">=E7=8E=AF=E6=AF=94<=
/th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style=3D"background: white;">
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; color: #333; font-weight: 600;">=E5=AE=A2=E6=B5=81=E2=86=
=92=E8=AF=95=E9=A9=BE</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">0%</td=
>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #666;">41%</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #dc3545; font-weight: 600;">
                                        -41%
                                    </td>
                                </tr>
                                <tr style=3D"background: #fafafa;">
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; color: #333; font-weight: 600;">=E8=AF=95=E9=A9=BE=E2=86=
=92=E6=8A=A5=E4=BB=B7</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">0%</td=
>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #666;">0%</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #28a745; font-weight: 600;">
                                        +0%
                                    </td>
                                </tr>
                                <tr style=3D"background: white;">
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; color: #333; font-weight: 600;">=E6=8A=A5=E4=BB=B7=E2=86=
=92=E6=88=90=E4=BA=A4</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">0%</td=
>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #666;">0%</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #28a745; font-weight: 600;">
                                        +0%
                                    </td>
                                </tr>
                                <tr style=3D"background: #f8f9fa; border-to=
p: 2px solid #ddd;">
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; color: #333; font-weight: 700;">=E6=95=B4=E4=BD=93=E8=BD=
=AC=E5=8C=96=E7=8E=87</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #333; font-weight: 700;">0%</td=
>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #666;">5%</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #dc3545; font-weight: 700;">
                                        -5%
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- =E6=BC=8F=E6=96=97=E6=B5=81=E5=A4=B1=E5=88=86=E6=
=9E=90 -->
                    <div>
                        <h4 style=3D"margin-bottom: 15px; color: #333; font=
-size: 14px;">
                            <i class=3D"fas fa-exclamation-triangle"></i> =
=E6=BC=8F=E6=96=97=E6=B5=81=E5=A4=B1=E5=88=86=E6=9E=90
                        </h4>
                        <div style=3D"background: #f8f9fa; border-radius: 8=
px; padding: 15px;">
                            <div style=3D"margin-bottom: 15px;">
                                <div style=3D"display: flex; justify-conten=
t: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style=3D"font-size: 12px; color: =
#666;">=E5=AE=A2=E6=B5=81=E6=B5=81=E5=A4=B1</span>
                                    <span style=3D"font-size: 12px; font-we=
ight: 600; color: #dc3545;">
                                        0=E4=BA=BA (100%)
                                    </span>
                                </div>
                                <div style=3D"background: #e9ecef; height: =
8px; border-radius: 4px; overflow: hidden;">
                                    <div style=3D"background: #dc3545; heig=
ht: 100%; width: 100%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style=3D"margin-bottom: 15px;">
                                <div style=3D"display: flex; justify-conten=
t: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style=3D"font-size: 12px; color: =
#666;">=E8=AF=95=E9=A9=BE=E6=B5=81=E5=A4=B1</span>
                                    <span style=3D"font-size: 12px; font-we=
ight: 600; color: #ffc107;">
                                        0=E4=BA=BA (100%)
                                    </span>
                                </div>
                                <div style=3D"background: #e9ecef; height: =
8px; border-radius: 4px; overflow: hidden;">
                                    <div style=3D"background: #ffc107; heig=
ht: 100%; width: 100%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style=3D"margin-bottom: 15px;">
                                <div style=3D"display: flex; justify-conten=
t: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style=3D"font-size: 12px; color: =
#666;">=E6=8A=A5=E4=BB=B7=E6=B5=81=E5=A4=B1</span>
                                    <span style=3D"font-size: 12px; font-we=
ight: 600; color: #fd7e14;">
                                        0=E4=BA=BA (100%)
                                    </span>
                                </div>
                                <div style=3D"background: #e9ecef; height: =
8px; border-radius: 4px; overflow: hidden;">
                                    <div style=3D"background: #fd7e14; heig=
ht: 100%; width: 100%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style=3D"margin-top: 20px; padding: 10px; =
background: white; border-radius: 6px; border-left: 4px solid #28a745;">
                                <div style=3D"font-size: 11px; color: #666;=
 margin-bottom: 5px;">=E4=BC=98=E5=8C=96=E5=BB=BA=E8=AE=AE</div>
                                <div style=3D"font-size: 10px; color: #333;=
 line-height: 1.4;">
                                    =E2=80=A2 =E6=8F=90=E5=8D=87=E8=AF=95=
=E9=A9=BE=E9=82=80=E7=BA=A6=E8=AF=9D=E6=9C=AF=E5=92=8C=E6=8A=80=E5=B7=A7<br=
>
                                    =E2=80=A2 =E5=8A=A0=E5=BC=BA=E8=AF=95=
=E9=A9=BE=E4=BD=93=E9=AA=8C=E5=92=8C=E4=BA=A7=E5=93=81=E4=BB=8B=E7=BB=8D<br=
>
                                    =E2=80=A2 =E4=BC=98=E5=8C=96=E6=8A=A5=
=E4=BB=B7=E7=AD=96=E7=95=A5=E5=92=8C=E6=88=90=E4=BA=A4=E6=8A=80=E5=B7=A7<br=
>
                                    =E2=80=A2 =E5=AE=9A=E6=9C=9F=E5=88=86=
=E6=9E=90=E6=B5=81=E5=A4=B1=E5=8E=9F=E5=9B=A0=E5=B9=B6=E5=88=B6=E5=AE=9A=E6=
=94=B9=E8=BF=9B=E6=8E=AA=E6=96=BD
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style=3D"margin-top: 20px; padding: 12px; background: =
#f8f9fa; border-radius: 6px; font-size: 10px; color: #666; text-align: cent=
er;">
                    =E6=95=B0=E6=8D=AE=E7=BB=9F=E8=AE=A1=E6=97=B6=E9=97=B4=
=EF=BC=9A=E6=9C=AC=E6=9C=88 | =E7=8E=AF=E6=AF=94=E6=95=B0=E6=8D=AE=EF=BC=9A=
=E4=B8=8A=E6=9C=88=E5=90=8C=E6=9C=9F | =E6=9B=B4=E6=96=B0=E6=97=B6=E9=97=B4=
=EF=BC=9A2025/8/1 13:58:33
                </div>
            </div>
        </div>
                                </div>
                            </div>
                        </div>

                        <!-- =E7=AC=AC=E5=85=AB=E8=A1=8C=EF=BC=9A=E7=BA=BF=
=E7=B4=A2=E6=BC=8F=E6=96=97=E5=88=86=E6=9E=90 -->
                        <div class=3D"analytics-row" style=3D"display: grid=
; grid-template-columns: 1fr; gap: 20px;">
                            <!-- =E7=BA=BF=E7=B4=A2=E6=BC=8F=E6=96=97=E5=88=
=86=E6=9E=90 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-funnel-dollar"><=
/i> =E7=BA=BF=E7=B4=A2=E6=BC=8F=E6=96=97=E5=88=86=E6=9E=90</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"leads-funnel-analysis">
            <div style=3D"padding: 20px;">
                <!-- =E7=BA=BF=E7=B4=A2=E6=BC=8F=E6=96=97=E6=A6=82=E8=A7=88=
 -->
                <div style=3D"display: grid; grid-template-columns: repeat(=
4, 1fr); gap: 15px; margin-bottom: 30px;">
                    <div style=3D"text-align: center; padding: 20px; backgr=
ound: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10p=
x; color: white; position: relative;">
                        <div style=3D"font-size: 28px; font-weight: bold; m=
argin-bottom: 8px;">0</div>
                        <div style=3D"font-size: 14px; opacity: 0.9; margin=
-bottom: 5px;">=E6=80=BB=E7=BA=BF=E7=B4=A2</div>
                        <div style=3D"font-size: 12px; opacity: 0.8;">100%<=
/div>
                        <div style=3D"position: absolute; top: 10px; right:=
 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px=
; border-radius: 10px;">
                            +0%
                        </div>
                    </div>
                    <div style=3D"text-align: center; padding: 20px; backgr=
ound: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 10p=
x; color: white; position: relative;">
                        <div style=3D"font-size: 28px; font-weight: bold; m=
argin-bottom: 8px;">0</div>
                        <div style=3D"font-size: 14px; opacity: 0.9; margin=
-bottom: 5px;">=E6=9C=89=E6=95=88=E7=BA=BF=E7=B4=A2</div>
                        <div style=3D"font-size: 12px; opacity: 0.8;">0%</d=
iv>
                        <div style=3D"position: absolute; top: 10px; right:=
 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px=
; border-radius: 10px;">
                            +0%
                        </div>
                    </div>
                    <div style=3D"text-align: center; padding: 20px; backgr=
ound: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 10p=
x; color: white; position: relative;">
                        <div style=3D"font-size: 28px; font-weight: bold; m=
argin-bottom: 8px;">0</div>
                        <div style=3D"font-size: 14px; opacity: 0.9; margin=
-bottom: 5px;">=E5=88=B0=E5=BA=97=E5=AE=A2=E6=88=B7</div>
                        <div style=3D"font-size: 12px; opacity: 0.8;">0%</d=
iv>
                        <div style=3D"position: absolute; top: 10px; right:=
 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px=
; border-radius: 10px;">
                            +0%
                        </div>
                    </div>
                    <div style=3D"text-align: center; padding: 20px; backgr=
ound: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 10p=
x; color: white; position: relative;">
                        <div style=3D"font-size: 28px; font-weight: bold; m=
argin-bottom: 8px;">0</div>
                        <div style=3D"font-size: 14px; opacity: 0.9; margin=
-bottom: 5px;">=E6=88=90=E4=BA=A4=E5=AE=A2=E6=88=B7</div>
                        <div style=3D"font-size: 12px; opacity: 0.8;">0%</d=
iv>
                        <div style=3D"position: absolute; top: 10px; right:=
 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px=
; border-radius: 10px;">
                            +0%
                        </div>
                    </div>
                </div>

                <!-- =E7=BA=BF=E7=B4=A2=E8=BD=AC=E5=8C=96=E8=AF=A6=E7=BB=86=
=E5=88=86=E6=9E=90 -->
                <div style=3D"display: grid; grid-template-columns: 1fr 1fr=
; gap: 30px;">
                    <!-- =E8=BD=AC=E5=8C=96=E7=8E=87=E5=AF=B9=E6=AF=94=E8=
=A1=A8=E6=A0=BC -->
                    <div>
                        <h4 style=3D"margin-bottom: 15px; color: #333; font=
-size: 14px;">
                            <i class=3D"fas fa-chart-line"></i> =E7=BA=BF=
=E7=B4=A2=E8=BD=AC=E5=8C=96=E7=8E=87=E5=AF=B9=E6=AF=94=E5=88=86=E6=9E=90
                        </h4>
                        <table style=3D"width: 100%; border-collapse: colla=
pse; font-size: 11px; font-family: Arial, sans-serif;">
                            <thead>
                                <tr style=3D"background: #f1f3f4; color: #3=
33;">
                                    <th style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: left; font-weight: 600;">=E8=BD=AC=E5=8C=96=E7=
=8E=AF=E8=8A=82</th>
                                    <th style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; font-weight: 600;">=E6=9C=AC=E6=9C=88<=
/th>
                                    <th style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; font-weight: 600;">=E4=B8=8A=E6=9C=88<=
/th>
                                    <th style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; font-weight: 600;">=E7=8E=AF=E6=AF=94<=
/th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style=3D"background: white;">
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; color: #333; font-weight: 600;">=E7=BA=BF=E7=B4=A2=E2=86=
=92=E6=9C=89=E6=95=88</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">0%</td=
>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #666;">0%</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #28a745; font-weight: 600;">
                                        +0%
                                    </td>
                                </tr>
                                <tr style=3D"background: #fafafa;">
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; color: #333; font-weight: 600;">=E6=9C=89=E6=95=88=E2=86=
=92=E5=88=B0=E5=BA=97</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">0%</td=
>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #666;">0%</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #28a745; font-weight: 600;">
                                        +0%
                                    </td>
                                </tr>
                                <tr style=3D"background: white;">
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; color: #333; font-weight: 600;">=E5=88=B0=E5=BA=97=E2=86=
=92=E6=88=90=E4=BA=A4</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">0%</td=
>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #666;">0%</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #28a745; font-weight: 600;">
                                        +0%
                                    </td>
                                </tr>
                                <tr style=3D"background: #f8f9fa; border-to=
p: 2px solid #ddd;">
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; color: #333; font-weight: 700;">=E6=95=B4=E4=BD=93=E8=BD=
=AC=E5=8C=96=E7=8E=87</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #333; font-weight: 700;">0%</td=
>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #666;">0%</td>
                                    <td style=3D"padding: 10px 8px; border:=
 1px solid #ddd; text-align: center; color: #28a745; font-weight: 700;">
                                        +0%
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- =E7=BA=BF=E7=B4=A2=E8=B4=A8=E9=87=8F=E5=88=86=E6=
=9E=90 -->
                    <div>
                        <h4 style=3D"margin-bottom: 15px; color: #333; font=
-size: 14px;">
                            <i class=3D"fas fa-search"></i> =E7=BA=BF=E7=B4=
=A2=E8=B4=A8=E9=87=8F=E5=88=86=E6=9E=90
                        </h4>
                        <div style=3D"background: #f8f9fa; border-radius: 8=
px; padding: 15px;">
                            <div style=3D"margin-bottom: 15px;">
                                <div style=3D"display: flex; justify-conten=
t: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style=3D"font-size: 12px; color: =
#666;">=E6=97=A0=E6=95=88=E7=BA=BF=E7=B4=A2</span>
                                    <span style=3D"font-size: 12px; font-we=
ight: 600; color: #dc3545;">
                                        0=E4=B8=AA (100%)
                                    </span>
                                </div>
                                <div style=3D"background: #e9ecef; height: =
8px; border-radius: 4px; overflow: hidden;">
                                    <div style=3D"background: #dc3545; heig=
ht: 100%; width: 100%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style=3D"margin-bottom: 15px;">
                                <div style=3D"display: flex; justify-conten=
t: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style=3D"font-size: 12px; color: =
#666;">=E6=9C=AA=E5=88=B0=E5=BA=97=E7=BA=BF=E7=B4=A2</span>
                                    <span style=3D"font-size: 12px; font-we=
ight: 600; color: #ffc107;">
                                        0=E4=B8=AA (100%)
                                    </span>
                                </div>
                                <div style=3D"background: #e9ecef; height: =
8px; border-radius: 4px; overflow: hidden;">
                                    <div style=3D"background: #ffc107; heig=
ht: 100%; width: 100%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style=3D"margin-bottom: 15px;">
                                <div style=3D"display: flex; justify-conten=
t: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style=3D"font-size: 12px; color: =
#666;">=E6=9C=AA=E6=88=90=E4=BA=A4=E7=BA=BF=E7=B4=A2</span>
                                    <span style=3D"font-size: 12px; font-we=
ight: 600; color: #fd7e14;">
                                        0=E4=B8=AA (100%)
                                    </span>
                                </div>
                                <div style=3D"background: #e9ecef; height: =
8px; border-radius: 4px; overflow: hidden;">
                                    <div style=3D"background: #fd7e14; heig=
ht: 100%; width: 100%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style=3D"margin-top: 20px; padding: 10px; =
background: white; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <div style=3D"font-size: 11px; color: #666;=
 margin-bottom: 5px;">=E7=BA=BF=E7=B4=A2=E4=BC=98=E5=8C=96=E5=BB=BA=E8=AE=
=AE</div>
                                <div style=3D"font-size: 10px; color: #333;=
 line-height: 1.4;">
                                    =E2=80=A2 =E6=8F=90=E5=8D=87=E7=BA=BF=
=E7=B4=A2=E7=AD=9B=E9=80=89=E5=92=8C=E9=AA=8C=E8=AF=81=E6=9C=BA=E5=88=B6<br=
>
                                    =E2=80=A2 =E5=8A=A0=E5=BC=BA=E7=BA=BF=
=E7=B4=A2=E8=B7=9F=E8=BF=9B=E5=92=8C=E9=82=80=E7=BA=A6=E6=8A=80=E5=B7=A7<br=
>
                                    =E2=80=A2 =E4=BC=98=E5=8C=96=E5=88=B0=
=E5=BA=97=E6=8E=A5=E5=BE=85=E5=92=8C=E9=94=80=E5=94=AE=E6=B5=81=E7=A8=8B<br=
>
                                    =E2=80=A2 =E5=BB=BA=E7=AB=8B=E7=BA=BF=
=E7=B4=A2=E5=88=86=E7=BA=A7=E7=AE=A1=E7=90=86=E5=92=8C=E8=B7=9F=E8=BF=9B=E4=
=BD=93=E7=B3=BB
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style=3D"margin-top: 20px; padding: 12px; background: =
#f8f9fa; border-radius: 6px; font-size: 10px; color: #666; text-align: cent=
er;">
                    =E6=95=B0=E6=8D=AE=E7=BB=9F=E8=AE=A1=E6=97=B6=E9=97=B4=
=EF=BC=9A=E6=9C=AC=E6=9C=88 | =E7=8E=AF=E6=AF=94=E6=95=B0=E6=8D=AE=EF=BC=9A=
=E4=B8=8A=E6=9C=88=E5=90=8C=E6=9C=9F | =E6=9B=B4=E6=96=B0=E6=97=B6=E9=97=B4=
=EF=BC=9A2025/8/1 13:58:33
                </div>
            </div>
        </div>
                                </div>
                            </div>
                        </div>

                        <!-- =E7=AC=AC=E4=B9=9D=E8=A1=8C=EF=BC=9A=E7=BA=BF=
=E7=B4=A2=E6=B8=A0=E9=81=93=E5=88=86=E6=9E=90=E3=80=81=E8=B6=8B=E5=8A=BF=E5=
=88=86=E6=9E=90 -->
                        <div class=3D"analytics-row" style=3D"display: grid=
; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- =E7=BA=BF=E7=B4=A2=E6=B8=A0=E9=81=93=E5=88=
=86=E6=9E=90 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-share-alt"></i> =
=E7=BA=BF=E7=B4=A2=E6=B8=A0=E9=81=93=E5=88=86=E6=9E=90</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E6=
=9C=88">=E6=9C=AC=E6=9C=88</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"lead-channel-analysis">
                <div style=3D"text-align: center; padding: 20px; color: #66=
6;">
                    <i class=3D"fas fa-info-circle"></i> =E6=9A=82=E6=97=A0=
=E7=BA=BF=E7=B4=A2=E6=B8=A0=E9=81=93=E6=95=B0=E6=8D=AE
                </div>
            </div>
                                </div>
                            </div>

                            <!-- =E8=B6=8B=E5=8A=BF=E5=88=86=E6=9E=90 -->
                            <div class=3D"analytics-module">
                                <div class=3D"module-header">
                                    <h3><i class=3D"fas fa-chart-area"></i>=
 =E8=B6=8B=E5=8A=BF=E5=88=86=E6=9E=90</h3>
                                    <div class=3D"time-selector">
                                        <select class=3D"form-select">
                                            <option value=3D"=E6=9C=AC=E5=
=B9=B4">=E6=9C=AC=E5=B9=B4</option>
                                        </select>
                                    </div>
                                </div>
                                <div class=3D"module-content">
                                    <div id=3D"trend-analysis">
            <div style=3D"padding: 15px;">
                <!-- =E8=B6=8B=E5=8A=BF=E5=9B=BE=E8=A1=A8=E5=8C=BA=E5=9F=9F=
 -->
                <div style=3D"height: 200px; background: #f8f9fa; border-ra=
dius: 8px; margin-bottom: 20px; display: flex; align-items: center; justify=
-content: center; position: relative;">
                    <canvas id=3D"trend-chart" style=3D"max-width: 100%; ma=
x-height: 100%;" width=3D"300" height=3D"150"></canvas>
                    <div style=3D"position: absolute; top: 10px; left: 10px=
; font-size: 10px; color: #666;">
                        2025=E5=B9=B4=E5=BA=A6=E8=B6=8B=E5=8A=BF
                    </div>
                </div>

                <!-- =E6=9C=88=E5=BA=A6=E6=95=B0=E6=8D=AE=E8=A1=A8=E6=A0=BC=
 -->
                <table style=3D"width: 100%; border-collapse: collapse; fon=
t-size: 10px; font-family: Arial, sans-serif;">
                    <thead>
                        <tr style=3D"background: #f1f3f4; color: #333;">
                            <th style=3D"padding: 6px 4px; border: 1px soli=
d #ddd; text-align: center; font-weight: 600;">=E6=9C=88=E4=BB=BD</th>
                            <th style=3D"padding: 6px 4px; border: 1px soli=
d #ddd; text-align: center; font-weight: 600;">=E5=88=B0=E5=BA=97=E5=AE=A2=
=E6=B5=81</th>
                            <th style=3D"padding: 6px 4px; border: 1px soli=
d #ddd; text-align: center; font-weight: 600;">=E6=80=BB=E7=BA=BF=E7=B4=A2<=
/th>
                            <th style=3D"padding: 6px 4px; border: 1px soli=
d #ddd; text-align: center; font-weight: 600;">=E6=9C=89=E6=95=88=E7=BA=BF=
=E7=B4=A2</th>
                            <th style=3D"padding: 6px 4px; border: 1px soli=
d #ddd; text-align: center; font-weight: 600;">=E6=88=90=E4=BA=A4=E8=AE=A2=
=E5=8D=95</th>
                        </tr>
                    </thead>
                    <tbody>
                       =20
                                <tr style=3D"background: white;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">
                                        1=E6=9C=88
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">90</=
td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                </tr>
                           =20
                                <tr style=3D"background: #fafafa;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">
                                        2=E6=9C=88
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">72</=
td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                </tr>
                           =20
                                <tr style=3D"background: white;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">
                                        3=E6=9C=88
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">121<=
/td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                </tr>
                           =20
                                <tr style=3D"background: #fafafa;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">
                                        4=E6=9C=88
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">110<=
/td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                </tr>
                           =20
                                <tr style=3D"background: white;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">
                                        5=E6=9C=88
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">109<=
/td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                </tr>
                           =20
                                <tr style=3D"background: #fafafa;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">
                                        6=E6=9C=88
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">104<=
/td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                </tr>
                           =20
                                <tr style=3D"background: white;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">
                                        7=E6=9C=88
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">61</=
td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">3</t=
d>
                                </tr>
                           =20
                                <tr style=3D"background: #e3f2fd;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: 600;">
                                        8=E6=9C=88 (=E6=9C=AC=E6=9C=88)
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: 600;">0</td>
                                </tr>
                           =20
                                <tr style=3D"background: white;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">
                                        9=E6=9C=88
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                </tr>
                           =20
                                <tr style=3D"background: #fafafa;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">
                                        10=E6=9C=88
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                </tr>
                           =20
                                <tr style=3D"background: white;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">
                                        11=E6=9C=88
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                </tr>
                           =20
                                <tr style=3D"background: #fafafa;">
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">
                                        12=E6=9C=88
                                    </td>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                    <td style=3D"padding: 6px 4px; border: =
1px solid #ddd; text-align: center; color: #333; font-weight: normal;">0</t=
d>
                                </tr>
                           =20
                    </tbody>
                </table>

                <div style=3D"margin-top: 15px; padding: 10px; background: =
#f8f9fa; border-radius: 4px; font-size: 10px; color: #666; text-align: cent=
er;">
                    =E8=B6=8B=E5=8A=BF=E5=88=86=E6=9E=90=E5=9F=BA=E4=BA=8E2=
025=E5=B9=B4=E5=BA=A6=E6=95=B0=E6=8D=AE | =E6=9B=B4=E6=96=B0=E6=97=B6=E9=97=
=B4=EF=BC=9A2025/8/1 13:58:33
                </div>
            </div>
        </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- =E5=BC=95=E5=85=A5=E5=BF=85=E8=A6=81=E7=9A=84=E8=84=9A=E6=9C=AC --=
>
   =20
   =20
   =20

   =20

</body></html>
------MultipartBoundary--tCdgLTemwq0jS7q9jNLfyJgUywxjTTSi7GqPcLUxlP----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: file:///D:/Users/<USER>/Desktop/V1.1FJ/styles.css

@charset "utf-8";

:root { --primary: #4361ee; --secondary: #3f37c9; --success: #4cc9f0; --inf=
o: #4895ef; --warning: #f72585; --danger: #e63946; --light: #f8f9fa; --dark=
: #212529; --gray: #6c757d; --light-gray: #e9ecef; --border-radius: 10px; -=
-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); }

* { margin: 0px; padding: 0px; box-sizing: border-box; font-family: "Segoe =
UI", "Microsoft YaHei", sans-serif; }

body { background-color: rgb(245, 247, 251); color: rgb(51, 51, 51); line-h=
eight: 1.6; min-height: 100vh; }

.container { display: flex; flex-direction: column; min-height: 100vh; }

.app-container { display: flex; min-height: 100vh; background-color: rgb(24=
5, 247, 251); }

header { background: linear-gradient(135deg, var(--primary), var(--secondar=
y)); color: white; padding: 15px 30px; display: flex; justify-content: spac=
e-between; align-items: center; box-shadow: var(--shadow); }

.logo { display: flex; align-items: center; gap: 15px; }

.logo i { font-size: 28px; }

.logo h1 { font-size: 22px; font-weight: 600; }

.user-info { display: flex; align-items: center; gap: 12px; }

.user-avatar { width: 42px; height: 42px; background: rgba(255, 255, 255, 0=
.2); border-radius: 50%; display: flex; align-items: center; justify-conten=
t: center; font-weight: bold; font-size: 18px; }

.main-content { display: flex; flex: 1 1 0%; }

.sidebar { width: 260px; background: white; padding: 25px 20px; display: fl=
ex; flex-direction: column; box-shadow: var(--shadow); z-index: 10; }

.nav-menu { list-style: none; margin-top: 20px; }

.nav-menu li { margin-bottom: 8px; }

.nav-menu a { display: flex; align-items: center; padding: 12px 15px; text-=
decoration: none; color: var(--dark); border-radius: 8px; transition: 0.3s;=
 font-size: 15px; font-weight: 500; }

.nav-menu a i { margin-right: 12px; width: 20px; text-align: center; color:=
 var(--gray); }

.nav-menu a:hover, .nav-menu a.active { background: rgba(67, 97, 238, 0.1);=
 color: var(--primary); }

.nav-menu a.active i { color: var(--primary); }

.nav-menu a:hover i { color: var(--primary); }

.content-area { flex: 1 1 0%; padding: 30px; overflow-y: auto; }

.module-container { display: none; }

.module-container.active { display: block; animation: 0.5s ease 0s 1 normal=
 none running fadeIn; }

@keyframes fadeIn {=20
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0px); }
}

.module-header { margin-bottom: 30px; display: flex; justify-content: space=
-between; align-items: center; }

.module-header h1 { font-size: 26px; font-weight: 700; color: var(--dark); =
display: flex; align-items: center; gap: 12px; }

.module-header h1 i { color: var(--primary); }

.card { background: white; border-radius: var(--border-radius); box-shadow:=
 var(--shadow); overflow: hidden; margin-bottom: 25px; }

.card-header { padding: 20px 25px; border-bottom: 1px solid var(--light-gra=
y); display: flex; justify-content: space-between; align-items: center; }

.card-header h2 { font-size: 18px; font-weight: 600; display: flex; align-i=
tems: center; gap: 10px; }

.card-header h2 i { color: var(--primary); }

.card-body { padding: 25px; }

.funnel-overview { display: grid; grid-template-columns: repeat(4, 1fr); ga=
p: 15px; }

.funnel-step { text-align: center; padding: 15px; border-radius: 8px; backg=
round: rgba(67, 97, 238, 0.05); transition: transform 0.3s; }

.funnel-step:hover { transform: translateY(-5px); background: rgba(67, 97, =
238, 0.1); }

.funnel-step h3 { font-size: 14px; color: var(--gray); margin-bottom: 10px;=
 }

.funnel-step .number { font-size: 32px; font-weight: 700; color: var(--prim=
ary); margin-bottom: 5px; }

.funnel-step .conversion { font-size: 14px; background: rgba(76, 201, 240, =
0.2); color: var(--info); padding: 4px 8px; border-radius: 20px; display: i=
nline-block; }

.chart-container { padding: 15px; height: 300px; position: relative; }

.data-management { display: grid; grid-template-columns: repeat(2, 1fr); ga=
p: 20px; }

.data-card { padding: 20px; border-radius: 8px; background: rgba(67, 97, 23=
8, 0.05); display: flex; flex-direction: column; }

.data-card h3 { font-size: 15px; color: var(--gray); margin-bottom: 15px; d=
isplay: flex; align-items: center; gap: 8px; }

.data-card h3 i { width: 24px; height: 24px; display: flex; align-items: ce=
nter; justify-content: center; background: rgba(67, 97, 238, 0.1); color: v=
ar(--primary); border-radius: 6px; }

.data-card .number { font-size: 28px; font-weight: 700; color: var(--primar=
y); margin-bottom: 10px; }

.data-card .info { font-size: 14px; color: var(--gray); margin-top: auto; p=
adding: 8px 12px; background: rgba(255, 255, 255, 0.6); border-radius: 6px;=
 display: flex; align-items: center; gap: 8px; }

.data-card .info i { color: var(--success); }

.progress-bar { height: 8px; background: var(--light-gray); border-radius: =
4px; overflow: hidden; margin-top: 5px; }

.progress { height: 100%; background: var(--success); border-radius: 4px; }

.notification { position: fixed; bottom: 30px; right: 30px; background: whi=
te; border-radius: var(--border-radius); box-shadow: var(--shadow); padding=
: 18px 22px; display: flex; align-items: center; gap: 15px; transform: tran=
slateX(150%); transition: transform 0.4s; z-index: 1000; border-left: 4px s=
olid var(--primary); }

.notification.show { transform: translateX(0px); }

.notification i { font-size: 24px; color: var(--primary); }

.btn { padding: 10px 20px; border-radius: 6px; font-weight: 500; cursor: po=
inter; border: none; transition: 0.3s; display: inline-flex; align-items: c=
enter; gap: 8px; font-size: 14px; }

.btn-primary { background: var(--primary); color: white; }

.btn-primary:hover { background: var(--secondary); }

.btn-secondary { background: white; color: var(--primary); border: 1px soli=
d var(--primary); }

.btn-secondary:hover { background: rgba(67, 97, 238, 0.1); }

.form-select { padding: 8px 12px; border: 1px solid var(--light-gray); bord=
er-radius: 6px; background: white; font-size: 14px; }

.nav-card { background: white; border-radius: var(--border-radius); padding=
: 20px; text-decoration: none; color: var(--dark); box-shadow: var(--shadow=
); transition: 0.3s; text-align: center; }

.nav-card:hover { transform: translateY(-5px); box-shadow: rgba(0, 0, 0, 0.=
15) 0px 8px 25px; }

.nav-card-icon { font-size: 48px; color: var(--primary); margin-bottom: 15p=
x; }

.nav-card h3 { font-size: 18px; margin-bottom: 10px; color: var(--primary);=
 }

.nav-card p { font-size: 14px; color: var(--gray); line-height: 1.4; }

.dashboard-grid { display: grid; grid-template-columns: repeat(auto-fit, mi=
nmax(300px, 1fr)); gap: 25px; margin-bottom: 30px; }

.settings-tabs { display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: =
20px; border-bottom: 1px solid rgb(238, 238, 238); padding-bottom: 15px; }

.settings-tab { padding: 10px 20px; border: 1px solid rgb(221, 221, 221); b=
order-radius: 6px; background: rgb(248, 249, 250); cursor: pointer; font-si=
ze: 14px; transition: 0.3s; }

.settings-tab:hover { background: rgb(233, 236, 239); }

.settings-tab.active { background: var(--secondary); color: white; border-c=
olor: var(--secondary); }

.settings-content { min-height: 400px; padding: 20px 0px; }

.btn-outline { background: transparent; border: 1px solid var(--secondary);=
 color: var(--secondary); padding: 8px 16px; border-radius: 6px; cursor: po=
inter; font-size: 14px; transition: 0.3s; display: flex; align-items: cente=
r; gap: 8px; justify-content: center; }

.btn-outline:hover { background: var(--secondary); color: white; }

.config-table-container { overflow-x: auto; margin-top: 20px; }

.config-table { width: 100%; border-collapse: collapse; background: white; =
}

.config-table th, .config-table td { padding: 12px 15px; text-align: left; =
border-bottom: 1px solid rgb(238, 238, 238); }

.config-table th { background: rgb(248, 249, 250); font-weight: 600; color:=
 var(--primary); font-size: 14px; }

.config-table td { font-size: 14px; color: var(--dark); }

.config-table tbody tr:hover { background: rgb(248, 249, 250); }

.form-group { margin-bottom: 20px; }

.form-group label { display: block; margin-bottom: 8px; font-weight: 500; c=
olor: var(--dark); font-size: 14px; }

.form-group input, .form-group select, .form-group textarea { width: 100%; =
padding: 10px 12px; border: 1px solid rgb(221, 221, 221); border-radius: 6p=
x; font-size: 14px; transition: border-color 0.3s; }

.form-group input:focus, .form-group select:focus, .form-group textarea:foc=
us { outline: none; border-color: var(--primary); box-shadow: rgba(67, 97, =
238, 0.1) 0px 0px 0px 2px; }

.form-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(2=
50px, 1fr)); gap: 20px; }

.action-buttons { display: flex; gap: 10px; align-items: center; }

.action-buttons .btn { padding: 6px 12px; font-size: 12px; min-width: auto;=
 }

.status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font=
-weight: 500; }

.status-badge.active { background: rgb(212, 237, 218); color: rgb(21, 87, 3=
6); }

.status-badge.inactive { background: rgb(248, 215, 218); color: rgb(114, 28=
, 36); }

.btn-danger { background: var(--danger); color: white; }

.btn-danger:hover { background: rgb(200, 35, 51); }

.import-progress-overlay { position: fixed; top: 0px; left: 0px; width: 100=
%; height: 100%; background: rgba(0, 0, 0, 0.5); display: flex; justify-con=
tent: center; align-items: center; z-index: 9999; }

.import-progress-container { background: white; padding: 30px; border-radiu=
s: 12px; width: 450px; text-align: center; box-shadow: rgba(0, 0, 0, 0.3) 0=
px 10px 30px; }

.import-progress-title { font-size: 18px; font-weight: 600; margin-bottom: =
20px; color: var(--dark); }

.import-progress-bar { height: 12px; background: rgb(240, 240, 240); border=
-radius: 6px; overflow: hidden; margin-bottom: 15px; }

.import-progress-fill { height: 100%; width: 0%; background: linear-gradien=
t(90deg, var(--primary), #5a67d8); transition: width 0.3s; border-radius: 6=
px; }

.import-progress-text { font-size: 14px; color: var(--gray); margin-top: 10=
px; }

.large-modal .modal-content { width: 90%; max-width: 1000px; max-height: 90=
vh; overflow-y: auto; }

.form-grid { display: flex; flex-direction: column; gap: 25px; }

.form-section { background: rgb(248, 249, 250); padding: 20px; border-radiu=
s: 8px; border: 1px solid rgb(233, 236, 239); }

.form-section h4 { margin: 0px 0px 15px; color: var(--primary); font-size: =
16px; font-weight: 600; display: flex; align-items: center; gap: 8px; }

.form-section h4 i { font-size: 14px; }

.form-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(2=
50px, 1fr)); gap: 15px; margin-bottom: 15px; }

.form-row:last-child { margin-bottom: 0px; }

.form-group.full-width { grid-column: 1 / -1; }

.form-group label { display: block; margin-bottom: 6px; font-weight: 500; c=
olor: var(--dark); font-size: 14px; }

.form-group input, .form-group select, .form-group textarea { width: 100%; =
padding: 10px 12px; border: 1px solid rgb(221, 221, 221); border-radius: 6p=
x; font-size: 14px; transition: border-color 0.3s; background: white; }

.form-group input:focus, .form-group select:focus, .form-group textarea:foc=
us { outline: none; border-color: var(--primary); box-shadow: rgba(67, 97, =
238, 0.1) 0px 0px 0px 2px; }

.form-group input[readonly] { background: rgb(248, 249, 250); color: var(--=
gray); }

.form-group textarea { resize: vertical; min-height: 80px; }

.detail-grid { display: grid; grid-template-columns: repeat(auto-fit, minma=
x(250px, 1fr)); gap: 15px; }

.detail-item { display: flex; flex-direction: column; gap: 4px; }

.detail-item.full-width { grid-column: 1 / -1; }

.detail-item label { font-weight: 600; color: var(--gray); font-size: 12px;=
 text-transform: uppercase; letter-spacing: 0.5px; }

.detail-item span { color: var(--dark); font-size: 14px; padding: 8px 0px; =
border-bottom: 1px solid rgb(240, 240, 240); }

.search-box { position: relative; width: 300px; }

.search-box input { width: 100%; padding: 10px 40px 10px 12px; border: 1px =
solid rgb(221, 221, 221); border-radius: 6px; font-size: 14px; }

.search-box i { position: absolute; right: 12px; top: 50%; transform: trans=
lateY(-50%); color: var(--gray); }

.pagination { display: flex; justify-content: center; align-items: center; =
gap: 8px; margin-top: 20px; }

.pagination .btn { min-width: 40px; height: 40px; padding: 0px; display: fl=
ex; align-items: center; justify-content: center; }

@media (max-width: 768px) {
  .large-modal .modal-content { width: 95%; margin: 20px auto; }
  .form-row { grid-template-columns: 1fr; }
  .detail-grid { grid-template-columns: 1fr; }
  .search-box { width: 100%; margin-top: 10px; }
  .action-bar { flex-direction: column; align-items: stretch; gap: 10px; }
}

.filter-btn { background: transparent; border: 1px solid var(--secondary); =
color: var(--secondary); padding: 8px; border-radius: 6px; cursor: pointer;=
 font-size: 13px; transition: 0.3s; text-align: center; margin-bottom: 5px;=
 }

.filter-btn:hover { background: var(--secondary); color: white; }

@media (max-width: 1200px) {
  .funnel-overview { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 768px) {
  .main-content { flex-direction: column; }
  .sidebar { width: 100%; padding: 15px; }
  .dashboard-grid { grid-template-columns: 1fr; }
  .funnel-overview { grid-template-columns: 1fr; }
  .data-management { grid-template-columns: 1fr; }
  .module-header { flex-direction: column; text-align: center; gap: 15px; }
}

.data-table { width: 100%; border-collapse: collapse; background: white; ma=
rgin-bottom: 0px; }

.data-table th, .data-table td { padding: 12px; text-align: left; border-bo=
ttom: 1px solid rgb(233, 236, 239); font-size: 14px; }

.data-table th { background-color: rgb(248, 249, 250); font-weight: 600; co=
lor: rgb(73, 80, 87); position: sticky; top: 0px; z-index: 10; }

.data-table td { color: rgb(33, 37, 41); }

.data-table tbody tr:hover { background-color: rgb(248, 249, 250); }

.table-container { width: 100%; overflow-x: auto; margin-bottom: 20px; bord=
er-radius: 8px; border: 1px solid rgb(233, 236, 239); box-shadow: rgba(0, 0=
, 0, 0.05) 0px 2px 5px; }

.table-container .data-table { margin-bottom: 0px; min-width: 1000px; }

.data-table.compact { font-size: 12px; min-width: 600px; }

.data-table.compact th, .data-table.compact td { padding: 6px 4px; font-siz=
e: 11px; }

.data-table.compact .btn-sm { padding: 2px 4px; font-size: 10px; margin-rig=
ht: 2px; }

.inventory-table { font-size: 12px; min-width: 1200px; }

.inventory-table th, .inventory-table td { padding: 8px 6px; font-size: 12p=
x; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-widt=
h: 120px; }

.inventory-table .btn-sm { padding: 2px 6px; font-size: 10px; margin-right:=
 2px; }

.status { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weigh=
t: 500; display: inline-block; text-align: center; white-space: nowrap; }

.status-success { background-color: rgb(212, 237, 218); color: rgb(21, 87, =
36); border: 1px solid rgb(195, 230, 203); }

.status-warning { background-color: rgb(255, 243, 205); color: rgb(133, 100=
, 4); border: 1px solid rgb(255, 234, 167); }

.status-secondary { background-color: rgb(226, 227, 229); color: rgb(56, 61=
, 65); border: 1px solid rgb(214, 216, 219); }

.inventory-=E5=8F=AF=E5=94=AE { background-color: rgb(212, 237, 218); color=
: rgb(21, 87, 36); border: 1px solid rgb(195, 230, 203); }

.inventory-=E9=A2=84=E5=94=AE { background-color: rgb(255, 243, 205); color=
: rgb(133, 100, 4); border: 1px solid rgb(255, 234, 167); }

.inventory-=E6=95=B4=E5=A4=87 { background-color: rgb(226, 227, 229); color=
: rgb(56, 61, 65); border: 1px solid rgb(214, 216, 219); }

.inventory-=E5=B7=B2=E9=85=8D=E5=AF=B9 { background-color: rgb(204, 229, 25=
5); color: rgb(0, 64, 133); border: 1px solid rgb(179, 215, 255); }

.inventory-=E5=B7=B2=E4=BA=A4=E8=BD=A6 { background-color: rgb(209, 236, 24=
1); color: rgb(12, 84, 96); border: 1px solid rgb(190, 229, 235); }

.stock-age-good { background-color: rgb(212, 237, 218); color: rgb(21, 87, =
36); border: 1px solid rgb(195, 230, 203); }

.stock-age-warning { background-color: rgb(255, 243, 205); color: rgb(133, =
100, 4); border: 1px solid rgb(255, 234, 167); }

.stock-age-caution { background-color: rgb(255, 234, 167); color: rgb(133, =
100, 4); border: 1px solid rgb(253, 216, 53); }

.stock-age-danger { background-color: rgb(248, 215, 218); color: rgb(114, 2=
8, 36); border: 1px solid rgb(245, 198, 203); }

.order-details .detail-group { display: flex; align-items: center; margin-b=
ottom: 15px; }

.order-details .detail-group label { font-weight: 500; color: var(--text-co=
lor); min-width: 80px; margin-right: 10px; }

.order-details .detail-group span { color: var(--text-secondary); }

.order-details .detail-actions { border-top: 1px solid var(--border-color);=
 padding-top: 20px; }

.audit-=E5=BE=85=E5=AE=A1=E6=A0=B8 { background-color: rgb(255, 243, 205); =
color: rgb(133, 100, 4); border: 1px solid rgb(255, 234, 167); }

.audit-=E5=B7=B2=E5=AE=A1=E6=A0=B8 { background-color: rgb(212, 237, 218); =
color: rgb(21, 87, 36); border: 1px solid rgb(195, 230, 203); }

.audit-=E9=A9=B3=E5=9B=9E { background-color: rgb(248, 215, 218); color: rg=
b(114, 28, 36); border: 1px solid rgb(245, 198, 203); }

.delivery-=E5=BE=85=E4=BA=A4=E4=BB=98 { background-color: rgb(226, 227, 229=
); color: rgb(56, 61, 65); border: 1px solid rgb(214, 216, 219); }

.delivery-=E5=B7=B2=E9=85=8D=E8=BD=A6 { background-color: rgb(204, 229, 255=
); color: rgb(0, 64, 133); border: 1px solid rgb(179, 215, 255); }

.delivery-=E5=B7=B2=E4=BA=A4=E4=BB=98 { background-color: rgb(212, 237, 218=
); color: rgb(21, 87, 36); border: 1px solid rgb(195, 230, 203); }

.btn-sm { padding: 4px 8px; font-size: 12px; margin-right: 4px; white-space=
: nowrap; }

.btn-warning { background-color: rgb(255, 193, 7); color: rgb(33, 37, 41); =
border: 1px solid rgb(255, 193, 7); }

.btn-warning:hover { background-color: rgb(224, 168, 0); border-color: rgb(=
211, 158, 0); }

@media (max-width: 768px) {
  .table-container .data-table { min-width: 800px; }
  .data-table th, .data-table td { padding: 8px; font-size: 12px; }
  .btn-sm { padding: 2px 4px; font-size: 10px; }
}

.targets-container { display: grid; grid-template-columns: 1fr 1fr 1fr; gap=
: 20px; margin-bottom: 20px; }

.target-section { background: white; border-radius: var(--border-radius); b=
ox-shadow: var(--shadow); padding: 15px; }

.target-section h3 { font-size: 16px; font-weight: 600; color: var(--dark);=
 margin-bottom: 10px; display: flex; align-items: center; gap: 8px; }

.target-section h3 i { color: var(--primary); font-size: 14px; }

.target-actions { margin-bottom: 15px; }

.targets-table { width: 100%; table-layout: fixed; line-height: 1.2; font-s=
ize: 12px; }

.targets-table td, .targets-table th { padding: 6px 4px; text-align: center=
; font-size: 12px; border: 1px solid rgb(233, 236, 239); }

.targets-table th { background-color: rgb(248, 249, 250); font-weight: 600;=
 color: rgb(73, 80, 87); font-size: 11px; }

.targets-table td { color: rgb(33, 37, 41); }

.targets-table .completion-rate { font-weight: 600; font-size: 11px; }

.targets-table .completion-rate.high { color: rgb(40, 167, 69); }

.targets-table .completion-rate.medium { color: rgb(255, 193, 7); }

.targets-table .completion-rate.low { color: rgb(220, 53, 69); }

@media (max-width: 1200px) {
  .targets-container { grid-template-columns: 1fr; gap: 15px; }
  .target-section { padding: 12px; }
  .targets-table { font-size: 11px; }
  .targets-table td, .targets-table th { padding: 4px 2px; font-size: 10px;=
 }
}

.app-container .sidebar { width: 260px; background: white; padding: 25px 20=
px; display: flex; flex-direction: column; box-shadow: rgba(0, 0, 0, 0.08) =
0px 4px 12px; z-index: 10; height: 100vh; overflow-y: auto; }

.app-container .main-content { flex: 1 1 0%; display: flex; flex-direction:=
 column; overflow: hidden; }

.sidebar-header { margin-bottom: 30px; text-align: center; }

.sidebar-header h2 { color: var(--primary); font-size: 18px; font-weight: 6=
00; margin: 0px; }

.sidebar-content { flex: 1 1 0%; }

.content-header { background: white; padding: 20px 30px; border-bottom: 1px=
 solid rgb(233, 236, 239); box-shadow: rgba(0, 0, 0, 0.05) 0px 2px 4px; }

.content-header h1 { margin: 0px; color: var(--dark); }

@media (max-width: 768px) {
  .app-container { flex-direction: column; }
  .app-container .sidebar { width: 100%; height: auto; position: relative; =
}
  .app-container .main-content { margin-left: 0px; }
}

.tab-navigation { display: flex; border-bottom: 2px solid rgb(233, 236, 239=
); margin-bottom: 30px; }

.tab-btn { padding: 12px 24px; border-top: none; border-right: none; border=
-left: none; border-image: initial; background: none; font-size: 16px; font=
-weight: 500; color: rgb(108, 117, 125); border-bottom: 2px solid transpare=
nt; cursor: pointer; transition: 0.3s; display: flex; align-items: center; =
gap: 8px; }

.tab-btn:hover { color: var(--primary); background: rgba(67, 97, 238, 0.05)=
; }

.tab-btn.active { color: var(--primary); border-bottom-color: var(--primary=
); }

.tab-header { padding: 20px 25px; border-bottom: 1px solid var(--light-gray=
); display: flex; justify-content: space-between; align-items: center; back=
ground: rgb(248, 249, 250); }

.tab-header h2 { font-size: 18px; font-weight: 600; display: flex; align-it=
ems: center; gap: 10px; margin: 0px; }

.tab-header h2 i { color: var(--primary); }

.tab-content { min-height: 500px; }

.table-responsive { overflow-x: auto; border-radius: 8px; border: 1px solid=
 rgb(233, 236, 239); box-shadow: rgba(0, 0, 0, 0.05) 0px 2px 5px; }

.table { width: 100%; border-collapse: collapse; background: white; margin:=
 0px; min-width: 1000px; }

.table th, .table td { padding: 12px; text-align: left; border-bottom: 1px =
solid rgb(233, 236, 239); font-size: 14px; vertical-align: middle; }

.table th { background-color: rgb(248, 249, 250); font-weight: 600; color: =
rgb(73, 80, 87); position: sticky; top: 0px; z-index: 10; }

.table td { color: rgb(33, 37, 41); }

.table tbody tr:hover { background-color: rgb(248, 249, 250); }

.empty-state { text-align: center; padding: 60px 20px; color: rgb(108, 117,=
 125); }

.empty-state i { font-size: 48px; color: rgb(222, 226, 230); margin-bottom:=
 16px; display: block; }

.empty-state h3 { color: rgb(108, 117, 125); margin-bottom: 8px; font-size:=
 18px; }

.empty-state p { color: rgb(173, 181, 189); font-size: 14px; margin: 0px; }

.modal-overlay { position: fixed; top: 0px; left: 0px; width: 100%; height:=
 100%; background: rgba(0, 0, 0, 0.5); display: flex; justify-content: cent=
er; align-items: center; z-index: 1000; }

.modal-content { background: white; border-radius: 8px; box-shadow: rgba(0,=
 0, 0, 0.3) 0px 10px 30px; width: 90%; max-width: 600px; max-height: 90vh; =
overflow-y: auto; }

.modal-header { padding: 20px 25px; border-bottom: 1px solid rgb(233, 236, =
239); display: flex; justify-content: space-between; align-items: center; }

.modal-header h3 { margin: 0px; font-size: 18px; font-weight: 600; color: v=
ar(--dark); }

.modal-close { background: none; border: none; font-size: 20px; color: rgb(=
108, 117, 125); cursor: pointer; padding: 0px; width: 30px; height: 30px; d=
isplay: flex; align-items: center; justify-content: center; border-radius: =
4px; transition: 0.3s; }

.modal-close:hover { background: rgb(248, 249, 250); color: var(--dark); }

.modal-body { padding: 25px; }

.modal-footer { padding: 20px 25px; border-top: 1px solid rgb(233, 236, 239=
); display: flex; justify-content: flex-end; gap: 10px; }

.form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(=
250px, 1fr)); gap: 20px; }

.form-group.full-width { grid-column: 1 / -1; }

.text-success { color: rgb(40, 167, 69) !important; }

.text-warning { color: rgb(255, 193, 7) !important; }

.text-danger { color: rgb(220, 53, 69) !important; }

.filter-section { padding: 20px 25px; background: rgb(248, 249, 250); borde=
r-bottom: 1px solid rgb(233, 236, 239); }

.filter-row { display: flex; gap: 20px; align-items: end; flex-wrap: wrap; =
}

.filter-group { display: flex; flex-direction: column; min-width: 150px; }

.filter-group label { font-size: 13px; font-weight: 500; color: rgb(73, 80,=
 87); margin-bottom: 5px; }

.filter-group input, .filter-group select { padding: 8px 12px; border: 1px =
solid rgb(206, 212, 218); border-radius: 4px; font-size: 14px; background: =
white; }

.filter-group input:focus, .filter-group select:focus { outline: none; bord=
er-color: var(--primary); box-shadow: rgba(67, 97, 238, 0.1) 0px 0px 0px 2p=
x; }

.pagination-container { display: flex; justify-content: space-between; alig=
n-items: center; padding: 20px 25px; border-top: 1px solid rgb(233, 236, 23=
9); background: rgb(248, 249, 250); }

.pagination-info { font-size: 14px; color: rgb(108, 117, 125); }

.pagination-controls { display: flex; gap: 5px; align-items: center; }

.pagination-controls .btn { min-width: 40px; height: 32px; display: flex; a=
lign-items: center; justify-content: center; font-size: 13px; }

.header-actions { display: flex; gap: 10px; align-items: center; }

.action-column { width: 120px; text-align: center; }

.action-buttons { display: flex; gap: 5px; justify-content: center; align-i=
tems: center; }

.action-buttons .btn { min-width: 32px; height: 32px; padding: 0px; display=
: flex; align-items: center; justify-content: center; }

.column-settings { max-height: 400px; overflow-y: auto; }

.column-setting-item { padding: 8px 0px; border-bottom: 1px solid rgb(240, =
240, 240); }

.column-setting-item:last-child { border-bottom: none; }

.column-setting-item label { display: flex; align-items: center; gap: 10px;=
 cursor: pointer; font-size: 14px; }

.column-setting-item input[type=3D"checkbox"] { margin: 0px; }

.import-section { padding: 10px 0px; }

.import-template { margin-top: 20px; padding: 15px; background: rgb(248, 24=
9, 250); border-radius: 6px; border: 1px solid rgb(233, 236, 239); }

.import-template h4 { margin: 0px 0px 10px; font-size: 16px; color: var(--d=
ark); }

.import-template p { margin: 0px 0px 10px; font-size: 14px; color: rgb(108,=
 117, 125); }

.import-template ul { margin: 10px 0px; padding-left: 20px; font-size: 13px=
; color: rgb(73, 80, 87); }

.import-template li { margin-bottom: 3px; }

@media (max-width: 768px) {
  .tab-navigation { flex-wrap: wrap; gap: 5px; }
  .tab-btn { padding: 8px 16px; font-size: 14px; }
  .table { min-width: 800px; }
  .table th, .table td { padding: 8px; font-size: 12px; }
  .modal-content { width: 95%; margin: 20px auto; }
  .form-grid { grid-template-columns: 1fr; }
  .filter-row { flex-direction: column; gap: 15px; }
  .filter-group { min-width: 100%; }
  .header-actions { flex-wrap: wrap; gap: 8px; }
  .pagination-container { flex-direction: column; gap: 15px; text-align: ce=
nter; }
  .pagination-controls { flex-wrap: wrap; justify-content: center; }
  .action-buttons { flex-direction: column; gap: 3px; }
}
------MultipartBoundary--tCdgLTemwq0jS7q9jNLfyJgUywxjTTSi7GqPcLUxlP----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css

@charset "utf-8";

.fa { font-family: var(--fa-style-family,"Font Awesome 6 Free"); font-weigh=
t: var(--fa-style,900); }

.fa, .fa-brands, .fa-classic, .fa-regular, .fa-sharp, .fa-solid, .fab, .far=
, .fas { -webkit-font-smoothing: antialiased; display: var(--fa-display,inl=
ine-block); font-style: normal; font-variant: normal; line-height: 1; text-=
rendering: auto; }

.fa-classic, .fa-regular, .fa-solid, .far, .fas { font-family: "Font Awesom=
e 6 Free"; }

.fa-brands, .fab { font-family: "Font Awesome 6 Brands"; }

.fa-1x { font-size: 1em; }

.fa-2x { font-size: 2em; }

.fa-3x { font-size: 3em; }

.fa-4x { font-size: 4em; }

.fa-5x { font-size: 5em; }

.fa-6x { font-size: 6em; }

.fa-7x { font-size: 7em; }

.fa-8x { font-size: 8em; }

.fa-9x { font-size: 9em; }

.fa-10x { font-size: 10em; }

.fa-2xs { font-size: 0.625em; line-height: 0.1em; vertical-align: 0.225em; =
}

.fa-xs { font-size: 0.75em; line-height: 0.08333em; vertical-align: 0.125em=
; }

.fa-sm { font-size: 0.875em; line-height: 0.07143em; vertical-align: 0.0535=
7em; }

.fa-lg { font-size: 1.25em; line-height: 0.05em; vertical-align: -0.075em; =
}

.fa-xl { font-size: 1.5em; line-height: 0.04167em; vertical-align: -0.125em=
; }

.fa-2xl { font-size: 2em; line-height: 0.03125em; vertical-align: -0.1875em=
; }

.fa-fw { text-align: center; width: 1.25em; }

.fa-ul { list-style-type: none; margin-left: var(--fa-li-margin,2.5em); pad=
ding-left: 0px; }

.fa-ul > li { position: relative; }

.fa-li { left: calc(var(--fa-li-width, 2em)*-1); position: absolute; text-a=
lign: center; width: var(--fa-li-width,2em); line-height: inherit; }

.fa-border { border-radius: var(--fa-border-radius,.1em); border: var(--fa-=
border-width,.08em) var(--fa-border-style,solid) var(--fa-border-color,#eee=
); padding: var(--fa-border-padding,.2em .25em .15em); }

.fa-pull-left { float: left; margin-right: var(--fa-pull-margin,.3em); }

.fa-pull-right { float: right; margin-left: var(--fa-pull-margin,.3em); }

.fa-beat { animation-name: fa-beat; animation-delay: var(--fa-animation-del=
ay,0s); animation-direction: var(--fa-animation-direction,normal); animatio=
n-duration: var(--fa-animation-duration,1s); animation-iteration-count: var=
(--fa-animation-iteration-count,infinite); animation-timing-function: var(-=
-fa-animation-timing,ease-in-out); }

.fa-bounce { animation-name: fa-bounce; animation-delay: var(--fa-animation=
-delay,0s); animation-direction: var(--fa-animation-direction,normal); anim=
ation-duration: var(--fa-animation-duration,1s); animation-iteration-count:=
 var(--fa-animation-iteration-count,infinite); animation-timing-function: v=
ar(--fa-animation-timing,cubic-bezier(.28,.84,.42,1)); }

.fa-fade { animation-name: fa-fade; animation-iteration-count: var(--fa-ani=
mation-iteration-count,infinite); animation-timing-function: var(--fa-anima=
tion-timing,cubic-bezier(.4,0,.6,1)); }

.fa-beat-fade, .fa-fade { animation-delay: var(--fa-animation-delay,0s); an=
imation-direction: var(--fa-animation-direction,normal); animation-duration=
: var(--fa-animation-duration,1s); }

.fa-beat-fade { animation-name: fa-beat-fade; animation-iteration-count: va=
r(--fa-animation-iteration-count,infinite); animation-timing-function: var(=
--fa-animation-timing,cubic-bezier(.4,0,.6,1)); }

.fa-flip { animation-name: fa-flip; animation-delay: var(--fa-animation-del=
ay,0s); animation-direction: var(--fa-animation-direction,normal); animatio=
n-duration: var(--fa-animation-duration,1s); animation-iteration-count: var=
(--fa-animation-iteration-count,infinite); animation-timing-function: var(-=
-fa-animation-timing,ease-in-out); }

.fa-shake { animation-name: fa-shake; animation-duration: var(--fa-animatio=
n-duration,1s); animation-iteration-count: var(--fa-animation-iteration-cou=
nt,infinite); animation-timing-function: var(--fa-animation-timing,linear);=
 }

.fa-shake, .fa-spin { animation-delay: var(--fa-animation-delay,0s); animat=
ion-direction: var(--fa-animation-direction,normal); }

.fa-spin { animation-name: fa-spin; animation-duration: var(--fa-animation-=
duration,2s); animation-iteration-count: var(--fa-animation-iteration-count=
,infinite); animation-timing-function: var(--fa-animation-timing,linear); }

.fa-spin-reverse { --fa-animation-direction: reverse; }

.fa-pulse, .fa-spin-pulse { animation-name: fa-spin; animation-direction: v=
ar(--fa-animation-direction,normal); animation-duration: var(--fa-animation=
-duration,1s); animation-iteration-count: var(--fa-animation-iteration-coun=
t,infinite); animation-timing-function: var(--fa-animation-timing,steps(8))=
; }

@media (prefers-reduced-motion: reduce) {
  .fa-beat, .fa-beat-fade, .fa-bounce, .fa-fade, .fa-flip, .fa-pulse, .fa-s=
hake, .fa-spin, .fa-spin-pulse { animation-delay: -1ms; animation-duration:=
 1ms; animation-iteration-count: 1; transition-delay: 0s; transition-durati=
on: 0s; }
}

@-webkit-keyframes fa-beat {=20
  0%, 90% { transform: scale(1); }
  45% { transform: scale(var(--fa-beat-scale,1.25)); }
}

@keyframes fa-beat {=20
  0%, 90% { transform: scale(1); }
  45% { transform: scale(var(--fa-beat-scale,1.25)); }
}

@-webkit-keyframes fa-bounce {=20
  0% { transform: scale(1) translateY(0px); }
  10% { transform: scale(var(--fa-bounce-start-scale-x,1.1),var(--fa-bounce=
-start-scale-y,.9)) translateY(0); }
  30% { transform: scale(var(--fa-bounce-jump-scale-x,.9),var(--fa-bounce-j=
ump-scale-y,1.1)) translateY(var(--fa-bounce-height,-.5em)); }
  50% { transform: scale(var(--fa-bounce-land-scale-x,1.05),var(--fa-bounce=
-land-scale-y,.95)) translateY(0); }
  57% { transform: scale(1) translateY(var(--fa-bounce-rebound,-.125em)); }
  64% { transform: scale(1) translateY(0px); }
  100% { transform: scale(1) translateY(0px); }
}

@keyframes fa-bounce {=20
  0% { transform: scale(1) translateY(0px); }
  10% { transform: scale(var(--fa-bounce-start-scale-x,1.1),var(--fa-bounce=
-start-scale-y,.9)) translateY(0); }
  30% { transform: scale(var(--fa-bounce-jump-scale-x,.9),var(--fa-bounce-j=
ump-scale-y,1.1)) translateY(var(--fa-bounce-height,-.5em)); }
  50% { transform: scale(var(--fa-bounce-land-scale-x,1.05),var(--fa-bounce=
-land-scale-y,.95)) translateY(0); }
  57% { transform: scale(1) translateY(var(--fa-bounce-rebound,-.125em)); }
  64% { transform: scale(1) translateY(0px); }
  100% { transform: scale(1) translateY(0px); }
}

@-webkit-keyframes fa-fade {=20
  50% { opacity: var(--fa-fade-opacity,.4); }
}

@keyframes fa-fade {=20
  50% { opacity: var(--fa-fade-opacity,.4); }
}

@-webkit-keyframes fa-beat-fade {=20
  0%, 100% { opacity: var(--fa-beat-fade-opacity,.4); transform: scale(1); =
}
  50% { opacity: 1; transform: scale(var(--fa-beat-fade-scale,1.125)); }
}

@keyframes fa-beat-fade {=20
  0%, 100% { opacity: var(--fa-beat-fade-opacity,.4); transform: scale(1); =
}
  50% { opacity: 1; transform: scale(var(--fa-beat-fade-scale,1.125)); }
}

@-webkit-keyframes fa-flip {=20
  50% { transform: rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-=
flip-z,0),var(--fa-flip-angle,-180deg)); }
}

@keyframes fa-flip {=20
  50% { transform: rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-=
flip-z,0),var(--fa-flip-angle,-180deg)); }
}

@-webkit-keyframes fa-shake {=20
  0% { transform: rotate(-15deg); }
  4% { transform: rotate(15deg); }
  8%, 24% { transform: rotate(-18deg); }
  12%, 28% { transform: rotate(18deg); }
  16% { transform: rotate(-22deg); }
  20% { transform: rotate(22deg); }
  32% { transform: rotate(-12deg); }
  36% { transform: rotate(12deg); }
  40%, 100% { transform: rotate(0deg); }
}

@keyframes fa-shake {=20
  0% { transform: rotate(-15deg); }
  4% { transform: rotate(15deg); }
  8%, 24% { transform: rotate(-18deg); }
  12%, 28% { transform: rotate(18deg); }
  16% { transform: rotate(-22deg); }
  20% { transform: rotate(22deg); }
  32% { transform: rotate(-12deg); }
  36% { transform: rotate(12deg); }
  40%, 100% { transform: rotate(0deg); }
}

@-webkit-keyframes fa-spin {=20
  0% { transform: rotate(0deg); }
  100% { transform: rotate(1turn); }
}

@keyframes fa-spin {=20
  0% { transform: rotate(0deg); }
  100% { transform: rotate(1turn); }
}

.fa-rotate-90 { transform: rotate(90deg); }

.fa-rotate-180 { transform: rotate(180deg); }

.fa-rotate-270 { transform: rotate(270deg); }

.fa-flip-horizontal { transform: scaleX(-1); }

.fa-flip-vertical { transform: scaleY(-1); }

.fa-flip-both, .fa-flip-horizontal.fa-flip-vertical { transform: scale(-1);=
 }

.fa-rotate-by { transform: rotate(var(--fa-rotate-angle,none)); }

.fa-stack { display: inline-block; height: 2em; line-height: 2em; position:=
 relative; vertical-align: middle; width: 2.5em; }

.fa-stack-1x, .fa-stack-2x { left: 0px; position: absolute; text-align: cen=
ter; width: 100%; z-index: var(--fa-stack-z-index,auto); }

.fa-stack-1x { line-height: inherit; }

.fa-stack-2x { font-size: 2em; }

.fa-inverse { color: var(--fa-inverse,#fff); }

.fa-0::before { content: "0"; }

.fa-1::before { content: "1"; }

.fa-2::before { content: "2"; }

.fa-3::before { content: "3"; }

.fa-4::before { content: "4"; }

.fa-5::before { content: "5"; }

.fa-6::before { content: "6"; }

.fa-7::before { content: "7"; }

.fa-8::before { content: "8"; }

.fa-9::before { content: "9"; }

.fa-fill-drip::before { content: "=EF=95=B6"; }

.fa-arrows-to-circle::before { content: "=EE=92=BD"; }

.fa-chevron-circle-right::before, .fa-circle-chevron-right::before { conten=
t: "=EF=84=B8"; }

.fa-at::before { content: "@"; }

.fa-trash-alt::before, .fa-trash-can::before { content: "=EF=8B=AD"; }

.fa-text-height::before { content: "=EF=80=B4"; }

.fa-user-times::before, .fa-user-xmark::before { content: "=EF=88=B5"; }

.fa-stethoscope::before { content: "=EF=83=B1"; }

.fa-comment-alt::before, .fa-message::before { content: "=EF=89=BA"; }

.fa-info::before { content: "=EF=84=A9"; }

.fa-compress-alt::before, .fa-down-left-and-up-right-to-center::before { co=
ntent: "=EF=90=A2"; }

.fa-explosion::before { content: "=EE=93=A9"; }

.fa-file-alt::before, .fa-file-lines::before, .fa-file-text::before { conte=
nt: "=EF=85=9C"; }

.fa-wave-square::before { content: "=EF=A0=BE"; }

.fa-ring::before { content: "=EF=9C=8B"; }

.fa-building-un::before { content: "=EE=93=99"; }

.fa-dice-three::before { content: "=EF=94=A7"; }

.fa-calendar-alt::before, .fa-calendar-days::before { content: "=EF=81=B3";=
 }

.fa-anchor-circle-check::before { content: "=EE=92=AA"; }

.fa-building-circle-arrow-right::before { content: "=EE=93=91"; }

.fa-volleyball-ball::before, .fa-volleyball::before { content: "=EF=91=9F";=
 }

.fa-arrows-up-to-line::before { content: "=EE=93=82"; }

.fa-sort-desc::before, .fa-sort-down::before { content: "=EF=83=9D"; }

.fa-circle-minus::before, .fa-minus-circle::before { content: "=EF=81=96"; =
}

.fa-door-open::before { content: "=EF=94=AB"; }

.fa-right-from-bracket::before, .fa-sign-out-alt::before { content: "=EF=8B=
=B5"; }

.fa-atom::before { content: "=EF=97=92"; }

.fa-soap::before { content: "=EE=81=AE"; }

.fa-heart-music-camera-bolt::before, .fa-icons::before { content: "=EF=A1=
=AD"; }

.fa-microphone-alt-slash::before, .fa-microphone-lines-slash::before { cont=
ent: "=EF=94=B9"; }

.fa-bridge-circle-check::before { content: "=EE=93=89"; }

.fa-pump-medical::before { content: "=EE=81=AA"; }

.fa-fingerprint::before { content: "=EF=95=B7"; }

.fa-hand-point-right::before { content: "=EF=82=A4"; }

.fa-magnifying-glass-location::before, .fa-search-location::before { conten=
t: "=EF=9A=89"; }

.fa-forward-step::before, .fa-step-forward::before { content: "=EF=81=91"; =
}

.fa-face-smile-beam::before, .fa-smile-beam::before { content: "=EF=96=B8";=
 }

.fa-flag-checkered::before { content: "=EF=84=9E"; }

.fa-football-ball::before, .fa-football::before { content: "=EF=91=8E"; }

.fa-school-circle-exclamation::before { content: "=EE=95=AC"; }

.fa-crop::before { content: "=EF=84=A5"; }

.fa-angle-double-down::before, .fa-angles-down::before { content: "=EF=84=
=83"; }

.fa-users-rectangle::before { content: "=EE=96=94"; }

.fa-people-roof::before { content: "=EE=94=B7"; }

.fa-people-line::before { content: "=EE=94=B4"; }

.fa-beer-mug-empty::before, .fa-beer::before { content: "=EF=83=BC"; }

.fa-diagram-predecessor::before { content: "=EE=91=B7"; }

.fa-arrow-up-long::before, .fa-long-arrow-up::before { content: "=EF=85=B6"=
; }

.fa-burn::before, .fa-fire-flame-simple::before { content: "=EF=91=AA"; }

.fa-male::before, .fa-person::before { content: "=EF=86=83"; }

.fa-laptop::before { content: "=EF=84=89"; }

.fa-file-csv::before { content: "=EF=9B=9D"; }

.fa-menorah::before { content: "=EF=99=B6"; }

.fa-truck-plane::before { content: "=EE=96=8F"; }

.fa-record-vinyl::before { content: "=EF=A3=99"; }

.fa-face-grin-stars::before, .fa-grin-stars::before { content: "=EF=96=87";=
 }

.fa-bong::before { content: "=EF=95=9C"; }

.fa-pastafarianism::before, .fa-spaghetti-monster-flying::before { content:=
 "=EF=99=BB"; }

.fa-arrow-down-up-across-line::before { content: "=EE=92=AF"; }

.fa-spoon::before, .fa-utensil-spoon::before { content: "=EF=8B=A5"; }

.fa-jar-wheat::before { content: "=EE=94=97"; }

.fa-envelopes-bulk::before, .fa-mail-bulk::before { content: "=EF=99=B4"; }

.fa-file-circle-exclamation::before { content: "=EE=93=AB"; }

.fa-circle-h::before, .fa-hospital-symbol::before { content: "=EF=91=BE"; }

.fa-pager::before { content: "=EF=A0=95"; }

.fa-address-book::before, .fa-contact-book::before { content: "=EF=8A=B9"; =
}

.fa-strikethrough::before { content: "=EF=83=8C"; }

.fa-k::before { content: "K"; }

.fa-landmark-flag::before { content: "=EE=94=9C"; }

.fa-pencil-alt::before, .fa-pencil::before { content: "=EF=8C=83"; }

.fa-backward::before { content: "=EF=81=8A"; }

.fa-caret-right::before { content: "=EF=83=9A"; }

.fa-comments::before { content: "=EF=82=86"; }

.fa-file-clipboard::before, .fa-paste::before { content: "=EF=83=AA"; }

.fa-code-pull-request::before { content: "=EE=84=BC"; }

.fa-clipboard-list::before { content: "=EF=91=AD"; }

.fa-truck-loading::before, .fa-truck-ramp-box::before { content: "=EF=93=9E=
"; }

.fa-user-check::before { content: "=EF=93=BC"; }

.fa-vial-virus::before { content: "=EE=96=97"; }

.fa-sheet-plastic::before { content: "=EE=95=B1"; }

.fa-blog::before { content: "=EF=9E=81"; }

.fa-user-ninja::before { content: "=EF=94=84"; }

.fa-person-arrow-up-from-line::before { content: "=EE=94=B9"; }

.fa-scroll-torah::before, .fa-torah::before { content: "=EF=9A=A0"; }

.fa-broom-ball::before, .fa-quidditch-broom-ball::before, .fa-quidditch::be=
fore { content: "=EF=91=98"; }

.fa-toggle-off::before { content: "=EF=88=84"; }

.fa-archive::before, .fa-box-archive::before { content: "=EF=86=87"; }

.fa-person-drowning::before { content: "=EE=95=85"; }

.fa-arrow-down-9-1::before, .fa-sort-numeric-desc::before, .fa-sort-numeric=
-down-alt::before { content: "=EF=A2=86"; }

.fa-face-grin-tongue-squint::before, .fa-grin-tongue-squint::before { conte=
nt: "=EF=96=8A"; }

.fa-spray-can::before { content: "=EF=96=BD"; }

.fa-truck-monster::before { content: "=EF=98=BB"; }

.fa-w::before { content: "W"; }

.fa-earth-africa::before, .fa-globe-africa::before { content: "=EF=95=BC"; =
}

.fa-rainbow::before { content: "=EF=9D=9B"; }

.fa-circle-notch::before { content: "=EF=87=8E"; }

.fa-tablet-alt::before, .fa-tablet-screen-button::before { content: "=EF=8F=
=BA"; }

.fa-paw::before { content: "=EF=86=B0"; }

.fa-cloud::before { content: "=EF=83=82"; }

.fa-trowel-bricks::before { content: "=EE=96=8A"; }

.fa-face-flushed::before, .fa-flushed::before { content: "=EF=95=B9"; }

.fa-hospital-user::before { content: "=EF=A0=8D"; }

.fa-tent-arrow-left-right::before { content: "=EE=95=BF"; }

.fa-gavel::before, .fa-legal::before { content: "=EF=83=A3"; }

.fa-binoculars::before { content: "=EF=87=A5"; }

.fa-microphone-slash::before { content: "=EF=84=B1"; }

.fa-box-tissue::before { content: "=EE=81=9B"; }

.fa-motorcycle::before { content: "=EF=88=9C"; }

.fa-bell-concierge::before, .fa-concierge-bell::before { content: "=EF=95=
=A2"; }

.fa-pen-ruler::before, .fa-pencil-ruler::before { content: "=EF=96=AE"; }

.fa-people-arrows-left-right::before, .fa-people-arrows::before { content: =
"=EE=81=A8"; }

.fa-mars-and-venus-burst::before { content: "=EE=94=A3"; }

.fa-caret-square-right::before, .fa-square-caret-right::before { content: "=
=EF=85=92"; }

.fa-cut::before, .fa-scissors::before { content: "=EF=83=84"; }

.fa-sun-plant-wilt::before { content: "=EE=95=BA"; }

.fa-toilets-portable::before { content: "=EE=96=84"; }

.fa-hockey-puck::before { content: "=EF=91=93"; }

.fa-table::before { content: "=EF=83=8E"; }

.fa-magnifying-glass-arrow-right::before { content: "=EE=94=A1"; }

.fa-digital-tachograph::before, .fa-tachograph-digital::before { content: "=
=EF=95=A6"; }

.fa-users-slash::before { content: "=EE=81=B3"; }

.fa-clover::before { content: "=EE=84=B9"; }

.fa-mail-reply::before, .fa-reply::before { content: "=EF=8F=A5"; }

.fa-star-and-crescent::before { content: "=EF=9A=99"; }

.fa-house-fire::before { content: "=EE=94=8C"; }

.fa-minus-square::before, .fa-square-minus::before { content: "=EF=85=86"; =
}

.fa-helicopter::before { content: "=EF=94=B3"; }

.fa-compass::before { content: "=EF=85=8E"; }

.fa-caret-square-down::before, .fa-square-caret-down::before { content: "=
=EF=85=90"; }

.fa-file-circle-question::before { content: "=EE=93=AF"; }

.fa-laptop-code::before { content: "=EF=97=BC"; }

.fa-swatchbook::before { content: "=EF=97=83"; }

.fa-prescription-bottle::before { content: "=EF=92=85"; }

.fa-bars::before, .fa-navicon::before { content: "=EF=83=89"; }

.fa-people-group::before { content: "=EE=94=B3"; }

.fa-hourglass-3::before, .fa-hourglass-end::before { content: "=EF=89=93"; =
}

.fa-heart-broken::before, .fa-heart-crack::before { content: "=EF=9E=A9"; }

.fa-external-link-square-alt::before, .fa-square-up-right::before { content=
: "=EF=8D=A0"; }

.fa-face-kiss-beam::before, .fa-kiss-beam::before { content: "=EF=96=97"; }

.fa-film::before { content: "=EF=80=88"; }

.fa-ruler-horizontal::before { content: "=EF=95=87"; }

.fa-people-robbery::before { content: "=EE=94=B6"; }

.fa-lightbulb::before { content: "=EF=83=AB"; }

.fa-caret-left::before { content: "=EF=83=99"; }

.fa-circle-exclamation::before, .fa-exclamation-circle::before { content: "=
=EF=81=AA"; }

.fa-school-circle-xmark::before { content: "=EE=95=AD"; }

.fa-arrow-right-from-bracket::before, .fa-sign-out::before { content: "=EF=
=82=8B"; }

.fa-chevron-circle-down::before, .fa-circle-chevron-down::before { content:=
 "=EF=84=BA"; }

.fa-unlock-alt::before, .fa-unlock-keyhole::before { content: "=EF=84=BE"; =
}

.fa-cloud-showers-heavy::before { content: "=EF=9D=80"; }

.fa-headphones-alt::before, .fa-headphones-simple::before { content: "=EF=
=96=8F"; }

.fa-sitemap::before { content: "=EF=83=A8"; }

.fa-circle-dollar-to-slot::before, .fa-donate::before { content: "=EF=92=B9=
"; }

.fa-memory::before { content: "=EF=94=B8"; }

.fa-road-spikes::before { content: "=EE=95=A8"; }

.fa-fire-burner::before { content: "=EE=93=B1"; }

.fa-flag::before { content: "=EF=80=A4"; }

.fa-hanukiah::before { content: "=EF=9B=A6"; }

.fa-feather::before { content: "=EF=94=AD"; }

.fa-volume-down::before, .fa-volume-low::before { content: "=EF=80=A7"; }

.fa-comment-slash::before { content: "=EF=92=B3"; }

.fa-cloud-sun-rain::before { content: "=EF=9D=83"; }

.fa-compress::before { content: "=EF=81=A6"; }

.fa-wheat-alt::before, .fa-wheat-awn::before { content: "=EE=8B=8D"; }

.fa-ankh::before { content: "=EF=99=84"; }

.fa-hands-holding-child::before { content: "=EE=93=BA"; }

.fa-asterisk::before { content: "*"; }

.fa-check-square::before, .fa-square-check::before { content: "=EF=85=8A"; =
}

.fa-peseta-sign::before { content: "=EE=88=A1"; }

.fa-header::before, .fa-heading::before { content: "=EF=87=9C"; }

.fa-ghost::before { content: "=EF=9B=A2"; }

.fa-list-squares::before, .fa-list::before { content: "=EF=80=BA"; }

.fa-phone-square-alt::before, .fa-square-phone-flip::before { content: "=EF=
=A1=BB"; }

.fa-cart-plus::before { content: "=EF=88=97"; }

.fa-gamepad::before { content: "=EF=84=9B"; }

.fa-circle-dot::before, .fa-dot-circle::before { content: "=EF=86=92"; }

.fa-dizzy::before, .fa-face-dizzy::before { content: "=EF=95=A7"; }

.fa-egg::before { content: "=EF=9F=BB"; }

.fa-house-medical-circle-xmark::before { content: "=EE=94=93"; }

.fa-campground::before { content: "=EF=9A=BB"; }

.fa-folder-plus::before { content: "=EF=99=9E"; }

.fa-futbol-ball::before, .fa-futbol::before, .fa-soccer-ball::before { cont=
ent: "=EF=87=A3"; }

.fa-paint-brush::before, .fa-paintbrush::before { content: "=EF=87=BC"; }

.fa-lock::before { content: "=EF=80=A3"; }

.fa-gas-pump::before { content: "=EF=94=AF"; }

.fa-hot-tub-person::before, .fa-hot-tub::before { content: "=EF=96=93"; }

.fa-map-location::before, .fa-map-marked::before { content: "=EF=96=9F"; }

.fa-house-flood-water::before { content: "=EE=94=8E"; }

.fa-tree::before { content: "=EF=86=BB"; }

.fa-bridge-lock::before { content: "=EE=93=8C"; }

.fa-sack-dollar::before { content: "=EF=A0=9D"; }

.fa-edit::before, .fa-pen-to-square::before { content: "=EF=81=84"; }

.fa-car-side::before { content: "=EF=97=A4"; }

.fa-share-alt::before, .fa-share-nodes::before { content: "=EF=87=A0"; }

.fa-heart-circle-minus::before { content: "=EE=93=BF"; }

.fa-hourglass-2::before, .fa-hourglass-half::before { content: "=EF=89=92";=
 }

.fa-microscope::before { content: "=EF=98=90"; }

.fa-sink::before { content: "=EE=81=AD"; }

.fa-bag-shopping::before, .fa-shopping-bag::before { content: "=EF=8A=90"; =
}

.fa-arrow-down-z-a::before, .fa-sort-alpha-desc::before, .fa-sort-alpha-dow=
n-alt::before { content: "=EF=A2=81"; }

.fa-mitten::before { content: "=EF=9E=B5"; }

.fa-person-rays::before { content: "=EE=95=8D"; }

.fa-users::before { content: "=EF=83=80"; }

.fa-eye-slash::before { content: "=EF=81=B0"; }

.fa-flask-vial::before { content: "=EE=93=B3"; }

.fa-hand-paper::before, .fa-hand::before { content: "=EF=89=96"; }

.fa-om::before { content: "=EF=99=B9"; }

.fa-worm::before { content: "=EE=96=99"; }

.fa-house-circle-xmark::before { content: "=EE=94=8B"; }

.fa-plug::before { content: "=EF=87=A6"; }

.fa-chevron-up::before { content: "=EF=81=B7"; }

.fa-hand-spock::before { content: "=EF=89=99"; }

.fa-stopwatch::before { content: "=EF=8B=B2"; }

.fa-face-kiss::before, .fa-kiss::before { content: "=EF=96=96"; }

.fa-bridge-circle-xmark::before { content: "=EE=93=8B"; }

.fa-face-grin-tongue::before, .fa-grin-tongue::before { content: "=EF=96=89=
"; }

.fa-chess-bishop::before { content: "=EF=90=BA"; }

.fa-face-grin-wink::before, .fa-grin-wink::before { content: "=EF=96=8C"; }

.fa-deaf::before, .fa-deafness::before, .fa-ear-deaf::before, .fa-hard-of-h=
earing::before { content: "=EF=8A=A4"; }

.fa-road-circle-check::before { content: "=EE=95=A4"; }

.fa-dice-five::before { content: "=EF=94=A3"; }

.fa-rss-square::before, .fa-square-rss::before { content: "=EF=85=83"; }

.fa-land-mine-on::before { content: "=EE=94=9B"; }

.fa-i-cursor::before { content: "=EF=89=86"; }

.fa-stamp::before { content: "=EF=96=BF"; }

.fa-stairs::before { content: "=EE=8A=89"; }

.fa-i::before { content: "I"; }

.fa-hryvnia-sign::before, .fa-hryvnia::before { content: "=EF=9B=B2"; }

.fa-pills::before { content: "=EF=92=84"; }

.fa-face-grin-wide::before, .fa-grin-alt::before { content: "=EF=96=81"; }

.fa-tooth::before { content: "=EF=97=89"; }

.fa-v::before { content: "V"; }

.fa-bangladeshi-taka-sign::before { content: "=EE=8B=A6"; }

.fa-bicycle::before { content: "=EF=88=86"; }

.fa-rod-asclepius::before, .fa-rod-snake::before, .fa-staff-aesculapius::be=
fore, .fa-staff-snake::before { content: "=EE=95=B9"; }

.fa-head-side-cough-slash::before { content: "=EE=81=A2"; }

.fa-ambulance::before, .fa-truck-medical::before { content: "=EF=83=B9"; }

.fa-wheat-awn-circle-exclamation::before { content: "=EE=96=98"; }

.fa-snowman::before { content: "=EF=9F=90"; }

.fa-mortar-pestle::before { content: "=EF=96=A7"; }

.fa-road-barrier::before { content: "=EE=95=A2"; }

.fa-school::before { content: "=EF=95=89"; }

.fa-igloo::before { content: "=EF=9E=AE"; }

.fa-joint::before { content: "=EF=96=95"; }

.fa-angle-right::before { content: "=EF=84=85"; }

.fa-horse::before { content: "=EF=9B=B0"; }

.fa-q::before { content: "Q"; }

.fa-g::before { content: "G"; }

.fa-notes-medical::before { content: "=EF=92=81"; }

.fa-temperature-2::before, .fa-temperature-half::before, .fa-thermometer-2:=
:before, .fa-thermometer-half::before { content: "=EF=8B=89"; }

.fa-dong-sign::before { content: "=EE=85=A9"; }

.fa-capsules::before { content: "=EF=91=AB"; }

.fa-poo-bolt::before, .fa-poo-storm::before { content: "=EF=9D=9A"; }

.fa-face-frown-open::before, .fa-frown-open::before { content: "=EF=95=BA";=
 }

.fa-hand-point-up::before { content: "=EF=82=A6"; }

.fa-money-bill::before { content: "=EF=83=96"; }

.fa-bookmark::before { content: "=EF=80=AE"; }

.fa-align-justify::before { content: "=EF=80=B9"; }

.fa-umbrella-beach::before { content: "=EF=97=8A"; }

.fa-helmet-un::before { content: "=EE=94=83"; }

.fa-bullseye::before { content: "=EF=85=80"; }

.fa-bacon::before { content: "=EF=9F=A5"; }

.fa-hand-point-down::before { content: "=EF=82=A7"; }

.fa-arrow-up-from-bracket::before { content: "=EE=82=9A"; }

.fa-folder-blank::before, .fa-folder::before { content: "=EF=81=BB"; }

.fa-file-medical-alt::before, .fa-file-waveform::before { content: "=EF=91=
=B8"; }

.fa-radiation::before { content: "=EF=9E=B9"; }

.fa-chart-simple::before { content: "=EE=91=B3"; }

.fa-mars-stroke::before { content: "=EF=88=A9"; }

.fa-vial::before { content: "=EF=92=92"; }

.fa-dashboard::before, .fa-gauge-med::before, .fa-gauge::before, .fa-tachom=
eter-alt-average::before { content: "=EF=98=A4"; }

.fa-magic-wand-sparkles::before, .fa-wand-magic-sparkles::before { content:=
 "=EE=8B=8A"; }

.fa-e::before { content: "E"; }

.fa-pen-alt::before, .fa-pen-clip::before { content: "=EF=8C=85"; }

.fa-bridge-circle-exclamation::before { content: "=EE=93=8A"; }

.fa-user::before { content: "=EF=80=87"; }

.fa-school-circle-check::before { content: "=EE=95=AB"; }

.fa-dumpster::before { content: "=EF=9E=93"; }

.fa-shuttle-van::before, .fa-van-shuttle::before { content: "=EF=96=B6"; }

.fa-building-user::before { content: "=EE=93=9A"; }

.fa-caret-square-left::before, .fa-square-caret-left::before { content: "=
=EF=86=91"; }

.fa-highlighter::before { content: "=EF=96=91"; }

.fa-key::before { content: "=EF=82=84"; }

.fa-bullhorn::before { content: "=EF=82=A1"; }

.fa-globe::before { content: "=EF=82=AC"; }

.fa-synagogue::before { content: "=EF=9A=9B"; }

.fa-person-half-dress::before { content: "=EE=95=88"; }

.fa-road-bridge::before { content: "=EE=95=A3"; }

.fa-location-arrow::before { content: "=EF=84=A4"; }

.fa-c::before { content: "C"; }

.fa-tablet-button::before { content: "=EF=84=8A"; }

.fa-building-lock::before { content: "=EE=93=96"; }

.fa-pizza-slice::before { content: "=EF=A0=98"; }

.fa-money-bill-wave::before { content: "=EF=94=BA"; }

.fa-area-chart::before, .fa-chart-area::before { content: "=EF=87=BE"; }

.fa-house-flag::before { content: "=EE=94=8D"; }

.fa-person-circle-minus::before { content: "=EE=95=80"; }

.fa-ban::before, .fa-cancel::before { content: "=EF=81=9E"; }

.fa-camera-rotate::before { content: "=EE=83=98"; }

.fa-air-freshener::before, .fa-spray-can-sparkles::before { content: "=EF=
=97=90"; }

.fa-star::before { content: "=EF=80=85"; }

.fa-repeat::before { content: "=EF=8D=A3"; }

.fa-cross::before { content: "=EF=99=94"; }

.fa-box::before { content: "=EF=91=A6"; }

.fa-venus-mars::before { content: "=EF=88=A8"; }

.fa-arrow-pointer::before, .fa-mouse-pointer::before { content: "=EF=89=85"=
; }

.fa-expand-arrows-alt::before, .fa-maximize::before { content: "=EF=8C=9E";=
 }

.fa-charging-station::before { content: "=EF=97=A7"; }

.fa-shapes::before, .fa-triangle-circle-square::before { content: "=EF=98=
=9F"; }

.fa-random::before, .fa-shuffle::before { content: "=EF=81=B4"; }

.fa-person-running::before, .fa-running::before { content: "=EF=9C=8C"; }

.fa-mobile-retro::before { content: "=EE=94=A7"; }

.fa-grip-lines-vertical::before { content: "=EF=9E=A5"; }

.fa-spider::before { content: "=EF=9C=97"; }

.fa-hands-bound::before { content: "=EE=93=B9"; }

.fa-file-invoice-dollar::before { content: "=EF=95=B1"; }

.fa-plane-circle-exclamation::before { content: "=EE=95=96"; }

.fa-x-ray::before { content: "=EF=92=97"; }

.fa-spell-check::before { content: "=EF=A2=91"; }

.fa-slash::before { content: "=EF=9C=95"; }

.fa-computer-mouse::before, .fa-mouse::before { content: "=EF=A3=8C"; }

.fa-arrow-right-to-bracket::before, .fa-sign-in::before { content: "=EF=82=
=90"; }

.fa-shop-slash::before, .fa-store-alt-slash::before { content: "=EE=81=B0";=
 }

.fa-server::before { content: "=EF=88=B3"; }

.fa-virus-covid-slash::before { content: "=EE=92=A9"; }

.fa-shop-lock::before { content: "=EE=92=A5"; }

.fa-hourglass-1::before, .fa-hourglass-start::before { content: "=EF=89=91"=
; }

.fa-blender-phone::before { content: "=EF=9A=B6"; }

.fa-building-wheat::before { content: "=EE=93=9B"; }

.fa-person-breastfeeding::before { content: "=EE=94=BA"; }

.fa-right-to-bracket::before, .fa-sign-in-alt::before { content: "=EF=8B=B6=
"; }

.fa-venus::before { content: "=EF=88=A1"; }

.fa-passport::before { content: "=EF=96=AB"; }

.fa-heart-pulse::before, .fa-heartbeat::before { content: "=EF=88=9E"; }

.fa-people-carry-box::before, .fa-people-carry::before { content: "=EF=93=
=8E"; }

.fa-temperature-high::before { content: "=EF=9D=A9"; }

.fa-microchip::before { content: "=EF=8B=9B"; }

.fa-crown::before { content: "=EF=94=A1"; }

.fa-weight-hanging::before { content: "=EF=97=8D"; }

.fa-xmarks-lines::before { content: "=EE=96=9A"; }

.fa-file-prescription::before { content: "=EF=95=B2"; }

.fa-weight-scale::before, .fa-weight::before { content: "=EF=92=96"; }

.fa-user-friends::before, .fa-user-group::before { content: "=EF=94=80"; }

.fa-arrow-up-a-z::before, .fa-sort-alpha-up::before { content: "=EF=85=9E";=
 }

.fa-chess-knight::before { content: "=EF=91=81"; }

.fa-face-laugh-squint::before, .fa-laugh-squint::before { content: "=EF=96=
=9B"; }

.fa-wheelchair::before { content: "=EF=86=93"; }

.fa-arrow-circle-up::before, .fa-circle-arrow-up::before { content: "=EF=82=
=AA"; }

.fa-toggle-on::before { content: "=EF=88=85"; }

.fa-person-walking::before, .fa-walking::before { content: "=EF=95=94"; }

.fa-l::before { content: "L"; }

.fa-fire::before { content: "=EF=81=AD"; }

.fa-bed-pulse::before, .fa-procedures::before { content: "=EF=92=87"; }

.fa-shuttle-space::before, .fa-space-shuttle::before { content: "=EF=86=97"=
; }

.fa-face-laugh::before, .fa-laugh::before { content: "=EF=96=99"; }

.fa-folder-open::before { content: "=EF=81=BC"; }

.fa-heart-circle-plus::before { content: "=EE=94=80"; }

.fa-code-fork::before { content: "=EE=84=BB"; }

.fa-city::before { content: "=EF=99=8F"; }

.fa-microphone-alt::before, .fa-microphone-lines::before { content: "=EF=8F=
=89"; }

.fa-pepper-hot::before { content: "=EF=A0=96"; }

.fa-unlock::before { content: "=EF=82=9C"; }

.fa-colon-sign::before { content: "=EE=85=80"; }

.fa-headset::before { content: "=EF=96=90"; }

.fa-store-slash::before { content: "=EE=81=B1"; }

.fa-road-circle-xmark::before { content: "=EE=95=A6"; }

.fa-user-minus::before { content: "=EF=94=83"; }

.fa-mars-stroke-up::before, .fa-mars-stroke-v::before { content: "=EF=88=AA=
"; }

.fa-champagne-glasses::before, .fa-glass-cheers::before { content: "=EF=9E=
=9F"; }

.fa-clipboard::before { content: "=EF=8C=A8"; }

.fa-house-circle-exclamation::before { content: "=EE=94=8A"; }

.fa-file-arrow-up::before, .fa-file-upload::before { content: "=EF=95=B4"; =
}

.fa-wifi-3::before, .fa-wifi-strong::before, .fa-wifi::before { content: "=
=EF=87=AB"; }

.fa-bath::before, .fa-bathtub::before { content: "=EF=8B=8D"; }

.fa-underline::before { content: "=EF=83=8D"; }

.fa-user-edit::before, .fa-user-pen::before { content: "=EF=93=BF"; }

.fa-signature::before { content: "=EF=96=B7"; }

.fa-stroopwafel::before { content: "=EF=95=91"; }

.fa-bold::before { content: "=EF=80=B2"; }

.fa-anchor-lock::before { content: "=EE=92=AD"; }

.fa-building-ngo::before { content: "=EE=93=97"; }

.fa-manat-sign::before { content: "=EE=87=95"; }

.fa-not-equal::before { content: "=EF=94=BE"; }

.fa-border-style::before, .fa-border-top-left::before { content: "=EF=A1=93=
"; }

.fa-map-location-dot::before, .fa-map-marked-alt::before { content: "=EF=96=
=A0"; }

.fa-jedi::before { content: "=EF=99=A9"; }

.fa-poll::before, .fa-square-poll-vertical::before { content: "=EF=9A=81"; =
}

.fa-mug-hot::before { content: "=EF=9E=B6"; }

.fa-battery-car::before, .fa-car-battery::before { content: "=EF=97=9F"; }

.fa-gift::before { content: "=EF=81=AB"; }

.fa-dice-two::before { content: "=EF=94=A8"; }

.fa-chess-queen::before { content: "=EF=91=85"; }

.fa-glasses::before { content: "=EF=94=B0"; }

.fa-chess-board::before { content: "=EF=90=BC"; }

.fa-building-circle-check::before { content: "=EE=93=92"; }

.fa-person-chalkboard::before { content: "=EE=94=BD"; }

.fa-mars-stroke-h::before, .fa-mars-stroke-right::before { content: "=EF=88=
=AB"; }

.fa-hand-back-fist::before, .fa-hand-rock::before { content: "=EF=89=95"; }

.fa-caret-square-up::before, .fa-square-caret-up::before { content: "=EF=85=
=91"; }

.fa-cloud-showers-water::before { content: "=EE=93=A4"; }

.fa-bar-chart::before, .fa-chart-bar::before { content: "=EF=82=80"; }

.fa-hands-bubbles::before, .fa-hands-wash::before { content: "=EE=81=9E"; }

.fa-less-than-equal::before { content: "=EF=94=B7"; }

.fa-train::before { content: "=EF=88=B8"; }

.fa-eye-low-vision::before, .fa-low-vision::before { content: "=EF=8A=A8"; =
}

.fa-crow::before { content: "=EF=94=A0"; }

.fa-sailboat::before { content: "=EE=91=85"; }

.fa-window-restore::before { content: "=EF=8B=92"; }

.fa-plus-square::before, .fa-square-plus::before { content: "=EF=83=BE"; }

.fa-torii-gate::before { content: "=EF=9A=A1"; }

.fa-frog::before { content: "=EF=94=AE"; }

.fa-bucket::before { content: "=EE=93=8F"; }

.fa-image::before { content: "=EF=80=BE"; }

.fa-microphone::before { content: "=EF=84=B0"; }

.fa-cow::before { content: "=EF=9B=88"; }

.fa-caret-up::before { content: "=EF=83=98"; }

.fa-screwdriver::before { content: "=EF=95=8A"; }

.fa-folder-closed::before { content: "=EE=86=85"; }

.fa-house-tsunami::before { content: "=EE=94=95"; }

.fa-square-nfi::before { content: "=EE=95=B6"; }

.fa-arrow-up-from-ground-water::before { content: "=EE=92=B5"; }

.fa-glass-martini-alt::before, .fa-martini-glass::before { content: "=EF=95=
=BB"; }

.fa-rotate-back::before, .fa-rotate-backward::before, .fa-rotate-left::befo=
re, .fa-undo-alt::before { content: "=EF=8B=AA"; }

.fa-columns::before, .fa-table-columns::before { content: "=EF=83=9B"; }

.fa-lemon::before { content: "=EF=82=94"; }

.fa-head-side-mask::before { content: "=EE=81=A3"; }

.fa-handshake::before { content: "=EF=8A=B5"; }

.fa-gem::before { content: "=EF=8E=A5"; }

.fa-dolly-box::before, .fa-dolly::before { content: "=EF=91=B2"; }

.fa-smoking::before { content: "=EF=92=8D"; }

.fa-compress-arrows-alt::before, .fa-minimize::before { content: "=EF=9E=8C=
"; }

.fa-monument::before { content: "=EF=96=A6"; }

.fa-snowplow::before { content: "=EF=9F=92"; }

.fa-angle-double-right::before, .fa-angles-right::before { content: "=EF=84=
=81"; }

.fa-cannabis::before { content: "=EF=95=9F"; }

.fa-circle-play::before, .fa-play-circle::before { content: "=EF=85=84"; }

.fa-tablets::before { content: "=EF=92=90"; }

.fa-ethernet::before { content: "=EF=9E=96"; }

.fa-eur::before, .fa-euro-sign::before, .fa-euro::before { content: "=EF=85=
=93"; }

.fa-chair::before { content: "=EF=9B=80"; }

.fa-check-circle::before, .fa-circle-check::before { content: "=EF=81=98"; =
}

.fa-circle-stop::before, .fa-stop-circle::before { content: "=EF=8A=8D"; }

.fa-compass-drafting::before, .fa-drafting-compass::before { content: "=EF=
=95=A8"; }

.fa-plate-wheat::before { content: "=EE=95=9A"; }

.fa-icicles::before { content: "=EF=9E=AD"; }

.fa-person-shelter::before { content: "=EE=95=8F"; }

.fa-neuter::before { content: "=EF=88=AC"; }

.fa-id-badge::before { content: "=EF=8B=81"; }

.fa-marker::before { content: "=EF=96=A1"; }

.fa-face-laugh-beam::before, .fa-laugh-beam::before { content: "=EF=96=9A";=
 }

.fa-helicopter-symbol::before { content: "=EE=94=82"; }

.fa-universal-access::before { content: "=EF=8A=9A"; }

.fa-chevron-circle-up::before, .fa-circle-chevron-up::before { content: "=
=EF=84=B9"; }

.fa-lari-sign::before { content: "=EE=87=88"; }

.fa-volcano::before { content: "=EF=9D=B0"; }

.fa-person-walking-dashed-line-arrow-right::before { content: "=EE=95=93"; =
}

.fa-gbp::before, .fa-pound-sign::before, .fa-sterling-sign::before { conten=
t: "=EF=85=94"; }

.fa-viruses::before { content: "=EE=81=B6"; }

.fa-square-person-confined::before { content: "=EE=95=B7"; }

.fa-user-tie::before { content: "=EF=94=88"; }

.fa-arrow-down-long::before, .fa-long-arrow-down::before { content: "=EF=85=
=B5"; }

.fa-tent-arrow-down-to-line::before { content: "=EE=95=BE"; }

.fa-certificate::before { content: "=EF=82=A3"; }

.fa-mail-reply-all::before, .fa-reply-all::before { content: "=EF=84=A2"; }

.fa-suitcase::before { content: "=EF=83=B2"; }

.fa-person-skating::before, .fa-skating::before { content: "=EF=9F=85"; }

.fa-filter-circle-dollar::before, .fa-funnel-dollar::before { content: "=EF=
=99=A2"; }

.fa-camera-retro::before { content: "=EF=82=83"; }

.fa-arrow-circle-down::before, .fa-circle-arrow-down::before { content: "=
=EF=82=AB"; }

.fa-arrow-right-to-file::before, .fa-file-import::before { content: "=EF=95=
=AF"; }

.fa-external-link-square::before, .fa-square-arrow-up-right::before { conte=
nt: "=EF=85=8C"; }

.fa-box-open::before { content: "=EF=92=9E"; }

.fa-scroll::before { content: "=EF=9C=8E"; }

.fa-spa::before { content: "=EF=96=BB"; }

.fa-location-pin-lock::before { content: "=EE=94=9F"; }

.fa-pause::before { content: "=EF=81=8C"; }

.fa-hill-avalanche::before { content: "=EE=94=87"; }

.fa-temperature-0::before, .fa-temperature-empty::before, .fa-thermometer-0=
::before, .fa-thermometer-empty::before { content: "=EF=8B=8B"; }

.fa-bomb::before { content: "=EF=87=A2"; }

.fa-registered::before { content: "=EF=89=9D"; }

.fa-address-card::before, .fa-contact-card::before, .fa-vcard::before { con=
tent: "=EF=8A=BB"; }

.fa-balance-scale-right::before, .fa-scale-unbalanced-flip::before { conten=
t: "=EF=94=96"; }

.fa-subscript::before { content: "=EF=84=AC"; }

.fa-diamond-turn-right::before, .fa-directions::before { content: "=EF=97=
=AB"; }

.fa-burst::before { content: "=EE=93=9C"; }

.fa-house-laptop::before, .fa-laptop-house::before { content: "=EE=81=A6"; =
}

.fa-face-tired::before, .fa-tired::before { content: "=EF=97=88"; }

.fa-money-bills::before { content: "=EE=87=B3"; }

.fa-smog::before { content: "=EF=9D=9F"; }

.fa-crutch::before { content: "=EF=9F=B7"; }

.fa-cloud-arrow-up::before, .fa-cloud-upload-alt::before, .fa-cloud-upload:=
:before { content: "=EF=83=AE"; }

.fa-palette::before { content: "=EF=94=BF"; }

.fa-arrows-turn-right::before { content: "=EE=93=80"; }

.fa-vest::before { content: "=EE=82=85"; }

.fa-ferry::before { content: "=EE=93=AA"; }

.fa-arrows-down-to-people::before { content: "=EE=92=B9"; }

.fa-seedling::before, .fa-sprout::before { content: "=EF=93=98"; }

.fa-arrows-alt-h::before, .fa-left-right::before { content: "=EF=8C=B7"; }

.fa-boxes-packing::before { content: "=EE=93=87"; }

.fa-arrow-circle-left::before, .fa-circle-arrow-left::before { content: "=
=EF=82=A8"; }

.fa-group-arrows-rotate::before { content: "=EE=93=B6"; }

.fa-bowl-food::before { content: "=EE=93=86"; }

.fa-candy-cane::before { content: "=EF=9E=86"; }

.fa-arrow-down-wide-short::before, .fa-sort-amount-asc::before, .fa-sort-am=
ount-down::before { content: "=EF=85=A0"; }

.fa-cloud-bolt::before, .fa-thunderstorm::before { content: "=EF=9D=AC"; }

.fa-remove-format::before, .fa-text-slash::before { content: "=EF=A1=BD"; }

.fa-face-smile-wink::before, .fa-smile-wink::before { content: "=EF=93=9A";=
 }

.fa-file-word::before { content: "=EF=87=82"; }

.fa-file-powerpoint::before { content: "=EF=87=84"; }

.fa-arrows-h::before, .fa-arrows-left-right::before { content: "=EF=81=BE";=
 }

.fa-house-lock::before { content: "=EE=94=90"; }

.fa-cloud-arrow-down::before, .fa-cloud-download-alt::before, .fa-cloud-dow=
nload::before { content: "=EF=83=AD"; }

.fa-children::before { content: "=EE=93=A1"; }

.fa-blackboard::before, .fa-chalkboard::before { content: "=EF=94=9B"; }

.fa-user-alt-slash::before, .fa-user-large-slash::before { content: "=EF=93=
=BA"; }

.fa-envelope-open::before { content: "=EF=8A=B6"; }

.fa-handshake-alt-slash::before, .fa-handshake-simple-slash::before { conte=
nt: "=EE=81=9F"; }

.fa-mattress-pillow::before { content: "=EE=94=A5"; }

.fa-guarani-sign::before { content: "=EE=86=9A"; }

.fa-arrows-rotate::before, .fa-refresh::before, .fa-sync::before { content:=
 "=EF=80=A1"; }

.fa-fire-extinguisher::before { content: "=EF=84=B4"; }

.fa-cruzeiro-sign::before { content: "=EE=85=92"; }

.fa-greater-than-equal::before { content: "=EF=94=B2"; }

.fa-shield-alt::before, .fa-shield-halved::before { content: "=EF=8F=AD"; }

.fa-atlas::before, .fa-book-atlas::before { content: "=EF=95=98"; }

.fa-virus::before { content: "=EE=81=B4"; }

.fa-envelope-circle-check::before { content: "=EE=93=A8"; }

.fa-layer-group::before { content: "=EF=97=BD"; }

.fa-arrows-to-dot::before { content: "=EE=92=BE"; }

.fa-archway::before { content: "=EF=95=97"; }

.fa-heart-circle-check::before { content: "=EE=93=BD"; }

.fa-house-chimney-crack::before, .fa-house-damage::before { content: "=EF=
=9B=B1"; }

.fa-file-archive::before, .fa-file-zipper::before { content: "=EF=87=86"; }

.fa-square::before { content: "=EF=83=88"; }

.fa-glass-martini::before, .fa-martini-glass-empty::before { content: "=EF=
=80=80"; }

.fa-couch::before { content: "=EF=92=B8"; }

.fa-cedi-sign::before { content: "=EE=83=9F"; }

.fa-italic::before { content: "=EF=80=B3"; }

.fa-church::before { content: "=EF=94=9D"; }

.fa-comments-dollar::before { content: "=EF=99=93"; }

.fa-democrat::before { content: "=EF=9D=87"; }

.fa-z::before { content: "Z"; }

.fa-person-skiing::before, .fa-skiing::before { content: "=EF=9F=89"; }

.fa-road-lock::before { content: "=EE=95=A7"; }

.fa-a::before { content: "A"; }

.fa-temperature-arrow-down::before, .fa-temperature-down::before { content:=
 "=EE=80=BF"; }

.fa-feather-alt::before, .fa-feather-pointed::before { content: "=EF=95=AB"=
; }

.fa-p::before { content: "P"; }

.fa-snowflake::before { content: "=EF=8B=9C"; }

.fa-newspaper::before { content: "=EF=87=AA"; }

.fa-ad::before, .fa-rectangle-ad::before { content: "=EF=99=81"; }

.fa-arrow-circle-right::before, .fa-circle-arrow-right::before { content: "=
=EF=82=A9"; }

.fa-filter-circle-xmark::before { content: "=EE=85=BB"; }

.fa-locust::before { content: "=EE=94=A0"; }

.fa-sort::before, .fa-unsorted::before { content: "=EF=83=9C"; }

.fa-list-1-2::before, .fa-list-numeric::before, .fa-list-ol::before { conte=
nt: "=EF=83=8B"; }

.fa-person-dress-burst::before { content: "=EE=95=84"; }

.fa-money-check-alt::before, .fa-money-check-dollar::before { content: "=EF=
=94=BD"; }

.fa-vector-square::before { content: "=EF=97=8B"; }

.fa-bread-slice::before { content: "=EF=9F=AC"; }

.fa-language::before { content: "=EF=86=AB"; }

.fa-face-kiss-wink-heart::before, .fa-kiss-wink-heart::before { content: "=
=EF=96=98"; }

.fa-filter::before { content: "=EF=82=B0"; }

.fa-question::before { content: "?"; }

.fa-file-signature::before { content: "=EF=95=B3"; }

.fa-arrows-alt::before, .fa-up-down-left-right::before { content: "=EF=82=
=B2"; }

.fa-house-chimney-user::before { content: "=EE=81=A5"; }

.fa-hand-holding-heart::before { content: "=EF=92=BE"; }

.fa-puzzle-piece::before { content: "=EF=84=AE"; }

.fa-money-check::before { content: "=EF=94=BC"; }

.fa-star-half-alt::before, .fa-star-half-stroke::before { content: "=EF=97=
=80"; }

.fa-code::before { content: "=EF=84=A1"; }

.fa-glass-whiskey::before, .fa-whiskey-glass::before { content: "=EF=9E=A0"=
; }

.fa-building-circle-exclamation::before { content: "=EE=93=93"; }

.fa-magnifying-glass-chart::before { content: "=EE=94=A2"; }

.fa-arrow-up-right-from-square::before, .fa-external-link::before { content=
: "=EF=82=8E"; }

.fa-cubes-stacked::before { content: "=EE=93=A6"; }

.fa-krw::before, .fa-won-sign::before, .fa-won::before { content: "=EF=85=
=99"; }

.fa-virus-covid::before { content: "=EE=92=A8"; }

.fa-austral-sign::before { content: "=EE=82=A9"; }

.fa-f::before { content: "F"; }

.fa-leaf::before { content: "=EF=81=AC"; }

.fa-road::before { content: "=EF=80=98"; }

.fa-cab::before, .fa-taxi::before { content: "=EF=86=BA"; }

.fa-person-circle-plus::before { content: "=EE=95=81"; }

.fa-chart-pie::before, .fa-pie-chart::before { content: "=EF=88=80"; }

.fa-bolt-lightning::before { content: "=EE=82=B7"; }

.fa-sack-xmark::before { content: "=EE=95=AA"; }

.fa-file-excel::before { content: "=EF=87=83"; }

.fa-file-contract::before { content: "=EF=95=AC"; }

.fa-fish-fins::before { content: "=EE=93=B2"; }

.fa-building-flag::before { content: "=EE=93=95"; }

.fa-face-grin-beam::before, .fa-grin-beam::before { content: "=EF=96=82"; }

.fa-object-ungroup::before { content: "=EF=89=88"; }

.fa-poop::before { content: "=EF=98=99"; }

.fa-location-pin::before, .fa-map-marker::before { content: "=EF=81=81"; }

.fa-kaaba::before { content: "=EF=99=AB"; }

.fa-toilet-paper::before { content: "=EF=9C=9E"; }

.fa-hard-hat::before, .fa-hat-hard::before, .fa-helmet-safety::before { con=
tent: "=EF=A0=87"; }

.fa-eject::before { content: "=EF=81=92"; }

.fa-arrow-alt-circle-right::before, .fa-circle-right::before { content: "=
=EF=8D=9A"; }

.fa-plane-circle-check::before { content: "=EE=95=95"; }

.fa-face-rolling-eyes::before, .fa-meh-rolling-eyes::before { content: "=EF=
=96=A5"; }

.fa-object-group::before { content: "=EF=89=87"; }

.fa-chart-line::before, .fa-line-chart::before { content: "=EF=88=81"; }

.fa-mask-ventilator::before { content: "=EE=94=A4"; }

.fa-arrow-right::before { content: "=EF=81=A1"; }

.fa-map-signs::before, .fa-signs-post::before { content: "=EF=89=B7"; }

.fa-cash-register::before { content: "=EF=9E=88"; }

.fa-person-circle-question::before { content: "=EE=95=82"; }

.fa-h::before { content: "H"; }

.fa-tarp::before { content: "=EE=95=BB"; }

.fa-screwdriver-wrench::before, .fa-tools::before { content: "=EF=9F=99"; }

.fa-arrows-to-eye::before { content: "=EE=92=BF"; }

.fa-plug-circle-bolt::before { content: "=EE=95=9B"; }

.fa-heart::before { content: "=EF=80=84"; }

.fa-mars-and-venus::before { content: "=EF=88=A4"; }

.fa-home-user::before, .fa-house-user::before { content: "=EE=86=B0"; }

.fa-dumpster-fire::before { content: "=EF=9E=94"; }

.fa-house-crack::before { content: "=EE=8E=B1"; }

.fa-cocktail::before, .fa-martini-glass-citrus::before { content: "=EF=95=
=A1"; }

.fa-face-surprise::before, .fa-surprise::before { content: "=EF=97=82"; }

.fa-bottle-water::before { content: "=EE=93=85"; }

.fa-circle-pause::before, .fa-pause-circle::before { content: "=EF=8A=8B"; =
}

.fa-toilet-paper-slash::before { content: "=EE=81=B2"; }

.fa-apple-alt::before, .fa-apple-whole::before { content: "=EF=97=91"; }

.fa-kitchen-set::before { content: "=EE=94=9A"; }

.fa-r::before { content: "R"; }

.fa-temperature-1::before, .fa-temperature-quarter::before, .fa-thermometer=
-1::before, .fa-thermometer-quarter::before { content: "=EF=8B=8A"; }

.fa-cube::before { content: "=EF=86=B2"; }

.fa-bitcoin-sign::before { content: "=EE=82=B4"; }

.fa-shield-dog::before { content: "=EE=95=B3"; }

.fa-solar-panel::before { content: "=EF=96=BA"; }

.fa-lock-open::before { content: "=EF=8F=81"; }

.fa-elevator::before { content: "=EE=85=AD"; }

.fa-money-bill-transfer::before { content: "=EE=94=A8"; }

.fa-money-bill-trend-up::before { content: "=EE=94=A9"; }

.fa-house-flood-water-circle-arrow-right::before { content: "=EE=94=8F"; }

.fa-poll-h::before, .fa-square-poll-horizontal::before { content: "=EF=9A=
=82"; }

.fa-circle::before { content: "=EF=84=91"; }

.fa-backward-fast::before, .fa-fast-backward::before { content: "=EF=81=89"=
; }

.fa-recycle::before { content: "=EF=86=B8"; }

.fa-user-astronaut::before { content: "=EF=93=BB"; }

.fa-plane-slash::before { content: "=EE=81=A9"; }

.fa-trademark::before { content: "=EF=89=9C"; }

.fa-basketball-ball::before, .fa-basketball::before { content: "=EF=90=B4";=
 }

.fa-satellite-dish::before { content: "=EF=9F=80"; }

.fa-arrow-alt-circle-up::before, .fa-circle-up::before { content: "=EF=8D=
=9B"; }

.fa-mobile-alt::before, .fa-mobile-screen-button::before { content: "=EF=8F=
=8D"; }

.fa-volume-high::before, .fa-volume-up::before { content: "=EF=80=A8"; }

.fa-users-rays::before { content: "=EE=96=93"; }

.fa-wallet::before { content: "=EF=95=95"; }

.fa-clipboard-check::before { content: "=EF=91=AC"; }

.fa-file-audio::before { content: "=EF=87=87"; }

.fa-burger::before, .fa-hamburger::before { content: "=EF=A0=85"; }

.fa-wrench::before { content: "=EF=82=AD"; }

.fa-bugs::before { content: "=EE=93=90"; }

.fa-rupee-sign::before, .fa-rupee::before { content: "=EF=85=96"; }

.fa-file-image::before { content: "=EF=87=85"; }

.fa-circle-question::before, .fa-question-circle::before { content: "=EF=81=
=99"; }

.fa-plane-departure::before { content: "=EF=96=B0"; }

.fa-handshake-slash::before { content: "=EE=81=A0"; }

.fa-book-bookmark::before { content: "=EE=82=BB"; }

.fa-code-branch::before { content: "=EF=84=A6"; }

.fa-hat-cowboy::before { content: "=EF=A3=80"; }

.fa-bridge::before { content: "=EE=93=88"; }

.fa-phone-alt::before, .fa-phone-flip::before { content: "=EF=A1=B9"; }

.fa-truck-front::before { content: "=EE=8A=B7"; }

.fa-cat::before { content: "=EF=9A=BE"; }

.fa-anchor-circle-exclamation::before { content: "=EE=92=AB"; }

.fa-truck-field::before { content: "=EE=96=8D"; }

.fa-route::before { content: "=EF=93=97"; }

.fa-clipboard-question::before { content: "=EE=93=A3"; }

.fa-panorama::before { content: "=EE=88=89"; }

.fa-comment-medical::before { content: "=EF=9F=B5"; }

.fa-teeth-open::before { content: "=EF=98=AF"; }

.fa-file-circle-minus::before { content: "=EE=93=AD"; }

.fa-tags::before { content: "=EF=80=AC"; }

.fa-wine-glass::before { content: "=EF=93=A3"; }

.fa-fast-forward::before, .fa-forward-fast::before { content: "=EF=81=90"; =
}

.fa-face-meh-blank::before, .fa-meh-blank::before { content: "=EF=96=A4"; }

.fa-parking::before, .fa-square-parking::before { content: "=EF=95=80"; }

.fa-house-signal::before { content: "=EE=80=92"; }

.fa-bars-progress::before, .fa-tasks-alt::before { content: "=EF=A0=A8"; }

.fa-faucet-drip::before { content: "=EE=80=86"; }

.fa-cart-flatbed::before, .fa-dolly-flatbed::before { content: "=EF=91=B4";=
 }

.fa-ban-smoking::before, .fa-smoking-ban::before { content: "=EF=95=8D"; }

.fa-terminal::before { content: "=EF=84=A0"; }

.fa-mobile-button::before { content: "=EF=84=8B"; }

.fa-house-medical-flag::before { content: "=EE=94=94"; }

.fa-basket-shopping::before, .fa-shopping-basket::before { content: "=EF=8A=
=91"; }

.fa-tape::before { content: "=EF=93=9B"; }

.fa-bus-alt::before, .fa-bus-simple::before { content: "=EF=95=9E"; }

.fa-eye::before { content: "=EF=81=AE"; }

.fa-face-sad-cry::before, .fa-sad-cry::before { content: "=EF=96=B3"; }

.fa-audio-description::before { content: "=EF=8A=9E"; }

.fa-person-military-to-person::before { content: "=EE=95=8C"; }

.fa-file-shield::before { content: "=EE=93=B0"; }

.fa-user-slash::before { content: "=EF=94=86"; }

.fa-pen::before { content: "=EF=8C=84"; }

.fa-tower-observation::before { content: "=EE=96=86"; }

.fa-file-code::before { content: "=EF=87=89"; }

.fa-signal-5::before, .fa-signal-perfect::before, .fa-signal::before { cont=
ent: "=EF=80=92"; }

.fa-bus::before { content: "=EF=88=87"; }

.fa-heart-circle-xmark::before { content: "=EE=94=81"; }

.fa-home-lg::before, .fa-house-chimney::before { content: "=EE=8E=AF"; }

.fa-window-maximize::before { content: "=EF=8B=90"; }

.fa-face-frown::before, .fa-frown::before { content: "=EF=84=99"; }

.fa-prescription::before { content: "=EF=96=B1"; }

.fa-shop::before, .fa-store-alt::before { content: "=EF=95=8F"; }

.fa-floppy-disk::before, .fa-save::before { content: "=EF=83=87"; }

.fa-vihara::before { content: "=EF=9A=A7"; }

.fa-balance-scale-left::before, .fa-scale-unbalanced::before { content: "=
=EF=94=95"; }

.fa-sort-asc::before, .fa-sort-up::before { content: "=EF=83=9E"; }

.fa-comment-dots::before, .fa-commenting::before { content: "=EF=92=AD"; }

.fa-plant-wilt::before { content: "=EE=96=AA"; }

.fa-diamond::before { content: "=EF=88=99"; }

.fa-face-grin-squint::before, .fa-grin-squint::before { content: "=EF=96=85=
"; }

.fa-hand-holding-dollar::before, .fa-hand-holding-usd::before { content: "=
=EF=93=80"; }

.fa-bacterium::before { content: "=EE=81=9A"; }

.fa-hand-pointer::before { content: "=EF=89=9A"; }

.fa-drum-steelpan::before { content: "=EF=95=AA"; }

.fa-hand-scissors::before { content: "=EF=89=97"; }

.fa-hands-praying::before, .fa-praying-hands::before { content: "=EF=9A=84"=
; }

.fa-arrow-right-rotate::before, .fa-arrow-rotate-forward::before, .fa-arrow=
-rotate-right::before, .fa-redo::before { content: "=EF=80=9E"; }

.fa-biohazard::before { content: "=EF=9E=80"; }

.fa-location-crosshairs::before, .fa-location::before { content: "=EF=98=81=
"; }

.fa-mars-double::before { content: "=EF=88=A7"; }

.fa-child-dress::before { content: "=EE=96=9C"; }

.fa-users-between-lines::before { content: "=EE=96=91"; }

.fa-lungs-virus::before { content: "=EE=81=A7"; }

.fa-face-grin-tears::before, .fa-grin-tears::before { content: "=EF=96=88";=
 }

.fa-phone::before { content: "=EF=82=95"; }

.fa-calendar-times::before, .fa-calendar-xmark::before { content: "=EF=89=
=B3"; }

.fa-child-reaching::before { content: "=EE=96=9D"; }

.fa-head-side-virus::before { content: "=EE=81=A4"; }

.fa-user-cog::before, .fa-user-gear::before { content: "=EF=93=BE"; }

.fa-arrow-up-1-9::before, .fa-sort-numeric-up::before { content: "=EF=85=A3=
"; }

.fa-door-closed::before { content: "=EF=94=AA"; }

.fa-shield-virus::before { content: "=EE=81=AC"; }

.fa-dice-six::before { content: "=EF=94=A6"; }

.fa-mosquito-net::before { content: "=EE=94=AC"; }

.fa-bridge-water::before { content: "=EE=93=8E"; }

.fa-person-booth::before { content: "=EF=9D=96"; }

.fa-text-width::before { content: "=EF=80=B5"; }

.fa-hat-wizard::before { content: "=EF=9B=A8"; }

.fa-pen-fancy::before { content: "=EF=96=AC"; }

.fa-digging::before, .fa-person-digging::before { content: "=EF=A1=9E"; }

.fa-trash::before { content: "=EF=87=B8"; }

.fa-gauge-simple-med::before, .fa-gauge-simple::before, .fa-tachometer-aver=
age::before { content: "=EF=98=A9"; }

.fa-book-medical::before { content: "=EF=9F=A6"; }

.fa-poo::before { content: "=EF=8B=BE"; }

.fa-quote-right-alt::before, .fa-quote-right::before { content: "=EF=84=8E"=
; }

.fa-shirt::before, .fa-t-shirt::before, .fa-tshirt::before { content: "=EF=
=95=93"; }

.fa-cubes::before { content: "=EF=86=B3"; }

.fa-divide::before { content: "=EF=94=A9"; }

.fa-tenge-sign::before, .fa-tenge::before { content: "=EF=9F=97"; }

.fa-headphones::before { content: "=EF=80=A5"; }

.fa-hands-holding::before { content: "=EF=93=82"; }

.fa-hands-clapping::before { content: "=EE=86=A8"; }

.fa-republican::before { content: "=EF=9D=9E"; }

.fa-arrow-left::before { content: "=EF=81=A0"; }

.fa-person-circle-xmark::before { content: "=EE=95=83"; }

.fa-ruler::before { content: "=EF=95=85"; }

.fa-align-left::before { content: "=EF=80=B6"; }

.fa-dice-d6::before { content: "=EF=9B=91"; }

.fa-restroom::before { content: "=EF=9E=BD"; }

.fa-j::before { content: "J"; }

.fa-users-viewfinder::before { content: "=EE=96=95"; }

.fa-file-video::before { content: "=EF=87=88"; }

.fa-external-link-alt::before, .fa-up-right-from-square::before { content: =
"=EF=8D=9D"; }

.fa-table-cells::before, .fa-th::before { content: "=EF=80=8A"; }

.fa-file-pdf::before { content: "=EF=87=81"; }

.fa-bible::before, .fa-book-bible::before { content: "=EF=99=87"; }

.fa-o::before { content: "O"; }

.fa-medkit::before, .fa-suitcase-medical::before { content: "=EF=83=BA"; }

.fa-user-secret::before { content: "=EF=88=9B"; }

.fa-otter::before { content: "=EF=9C=80"; }

.fa-female::before, .fa-person-dress::before { content: "=EF=86=82"; }

.fa-comment-dollar::before { content: "=EF=99=91"; }

.fa-briefcase-clock::before, .fa-business-time::before { content: "=EF=99=
=8A"; }

.fa-table-cells-large::before, .fa-th-large::before { content: "=EF=80=89";=
 }

.fa-book-tanakh::before, .fa-tanakh::before { content: "=EF=A0=A7"; }

.fa-phone-volume::before, .fa-volume-control-phone::before { content: "=EF=
=8A=A0"; }

.fa-hat-cowboy-side::before { content: "=EF=A3=81"; }

.fa-clipboard-user::before { content: "=EF=9F=B3"; }

.fa-child::before { content: "=EF=86=AE"; }

.fa-lira-sign::before { content: "=EF=86=95"; }

.fa-satellite::before { content: "=EF=9E=BF"; }

.fa-plane-lock::before { content: "=EE=95=98"; }

.fa-tag::before { content: "=EF=80=AB"; }

.fa-comment::before { content: "=EF=81=B5"; }

.fa-birthday-cake::before, .fa-cake-candles::before, .fa-cake::before { con=
tent: "=EF=87=BD"; }

.fa-envelope::before { content: "=EF=83=A0"; }

.fa-angle-double-up::before, .fa-angles-up::before { content: "=EF=84=82"; =
}

.fa-paperclip::before { content: "=EF=83=86"; }

.fa-arrow-right-to-city::before { content: "=EE=92=B3"; }

.fa-ribbon::before { content: "=EF=93=96"; }

.fa-lungs::before { content: "=EF=98=84"; }

.fa-arrow-up-9-1::before, .fa-sort-numeric-up-alt::before { content: "=EF=
=A2=87"; }

.fa-litecoin-sign::before { content: "=EE=87=93"; }

.fa-border-none::before { content: "=EF=A1=90"; }

.fa-circle-nodes::before { content: "=EE=93=A2"; }

.fa-parachute-box::before { content: "=EF=93=8D"; }

.fa-indent::before { content: "=EF=80=BC"; }

.fa-truck-field-un::before { content: "=EE=96=8E"; }

.fa-hourglass-empty::before, .fa-hourglass::before { content: "=EF=89=94"; =
}

.fa-mountain::before { content: "=EF=9B=BC"; }

.fa-user-doctor::before, .fa-user-md::before { content: "=EF=83=B0"; }

.fa-circle-info::before, .fa-info-circle::before { content: "=EF=81=9A"; }

.fa-cloud-meatball::before { content: "=EF=9C=BB"; }

.fa-camera-alt::before, .fa-camera::before { content: "=EF=80=B0"; }

.fa-square-virus::before { content: "=EE=95=B8"; }

.fa-meteor::before { content: "=EF=9D=93"; }

.fa-car-on::before { content: "=EE=93=9D"; }

.fa-sleigh::before { content: "=EF=9F=8C"; }

.fa-arrow-down-1-9::before, .fa-sort-numeric-asc::before, .fa-sort-numeric-=
down::before { content: "=EF=85=A2"; }

.fa-hand-holding-droplet::before, .fa-hand-holding-water::before { content:=
 "=EF=93=81"; }

.fa-water::before { content: "=EF=9D=B3"; }

.fa-calendar-check::before { content: "=EF=89=B4"; }

.fa-braille::before { content: "=EF=8A=A1"; }

.fa-prescription-bottle-alt::before, .fa-prescription-bottle-medical::befor=
e { content: "=EF=92=86"; }

.fa-landmark::before { content: "=EF=99=AF"; }

.fa-truck::before { content: "=EF=83=91"; }

.fa-crosshairs::before { content: "=EF=81=9B"; }

.fa-person-cane::before { content: "=EE=94=BC"; }

.fa-tent::before { content: "=EE=95=BD"; }

.fa-vest-patches::before { content: "=EE=82=86"; }

.fa-check-double::before { content: "=EF=95=A0"; }

.fa-arrow-down-a-z::before, .fa-sort-alpha-asc::before, .fa-sort-alpha-down=
::before { content: "=EF=85=9D"; }

.fa-money-bill-wheat::before { content: "=EE=94=AA"; }

.fa-cookie::before { content: "=EF=95=A3"; }

.fa-arrow-left-rotate::before, .fa-arrow-rotate-back::before, .fa-arrow-rot=
ate-backward::before, .fa-arrow-rotate-left::before, .fa-undo::before { con=
tent: "=EF=83=A2"; }

.fa-hard-drive::before, .fa-hdd::before { content: "=EF=82=A0"; }

.fa-face-grin-squint-tears::before, .fa-grin-squint-tears::before { content=
: "=EF=96=86"; }

.fa-dumbbell::before { content: "=EF=91=8B"; }

.fa-list-alt::before, .fa-rectangle-list::before { content: "=EF=80=A2"; }

.fa-tarp-droplet::before { content: "=EE=95=BC"; }

.fa-house-medical-circle-check::before { content: "=EE=94=91"; }

.fa-person-skiing-nordic::before, .fa-skiing-nordic::before { content: "=EF=
=9F=8A"; }

.fa-calendar-plus::before { content: "=EF=89=B1"; }

.fa-plane-arrival::before { content: "=EF=96=AF"; }

.fa-arrow-alt-circle-left::before, .fa-circle-left::before { content: "=EF=
=8D=99"; }

.fa-subway::before, .fa-train-subway::before { content: "=EF=88=B9"; }

.fa-chart-gantt::before { content: "=EE=83=A4"; }

.fa-indian-rupee-sign::before, .fa-indian-rupee::before, .fa-inr::before { =
content: "=EE=86=BC"; }

.fa-crop-alt::before, .fa-crop-simple::before { content: "=EF=95=A5"; }

.fa-money-bill-1::before, .fa-money-bill-alt::before { content: "=EF=8F=91"=
; }

.fa-left-long::before, .fa-long-arrow-alt-left::before { content: "=EF=8C=
=8A"; }

.fa-dna::before { content: "=EF=91=B1"; }

.fa-virus-slash::before { content: "=EE=81=B5"; }

.fa-minus::before, .fa-subtract::before { content: "=EF=81=A8"; }

.fa-chess::before { content: "=EF=90=B9"; }

.fa-arrow-left-long::before, .fa-long-arrow-left::before { content: "=EF=85=
=B7"; }

.fa-plug-circle-check::before { content: "=EE=95=9C"; }

.fa-street-view::before { content: "=EF=88=9D"; }

.fa-franc-sign::before { content: "=EE=86=8F"; }

.fa-volume-off::before { content: "=EF=80=A6"; }

.fa-american-sign-language-interpreting::before, .fa-asl-interpreting::befo=
re, .fa-hands-american-sign-language-interpreting::before, .fa-hands-asl-in=
terpreting::before { content: "=EF=8A=A3"; }

.fa-cog::before, .fa-gear::before { content: "=EF=80=93"; }

.fa-droplet-slash::before, .fa-tint-slash::before { content: "=EF=97=87"; }

.fa-mosque::before { content: "=EF=99=B8"; }

.fa-mosquito::before { content: "=EE=94=AB"; }

.fa-star-of-david::before { content: "=EF=9A=9A"; }

.fa-person-military-rifle::before { content: "=EE=95=8B"; }

.fa-cart-shopping::before, .fa-shopping-cart::before { content: "=EF=81=BA"=
; }

.fa-vials::before { content: "=EF=92=93"; }

.fa-plug-circle-plus::before { content: "=EE=95=9F"; }

.fa-place-of-worship::before { content: "=EF=99=BF"; }

.fa-grip-vertical::before { content: "=EF=96=8E"; }

.fa-arrow-turn-up::before, .fa-level-up::before { content: "=EF=85=88"; }

.fa-u::before { content: "U"; }

.fa-square-root-alt::before, .fa-square-root-variable::before { content: "=
=EF=9A=98"; }

.fa-clock-four::before, .fa-clock::before { content: "=EF=80=97"; }

.fa-backward-step::before, .fa-step-backward::before { content: "=EF=81=88"=
; }

.fa-pallet::before { content: "=EF=92=82"; }

.fa-faucet::before { content: "=EE=80=85"; }

.fa-baseball-bat-ball::before { content: "=EF=90=B2"; }

.fa-s::before { content: "S"; }

.fa-timeline::before { content: "=EE=8A=9C"; }

.fa-keyboard::before { content: "=EF=84=9C"; }

.fa-caret-down::before { content: "=EF=83=97"; }

.fa-clinic-medical::before, .fa-house-chimney-medical::before { content: "=
=EF=9F=B2"; }

.fa-temperature-3::before, .fa-temperature-three-quarters::before, .fa-ther=
mometer-3::before, .fa-thermometer-three-quarters::before { content: "=EF=
=8B=88"; }

.fa-mobile-android-alt::before, .fa-mobile-screen::before { content: "=EF=
=8F=8F"; }

.fa-plane-up::before { content: "=EE=88=AD"; }

.fa-piggy-bank::before { content: "=EF=93=93"; }

.fa-battery-3::before, .fa-battery-half::before { content: "=EF=89=82"; }

.fa-mountain-city::before { content: "=EE=94=AE"; }

.fa-coins::before { content: "=EF=94=9E"; }

.fa-khanda::before { content: "=EF=99=AD"; }

.fa-sliders-h::before, .fa-sliders::before { content: "=EF=87=9E"; }

.fa-folder-tree::before { content: "=EF=A0=82"; }

.fa-network-wired::before { content: "=EF=9B=BF"; }

.fa-map-pin::before { content: "=EF=89=B6"; }

.fa-hamsa::before { content: "=EF=99=A5"; }

.fa-cent-sign::before { content: "=EE=8F=B5"; }

.fa-flask::before { content: "=EF=83=83"; }

.fa-person-pregnant::before { content: "=EE=8C=9E"; }

.fa-wand-sparkles::before { content: "=EF=9C=AB"; }

.fa-ellipsis-v::before, .fa-ellipsis-vertical::before { content: "=EF=85=82=
"; }

.fa-ticket::before { content: "=EF=85=85"; }

.fa-power-off::before { content: "=EF=80=91"; }

.fa-long-arrow-alt-right::before, .fa-right-long::before { content: "=EF=8C=
=8B"; }

.fa-flag-usa::before { content: "=EF=9D=8D"; }

.fa-laptop-file::before { content: "=EE=94=9D"; }

.fa-teletype::before, .fa-tty::before { content: "=EF=87=A4"; }

.fa-diagram-next::before { content: "=EE=91=B6"; }

.fa-person-rifle::before { content: "=EE=95=8E"; }

.fa-house-medical-circle-exclamation::before { content: "=EE=94=92"; }

.fa-closed-captioning::before { content: "=EF=88=8A"; }

.fa-hiking::before, .fa-person-hiking::before { content: "=EF=9B=AC"; }

.fa-venus-double::before { content: "=EF=88=A6"; }

.fa-images::before { content: "=EF=8C=82"; }

.fa-calculator::before { content: "=EF=87=AC"; }

.fa-people-pulling::before { content: "=EE=94=B5"; }

.fa-n::before { content: "N"; }

.fa-cable-car::before, .fa-tram::before { content: "=EF=9F=9A"; }

.fa-cloud-rain::before { content: "=EF=9C=BD"; }

.fa-building-circle-xmark::before { content: "=EE=93=94"; }

.fa-ship::before { content: "=EF=88=9A"; }

.fa-arrows-down-to-line::before { content: "=EE=92=B8"; }

.fa-download::before { content: "=EF=80=99"; }

.fa-face-grin::before, .fa-grin::before { content: "=EF=96=80"; }

.fa-backspace::before, .fa-delete-left::before { content: "=EF=95=9A"; }

.fa-eye-dropper-empty::before, .fa-eye-dropper::before, .fa-eyedropper::bef=
ore { content: "=EF=87=BB"; }

.fa-file-circle-check::before { content: "=EE=96=A0"; }

.fa-forward::before { content: "=EF=81=8E"; }

.fa-mobile-android::before, .fa-mobile-phone::before, .fa-mobile::before { =
content: "=EF=8F=8E"; }

.fa-face-meh::before, .fa-meh::before { content: "=EF=84=9A"; }

.fa-align-center::before { content: "=EF=80=B7"; }

.fa-book-dead::before, .fa-book-skull::before { content: "=EF=9A=B7"; }

.fa-drivers-license::before, .fa-id-card::before { content: "=EF=8B=82"; }

.fa-dedent::before, .fa-outdent::before { content: "=EF=80=BB"; }

.fa-heart-circle-exclamation::before { content: "=EE=93=BE"; }

.fa-home-alt::before, .fa-home-lg-alt::before, .fa-home::before, .fa-house:=
:before { content: "=EF=80=95"; }

.fa-calendar-week::before { content: "=EF=9E=84"; }

.fa-laptop-medical::before { content: "=EF=A0=92"; }

.fa-b::before { content: "B"; }

.fa-file-medical::before { content: "=EF=91=B7"; }

.fa-dice-one::before { content: "=EF=94=A5"; }

.fa-kiwi-bird::before { content: "=EF=94=B5"; }

.fa-arrow-right-arrow-left::before, .fa-exchange::before { content: "=EF=83=
=AC"; }

.fa-redo-alt::before, .fa-rotate-forward::before, .fa-rotate-right::before =
{ content: "=EF=8B=B9"; }

.fa-cutlery::before, .fa-utensils::before { content: "=EF=8B=A7"; }

.fa-arrow-up-wide-short::before, .fa-sort-amount-up::before { content: "=EF=
=85=A1"; }

.fa-mill-sign::before { content: "=EE=87=AD"; }

.fa-bowl-rice::before { content: "=EE=8B=AB"; }

.fa-skull::before { content: "=EF=95=8C"; }

.fa-broadcast-tower::before, .fa-tower-broadcast::before { content: "=EF=94=
=99"; }

.fa-truck-pickup::before { content: "=EF=98=BC"; }

.fa-long-arrow-alt-up::before, .fa-up-long::before { content: "=EF=8C=8C"; =
}

.fa-stop::before { content: "=EF=81=8D"; }

.fa-code-merge::before { content: "=EF=8E=87"; }

.fa-upload::before { content: "=EF=82=93"; }

.fa-hurricane::before { content: "=EF=9D=91"; }

.fa-mound::before { content: "=EE=94=AD"; }

.fa-toilet-portable::before { content: "=EE=96=83"; }

.fa-compact-disc::before { content: "=EF=94=9F"; }

.fa-file-arrow-down::before, .fa-file-download::before { content: "=EF=95=
=AD"; }

.fa-caravan::before { content: "=EF=A3=BF"; }

.fa-shield-cat::before { content: "=EE=95=B2"; }

.fa-bolt::before, .fa-zap::before { content: "=EF=83=A7"; }

.fa-glass-water::before { content: "=EE=93=B4"; }

.fa-oil-well::before { content: "=EE=94=B2"; }

.fa-vault::before { content: "=EE=8B=85"; }

.fa-mars::before { content: "=EF=88=A2"; }

.fa-toilet::before { content: "=EF=9F=98"; }

.fa-plane-circle-xmark::before { content: "=EE=95=97"; }

.fa-cny::before, .fa-jpy::before, .fa-rmb::before, .fa-yen-sign::before, .f=
a-yen::before { content: "=EF=85=97"; }

.fa-rouble::before, .fa-rub::before, .fa-ruble-sign::before, .fa-ruble::bef=
ore { content: "=EF=85=98"; }

.fa-sun::before { content: "=EF=86=85"; }

.fa-guitar::before { content: "=EF=9E=A6"; }

.fa-face-laugh-wink::before, .fa-laugh-wink::before { content: "=EF=96=9C";=
 }

.fa-horse-head::before { content: "=EF=9E=AB"; }

.fa-bore-hole::before { content: "=EE=93=83"; }

.fa-industry::before { content: "=EF=89=B5"; }

.fa-arrow-alt-circle-down::before, .fa-circle-down::before { content: "=EF=
=8D=98"; }

.fa-arrows-turn-to-dots::before { content: "=EE=93=81"; }

.fa-florin-sign::before { content: "=EE=86=84"; }

.fa-arrow-down-short-wide::before, .fa-sort-amount-desc::before, .fa-sort-a=
mount-down-alt::before { content: "=EF=A2=84"; }

.fa-less-than::before { content: "<"; }

.fa-angle-down::before { content: "=EF=84=87"; }

.fa-car-tunnel::before { content: "=EE=93=9E"; }

.fa-head-side-cough::before { content: "=EE=81=A1"; }

.fa-grip-lines::before { content: "=EF=9E=A4"; }

.fa-thumbs-down::before { content: "=EF=85=A5"; }

.fa-user-lock::before { content: "=EF=94=82"; }

.fa-arrow-right-long::before, .fa-long-arrow-right::before { content: "=EF=
=85=B8"; }

.fa-anchor-circle-xmark::before { content: "=EE=92=AC"; }

.fa-ellipsis-h::before, .fa-ellipsis::before { content: "=EF=85=81"; }

.fa-chess-pawn::before { content: "=EF=91=83"; }

.fa-first-aid::before, .fa-kit-medical::before { content: "=EF=91=B9"; }

.fa-person-through-window::before { content: "=EE=96=A9"; }

.fa-toolbox::before { content: "=EF=95=92"; }

.fa-hands-holding-circle::before { content: "=EE=93=BB"; }

.fa-bug::before { content: "=EF=86=88"; }

.fa-credit-card-alt::before, .fa-credit-card::before { content: "=EF=82=9D"=
; }

.fa-automobile::before, .fa-car::before { content: "=EF=86=B9"; }

.fa-hand-holding-hand::before { content: "=EE=93=B7"; }

.fa-book-open-reader::before, .fa-book-reader::before { content: "=EF=97=9A=
"; }

.fa-mountain-sun::before { content: "=EE=94=AF"; }

.fa-arrows-left-right-to-line::before { content: "=EE=92=BA"; }

.fa-dice-d20::before { content: "=EF=9B=8F"; }

.fa-truck-droplet::before { content: "=EE=96=8C"; }

.fa-file-circle-xmark::before { content: "=EE=96=A1"; }

.fa-temperature-arrow-up::before, .fa-temperature-up::before { content: "=
=EE=81=80"; }

.fa-medal::before { content: "=EF=96=A2"; }

.fa-bed::before { content: "=EF=88=B6"; }

.fa-h-square::before, .fa-square-h::before { content: "=EF=83=BD"; }

.fa-podcast::before { content: "=EF=8B=8E"; }

.fa-temperature-4::before, .fa-temperature-full::before, .fa-thermometer-4:=
:before, .fa-thermometer-full::before { content: "=EF=8B=87"; }

.fa-bell::before { content: "=EF=83=B3"; }

.fa-superscript::before { content: "=EF=84=AB"; }

.fa-plug-circle-xmark::before { content: "=EE=95=A0"; }

.fa-star-of-life::before { content: "=EF=98=A1"; }

.fa-phone-slash::before { content: "=EF=8F=9D"; }

.fa-paint-roller::before { content: "=EF=96=AA"; }

.fa-hands-helping::before, .fa-handshake-angle::before { content: "=EF=93=
=84"; }

.fa-location-dot::before, .fa-map-marker-alt::before { content: "=EF=8F=85"=
; }

.fa-file::before { content: "=EF=85=9B"; }

.fa-greater-than::before { content: ">"; }

.fa-person-swimming::before, .fa-swimmer::before { content: "=EF=97=84"; }

.fa-arrow-down::before { content: "=EF=81=A3"; }

.fa-droplet::before, .fa-tint::before { content: "=EF=81=83"; }

.fa-eraser::before { content: "=EF=84=AD"; }

.fa-earth-america::before, .fa-earth-americas::before, .fa-earth::before, .=
fa-globe-americas::before { content: "=EF=95=BD"; }

.fa-person-burst::before { content: "=EE=94=BB"; }

.fa-dove::before { content: "=EF=92=BA"; }

.fa-battery-0::before, .fa-battery-empty::before { content: "=EF=89=84"; }

.fa-socks::before { content: "=EF=9A=96"; }

.fa-inbox::before { content: "=EF=80=9C"; }

.fa-section::before { content: "=EE=91=87"; }

.fa-gauge-high::before, .fa-tachometer-alt-fast::before, .fa-tachometer-alt=
::before { content: "=EF=98=A5"; }

.fa-envelope-open-text::before { content: "=EF=99=98"; }

.fa-hospital-alt::before, .fa-hospital-wide::before, .fa-hospital::before {=
 content: "=EF=83=B8"; }

.fa-wine-bottle::before { content: "=EF=9C=AF"; }

.fa-chess-rook::before { content: "=EF=91=87"; }

.fa-bars-staggered::before, .fa-reorder::before, .fa-stream::before { conte=
nt: "=EF=95=90"; }

.fa-dharmachakra::before { content: "=EF=99=95"; }

.fa-hotdog::before { content: "=EF=A0=8F"; }

.fa-blind::before, .fa-person-walking-with-cane::before { content: "=EF=8A=
=9D"; }

.fa-drum::before { content: "=EF=95=A9"; }

.fa-ice-cream::before { content: "=EF=A0=90"; }

.fa-heart-circle-bolt::before { content: "=EE=93=BC"; }

.fa-fax::before { content: "=EF=86=AC"; }

.fa-paragraph::before { content: "=EF=87=9D"; }

.fa-check-to-slot::before, .fa-vote-yea::before { content: "=EF=9D=B2"; }

.fa-star-half::before { content: "=EF=82=89"; }

.fa-boxes-alt::before, .fa-boxes-stacked::before, .fa-boxes::before { conte=
nt: "=EF=91=A8"; }

.fa-chain::before, .fa-link::before { content: "=EF=83=81"; }

.fa-assistive-listening-systems::before, .fa-ear-listen::before { content: =
"=EF=8A=A2"; }

.fa-tree-city::before { content: "=EE=96=87"; }

.fa-play::before { content: "=EF=81=8B"; }

.fa-font::before { content: "=EF=80=B1"; }

.fa-rupiah-sign::before { content: "=EE=88=BD"; }

.fa-magnifying-glass::before, .fa-search::before { content: "=EF=80=82"; }

.fa-ping-pong-paddle-ball::before, .fa-table-tennis-paddle-ball::before, .f=
a-table-tennis::before { content: "=EF=91=9D"; }

.fa-diagnoses::before, .fa-person-dots-from-line::before { content: "=EF=91=
=B0"; }

.fa-trash-can-arrow-up::before, .fa-trash-restore-alt::before { content: "=
=EF=A0=AA"; }

.fa-naira-sign::before { content: "=EE=87=B6"; }

.fa-cart-arrow-down::before { content: "=EF=88=98"; }

.fa-walkie-talkie::before { content: "=EF=A3=AF"; }

.fa-file-edit::before, .fa-file-pen::before { content: "=EF=8C=9C"; }

.fa-receipt::before { content: "=EF=95=83"; }

.fa-pen-square::before, .fa-pencil-square::before, .fa-square-pen::before {=
 content: "=EF=85=8B"; }

.fa-suitcase-rolling::before { content: "=EF=97=81"; }

.fa-person-circle-exclamation::before { content: "=EE=94=BF"; }

.fa-chevron-down::before { content: "=EF=81=B8"; }

.fa-battery-5::before, .fa-battery-full::before, .fa-battery::before { cont=
ent: "=EF=89=80"; }

.fa-skull-crossbones::before { content: "=EF=9C=94"; }

.fa-code-compare::before { content: "=EE=84=BA"; }

.fa-list-dots::before, .fa-list-ul::before { content: "=EF=83=8A"; }

.fa-school-lock::before { content: "=EE=95=AF"; }

.fa-tower-cell::before { content: "=EE=96=85"; }

.fa-down-long::before, .fa-long-arrow-alt-down::before { content: "=EF=8C=
=89"; }

.fa-ranking-star::before { content: "=EE=95=A1"; }

.fa-chess-king::before { content: "=EF=90=BF"; }

.fa-person-harassing::before { content: "=EE=95=89"; }

.fa-brazilian-real-sign::before { content: "=EE=91=AC"; }

.fa-landmark-alt::before, .fa-landmark-dome::before { content: "=EF=9D=92";=
 }

.fa-arrow-up::before { content: "=EF=81=A2"; }

.fa-television::before, .fa-tv-alt::before, .fa-tv::before { content: "=EF=
=89=AC"; }

.fa-shrimp::before { content: "=EE=91=88"; }

.fa-list-check::before, .fa-tasks::before { content: "=EF=82=AE"; }

.fa-jug-detergent::before { content: "=EE=94=99"; }

.fa-circle-user::before, .fa-user-circle::before { content: "=EF=8A=BD"; }

.fa-user-shield::before { content: "=EF=94=85"; }

.fa-wind::before { content: "=EF=9C=AE"; }

.fa-car-burst::before, .fa-car-crash::before { content: "=EF=97=A1"; }

.fa-y::before { content: "Y"; }

.fa-person-snowboarding::before, .fa-snowboarding::before { content: "=EF=
=9F=8E"; }

.fa-shipping-fast::before, .fa-truck-fast::before { content: "=EF=92=8B"; }

.fa-fish::before { content: "=EF=95=B8"; }

.fa-user-graduate::before { content: "=EF=94=81"; }

.fa-adjust::before, .fa-circle-half-stroke::before { content: "=EF=81=82"; =
}

.fa-clapperboard::before { content: "=EE=84=B1"; }

.fa-circle-radiation::before, .fa-radiation-alt::before { content: "=EF=9E=
=BA"; }

.fa-baseball-ball::before, .fa-baseball::before { content: "=EF=90=B3"; }

.fa-jet-fighter-up::before { content: "=EE=94=98"; }

.fa-diagram-project::before, .fa-project-diagram::before { content: "=EF=95=
=82"; }

.fa-copy::before { content: "=EF=83=85"; }

.fa-volume-mute::before, .fa-volume-times::before, .fa-volume-xmark::before=
 { content: "=EF=9A=A9"; }

.fa-hand-sparkles::before { content: "=EE=81=9D"; }

.fa-grip-horizontal::before, .fa-grip::before { content: "=EF=96=8D"; }

.fa-share-from-square::before, .fa-share-square::before { content: "=EF=85=
=8D"; }

.fa-child-combatant::before, .fa-child-rifle::before { content: "=EE=93=A0"=
; }

.fa-gun::before { content: "=EE=86=9B"; }

.fa-phone-square::before, .fa-square-phone::before { content: "=EF=82=98"; =
}

.fa-add::before, .fa-plus::before { content: "+"; }

.fa-expand::before { content: "=EF=81=A5"; }

.fa-computer::before { content: "=EE=93=A5"; }

.fa-close::before, .fa-multiply::before, .fa-remove::before, .fa-times::bef=
ore, .fa-xmark::before { content: "=EF=80=8D"; }

.fa-arrows-up-down-left-right::before, .fa-arrows::before { content: "=EF=
=81=87"; }

.fa-chalkboard-teacher::before, .fa-chalkboard-user::before { content: "=EF=
=94=9C"; }

.fa-peso-sign::before { content: "=EE=88=A2"; }

.fa-building-shield::before { content: "=EE=93=98"; }

.fa-baby::before { content: "=EF=9D=BC"; }

.fa-users-line::before { content: "=EE=96=92"; }

.fa-quote-left-alt::before, .fa-quote-left::before { content: "=EF=84=8D"; =
}

.fa-tractor::before { content: "=EF=9C=A2"; }

.fa-trash-arrow-up::before, .fa-trash-restore::before { content: "=EF=A0=A9=
"; }

.fa-arrow-down-up-lock::before { content: "=EE=92=B0"; }

.fa-lines-leaning::before { content: "=EE=94=9E"; }

.fa-ruler-combined::before { content: "=EF=95=86"; }

.fa-copyright::before { content: "=EF=87=B9"; }

.fa-equals::before { content: "=3D"; }

.fa-blender::before { content: "=EF=94=97"; }

.fa-teeth::before { content: "=EF=98=AE"; }

.fa-ils::before, .fa-shekel-sign::before, .fa-shekel::before, .fa-sheqel-si=
gn::before, .fa-sheqel::before { content: "=EF=88=8B"; }

.fa-map::before { content: "=EF=89=B9"; }

.fa-rocket::before { content: "=EF=84=B5"; }

.fa-photo-film::before, .fa-photo-video::before { content: "=EF=A1=BC"; }

.fa-folder-minus::before { content: "=EF=99=9D"; }

.fa-store::before { content: "=EF=95=8E"; }

.fa-arrow-trend-up::before { content: "=EE=82=98"; }

.fa-plug-circle-minus::before { content: "=EE=95=9E"; }

.fa-sign-hanging::before, .fa-sign::before { content: "=EF=93=99"; }

.fa-bezier-curve::before { content: "=EF=95=9B"; }

.fa-bell-slash::before { content: "=EF=87=B6"; }

.fa-tablet-android::before, .fa-tablet::before { content: "=EF=8F=BB"; }

.fa-school-flag::before { content: "=EE=95=AE"; }

.fa-fill::before { content: "=EF=95=B5"; }

.fa-angle-up::before { content: "=EF=84=86"; }

.fa-drumstick-bite::before { content: "=EF=9B=97"; }

.fa-holly-berry::before { content: "=EF=9E=AA"; }

.fa-chevron-left::before { content: "=EF=81=93"; }

.fa-bacteria::before { content: "=EE=81=99"; }

.fa-hand-lizard::before { content: "=EF=89=98"; }

.fa-notdef::before { content: "=EE=87=BE"; }

.fa-disease::before { content: "=EF=9F=BA"; }

.fa-briefcase-medical::before { content: "=EF=91=A9"; }

.fa-genderless::before { content: "=EF=88=AD"; }

.fa-chevron-right::before { content: "=EF=81=94"; }

.fa-retweet::before { content: "=EF=81=B9"; }

.fa-car-alt::before, .fa-car-rear::before { content: "=EF=97=9E"; }

.fa-pump-soap::before { content: "=EE=81=AB"; }

.fa-video-slash::before { content: "=EF=93=A2"; }

.fa-battery-2::before, .fa-battery-quarter::before { content: "=EF=89=83"; =
}

.fa-radio::before { content: "=EF=A3=97"; }

.fa-baby-carriage::before, .fa-carriage-baby::before { content: "=EF=9D=BD"=
; }

.fa-traffic-light::before { content: "=EF=98=B7"; }

.fa-thermometer::before { content: "=EF=92=91"; }

.fa-vr-cardboard::before { content: "=EF=9C=A9"; }

.fa-hand-middle-finger::before { content: "=EF=A0=86"; }

.fa-percent::before, .fa-percentage::before { content: "%"; }

.fa-truck-moving::before { content: "=EF=93=9F"; }

.fa-glass-water-droplet::before { content: "=EE=93=B5"; }

.fa-display::before { content: "=EE=85=A3"; }

.fa-face-smile::before, .fa-smile::before { content: "=EF=84=98"; }

.fa-thumb-tack::before, .fa-thumbtack::before { content: "=EF=82=8D"; }

.fa-trophy::before { content: "=EF=82=91"; }

.fa-person-praying::before, .fa-pray::before { content: "=EF=9A=83"; }

.fa-hammer::before { content: "=EF=9B=A3"; }

.fa-hand-peace::before { content: "=EF=89=9B"; }

.fa-rotate::before, .fa-sync-alt::before { content: "=EF=8B=B1"; }

.fa-spinner::before { content: "=EF=84=90"; }

.fa-robot::before { content: "=EF=95=84"; }

.fa-peace::before { content: "=EF=99=BC"; }

.fa-cogs::before, .fa-gears::before { content: "=EF=82=85"; }

.fa-warehouse::before { content: "=EF=92=94"; }

.fa-arrow-up-right-dots::before { content: "=EE=92=B7"; }

.fa-splotch::before { content: "=EF=96=BC"; }

.fa-face-grin-hearts::before, .fa-grin-hearts::before { content: "=EF=96=84=
"; }

.fa-dice-four::before { content: "=EF=94=A4"; }

.fa-sim-card::before { content: "=EF=9F=84"; }

.fa-transgender-alt::before, .fa-transgender::before { content: "=EF=88=A5"=
; }

.fa-mercury::before { content: "=EF=88=A3"; }

.fa-arrow-turn-down::before, .fa-level-down::before { content: "=EF=85=89";=
 }

.fa-person-falling-burst::before { content: "=EE=95=87"; }

.fa-award::before { content: "=EF=95=99"; }

.fa-ticket-alt::before, .fa-ticket-simple::before { content: "=EF=8F=BF"; }

.fa-building::before { content: "=EF=86=AD"; }

.fa-angle-double-left::before, .fa-angles-left::before { content: "=EF=84=
=80"; }

.fa-qrcode::before { content: "=EF=80=A9"; }

.fa-clock-rotate-left::before, .fa-history::before { content: "=EF=87=9A"; =
}

.fa-face-grin-beam-sweat::before, .fa-grin-beam-sweat::before { content: "=
=EF=96=83"; }

.fa-arrow-right-from-file::before, .fa-file-export::before { content: "=EF=
=95=AE"; }

.fa-shield-blank::before, .fa-shield::before { content: "=EF=84=B2"; }

.fa-arrow-up-short-wide::before, .fa-sort-amount-up-alt::before { content: =
"=EF=A2=85"; }

.fa-house-medical::before { content: "=EE=8E=B2"; }

.fa-golf-ball-tee::before, .fa-golf-ball::before { content: "=EF=91=90"; }

.fa-chevron-circle-left::before, .fa-circle-chevron-left::before { content:=
 "=EF=84=B7"; }

.fa-house-chimney-window::before { content: "=EE=80=8D"; }

.fa-pen-nib::before { content: "=EF=96=AD"; }

.fa-tent-arrow-turn-left::before { content: "=EE=96=80"; }

.fa-tents::before { content: "=EE=96=82"; }

.fa-magic::before, .fa-wand-magic::before { content: "=EF=83=90"; }

.fa-dog::before { content: "=EF=9B=93"; }

.fa-carrot::before { content: "=EF=9E=87"; }

.fa-moon::before { content: "=EF=86=86"; }

.fa-wine-glass-alt::before, .fa-wine-glass-empty::before { content: "=EF=97=
=8E"; }

.fa-cheese::before { content: "=EF=9F=AF"; }

.fa-yin-yang::before { content: "=EF=9A=AD"; }

.fa-music::before { content: "=EF=80=81"; }

.fa-code-commit::before { content: "=EF=8E=86"; }

.fa-temperature-low::before { content: "=EF=9D=AB"; }

.fa-biking::before, .fa-person-biking::before { content: "=EF=A1=8A"; }

.fa-broom::before { content: "=EF=94=9A"; }

.fa-shield-heart::before { content: "=EE=95=B4"; }

.fa-gopuram::before { content: "=EF=99=A4"; }

.fa-earth-oceania::before, .fa-globe-oceania::before { content: "=EE=91=BB"=
; }

.fa-square-xmark::before, .fa-times-square::before, .fa-xmark-square::befor=
e { content: "=EF=8B=93"; }

.fa-hashtag::before { content: "#"; }

.fa-expand-alt::before, .fa-up-right-and-down-left-from-center::before { co=
ntent: "=EF=90=A4"; }

.fa-oil-can::before { content: "=EF=98=93"; }

.fa-t::before { content: "T"; }

.fa-hippo::before { content: "=EF=9B=AD"; }

.fa-chart-column::before { content: "=EE=83=A3"; }

.fa-infinity::before { content: "=EF=94=B4"; }

.fa-vial-circle-check::before { content: "=EE=96=96"; }

.fa-person-arrow-down-to-line::before { content: "=EE=94=B8"; }

.fa-voicemail::before { content: "=EF=A2=97"; }

.fa-fan::before { content: "=EF=A1=A3"; }

.fa-person-walking-luggage::before { content: "=EE=95=94"; }

.fa-arrows-alt-v::before, .fa-up-down::before { content: "=EF=8C=B8"; }

.fa-cloud-moon-rain::before { content: "=EF=9C=BC"; }

.fa-calendar::before { content: "=EF=84=B3"; }

.fa-trailer::before { content: "=EE=81=81"; }

.fa-bahai::before, .fa-haykal::before { content: "=EF=99=A6"; }

.fa-sd-card::before { content: "=EF=9F=82"; }

.fa-dragon::before { content: "=EF=9B=95"; }

.fa-shoe-prints::before { content: "=EF=95=8B"; }

.fa-circle-plus::before, .fa-plus-circle::before { content: "=EF=81=95"; }

.fa-face-grin-tongue-wink::before, .fa-grin-tongue-wink::before { content: =
"=EF=96=8B"; }

.fa-hand-holding::before { content: "=EF=92=BD"; }

.fa-plug-circle-exclamation::before { content: "=EE=95=9D"; }

.fa-chain-broken::before, .fa-chain-slash::before, .fa-link-slash::before, =
.fa-unlink::before { content: "=EF=84=A7"; }

.fa-clone::before { content: "=EF=89=8D"; }

.fa-person-walking-arrow-loop-left::before { content: "=EE=95=91"; }

.fa-arrow-up-z-a::before, .fa-sort-alpha-up-alt::before { content: "=EF=A2=
=82"; }

.fa-fire-alt::before, .fa-fire-flame-curved::before { content: "=EF=9F=A4";=
 }

.fa-tornado::before { content: "=EF=9D=AF"; }

.fa-file-circle-plus::before { content: "=EE=92=94"; }

.fa-book-quran::before, .fa-quran::before { content: "=EF=9A=87"; }

.fa-anchor::before { content: "=EF=84=BD"; }

.fa-border-all::before { content: "=EF=A1=8C"; }

.fa-angry::before, .fa-face-angry::before { content: "=EF=95=96"; }

.fa-cookie-bite::before { content: "=EF=95=A4"; }

.fa-arrow-trend-down::before { content: "=EE=82=97"; }

.fa-feed::before, .fa-rss::before { content: "=EF=82=9E"; }

.fa-draw-polygon::before { content: "=EF=97=AE"; }

.fa-balance-scale::before, .fa-scale-balanced::before { content: "=EF=89=8E=
"; }

.fa-gauge-simple-high::before, .fa-tachometer-fast::before, .fa-tachometer:=
:before { content: "=EF=98=AA"; }

.fa-shower::before { content: "=EF=8B=8C"; }

.fa-desktop-alt::before, .fa-desktop::before { content: "=EF=8E=90"; }

.fa-m::before { content: "M"; }

.fa-table-list::before, .fa-th-list::before { content: "=EF=80=8B"; }

.fa-comment-sms::before, .fa-sms::before { content: "=EF=9F=8D"; }

.fa-book::before { content: "=EF=80=AD"; }

.fa-user-plus::before { content: "=EF=88=B4"; }

.fa-check::before { content: "=EF=80=8C"; }

.fa-battery-4::before, .fa-battery-three-quarters::before { content: "=EF=
=89=81"; }

.fa-house-circle-check::before { content: "=EE=94=89"; }

.fa-angle-left::before { content: "=EF=84=84"; }

.fa-diagram-successor::before { content: "=EE=91=BA"; }

.fa-truck-arrow-right::before { content: "=EE=96=8B"; }

.fa-arrows-split-up-and-left::before { content: "=EE=92=BC"; }

.fa-fist-raised::before, .fa-hand-fist::before { content: "=EF=9B=9E"; }

.fa-cloud-moon::before { content: "=EF=9B=83"; }

.fa-briefcase::before { content: "=EF=82=B1"; }

.fa-person-falling::before { content: "=EE=95=86"; }

.fa-image-portrait::before, .fa-portrait::before { content: "=EF=8F=A0"; }

.fa-user-tag::before { content: "=EF=94=87"; }

.fa-rug::before { content: "=EE=95=A9"; }

.fa-earth-europe::before, .fa-globe-europe::before { content: "=EF=9E=A2"; =
}

.fa-cart-flatbed-suitcase::before, .fa-luggage-cart::before { content: "=EF=
=96=9D"; }

.fa-rectangle-times::before, .fa-rectangle-xmark::before, .fa-times-rectang=
le::before, .fa-window-close::before { content: "=EF=90=90"; }

.fa-baht-sign::before { content: "=EE=82=AC"; }

.fa-book-open::before { content: "=EF=94=98"; }

.fa-book-journal-whills::before, .fa-journal-whills::before { content: "=EF=
=99=AA"; }

.fa-handcuffs::before { content: "=EE=93=B8"; }

.fa-exclamation-triangle::before, .fa-triangle-exclamation::before, .fa-war=
ning::before { content: "=EF=81=B1"; }

.fa-database::before { content: "=EF=87=80"; }

.fa-arrow-turn-right::before, .fa-mail-forward::before, .fa-share::before {=
 content: "=EF=81=A4"; }

.fa-bottle-droplet::before { content: "=EE=93=84"; }

.fa-mask-face::before { content: "=EE=87=97"; }

.fa-hill-rockslide::before { content: "=EE=94=88"; }

.fa-exchange-alt::before, .fa-right-left::before { content: "=EF=8D=A2"; }

.fa-paper-plane::before { content: "=EF=87=98"; }

.fa-road-circle-exclamation::before { content: "=EE=95=A5"; }

.fa-dungeon::before { content: "=EF=9B=99"; }

.fa-align-right::before { content: "=EF=80=B8"; }

.fa-money-bill-1-wave::before, .fa-money-bill-wave-alt::before { content: "=
=EF=94=BB"; }

.fa-life-ring::before { content: "=EF=87=8D"; }

.fa-hands::before, .fa-sign-language::before, .fa-signing::before { content=
: "=EF=8A=A7"; }

.fa-calendar-day::before { content: "=EF=9E=83"; }

.fa-ladder-water::before, .fa-swimming-pool::before, .fa-water-ladder::befo=
re { content: "=EF=97=85"; }

.fa-arrows-up-down::before, .fa-arrows-v::before { content: "=EF=81=BD"; }

.fa-face-grimace::before, .fa-grimace::before { content: "=EF=95=BF"; }

.fa-wheelchair-alt::before, .fa-wheelchair-move::before { content: "=EE=8B=
=8E"; }

.fa-level-down-alt::before, .fa-turn-down::before { content: "=EF=8E=BE"; }

.fa-person-walking-arrow-right::before { content: "=EE=95=92"; }

.fa-envelope-square::before, .fa-square-envelope::before { content: "=EF=86=
=99"; }

.fa-dice::before { content: "=EF=94=A2"; }

.fa-bowling-ball::before { content: "=EF=90=B6"; }

.fa-brain::before { content: "=EF=97=9C"; }

.fa-band-aid::before, .fa-bandage::before { content: "=EF=91=A2"; }

.fa-calendar-minus::before { content: "=EF=89=B2"; }

.fa-circle-xmark::before, .fa-times-circle::before, .fa-xmark-circle::befor=
e { content: "=EF=81=97"; }

.fa-gifts::before { content: "=EF=9E=9C"; }

.fa-hotel::before { content: "=EF=96=94"; }

.fa-earth-asia::before, .fa-globe-asia::before { content: "=EF=95=BE"; }

.fa-id-card-alt::before, .fa-id-card-clip::before { content: "=EF=91=BF"; }

.fa-magnifying-glass-plus::before, .fa-search-plus::before { content: "=EF=
=80=8E"; }

.fa-thumbs-up::before { content: "=EF=85=A4"; }

.fa-user-clock::before { content: "=EF=93=BD"; }

.fa-allergies::before, .fa-hand-dots::before { content: "=EF=91=A1"; }

.fa-file-invoice::before { content: "=EF=95=B0"; }

.fa-window-minimize::before { content: "=EF=8B=91"; }

.fa-coffee::before, .fa-mug-saucer::before { content: "=EF=83=B4"; }

.fa-brush::before { content: "=EF=95=9D"; }

.fa-mask::before { content: "=EF=9B=BA"; }

.fa-magnifying-glass-minus::before, .fa-search-minus::before { content: "=
=EF=80=90"; }

.fa-ruler-vertical::before { content: "=EF=95=88"; }

.fa-user-alt::before, .fa-user-large::before { content: "=EF=90=86"; }

.fa-train-tram::before { content: "=EE=96=B4"; }

.fa-user-nurse::before { content: "=EF=A0=AF"; }

.fa-syringe::before { content: "=EF=92=8E"; }

.fa-cloud-sun::before { content: "=EF=9B=84"; }

.fa-stopwatch-20::before { content: "=EE=81=AF"; }

.fa-square-full::before { content: "=EF=91=9C"; }

.fa-magnet::before { content: "=EF=81=B6"; }

.fa-jar::before { content: "=EE=94=96"; }

.fa-note-sticky::before, .fa-sticky-note::before { content: "=EF=89=89"; }

.fa-bug-slash::before { content: "=EE=92=90"; }

.fa-arrow-up-from-water-pump::before { content: "=EE=92=B6"; }

.fa-bone::before { content: "=EF=97=97"; }

.fa-user-injured::before { content: "=EF=9C=A8"; }

.fa-face-sad-tear::before, .fa-sad-tear::before { content: "=EF=96=B4"; }

.fa-plane::before { content: "=EF=81=B2"; }

.fa-tent-arrows-down::before { content: "=EE=96=81"; }

.fa-exclamation::before { content: "!"; }

.fa-arrows-spin::before { content: "=EE=92=BB"; }

.fa-print::before { content: "=EF=80=AF"; }

.fa-try::before, .fa-turkish-lira-sign::before, .fa-turkish-lira::before { =
content: "=EE=8A=BB"; }

.fa-dollar-sign::before, .fa-dollar::before, .fa-usd::before { content: "$"=
; }

.fa-x::before { content: "X"; }

.fa-magnifying-glass-dollar::before, .fa-search-dollar::before { content: "=
=EF=9A=88"; }

.fa-users-cog::before, .fa-users-gear::before { content: "=EF=94=89"; }

.fa-person-military-pointing::before { content: "=EE=95=8A"; }

.fa-bank::before, .fa-building-columns::before, .fa-institution::before, .f=
a-museum::before, .fa-university::before { content: "=EF=86=9C"; }

.fa-umbrella::before { content: "=EF=83=A9"; }

.fa-trowel::before { content: "=EE=96=89"; }

.fa-d::before { content: "D"; }

.fa-stapler::before { content: "=EE=96=AF"; }

.fa-masks-theater::before, .fa-theater-masks::before { content: "=EF=98=B0"=
; }

.fa-kip-sign::before { content: "=EE=87=84"; }

.fa-hand-point-left::before { content: "=EF=82=A5"; }

.fa-handshake-alt::before, .fa-handshake-simple::before { content: "=EF=93=
=86"; }

.fa-fighter-jet::before, .fa-jet-fighter::before { content: "=EF=83=BB"; }

.fa-share-alt-square::before, .fa-square-share-nodes::before { content: "=
=EF=87=A1"; }

.fa-barcode::before { content: "=EF=80=AA"; }

.fa-plus-minus::before { content: "=EE=90=BC"; }

.fa-video-camera::before, .fa-video::before { content: "=EF=80=BD"; }

.fa-graduation-cap::before, .fa-mortar-board::before { content: "=EF=86=9D"=
; }

.fa-hand-holding-medical::before { content: "=EE=81=9C"; }

.fa-person-circle-check::before { content: "=EE=94=BE"; }

.fa-level-up-alt::before, .fa-turn-up::before { content: "=EF=8E=BF"; }

.fa-sr-only, .fa-sr-only-focusable:not(:focus), .sr-only, .sr-only-focusabl=
e:not(:focus) { position: absolute; width: 1px; height: 1px; padding: 0px; =
margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space=
: nowrap; border-width: 0px; }

:host, :root { --fa-style-family-brands: "Font Awesome 6 Brands"; --fa-font=
-brands: normal 400 1em/1 "Font Awesome 6 Brands"; }

@font-face { font-family: "Font Awesome 6 Brands"; font-style: normal; font=
-weight: 400; font-display: block; src: url("../webfonts/fa-brands-400.woff=
2") format("woff2"), url("../webfonts/fa-brands-400.ttf") format("truetype"=
); }

.fa-brands, .fab { font-weight: 400; }

.fa-monero::before { content: "=EF=8F=90"; }

.fa-hooli::before { content: "=EF=90=A7"; }

.fa-yelp::before { content: "=EF=87=A9"; }

.fa-cc-visa::before { content: "=EF=87=B0"; }

.fa-lastfm::before { content: "=EF=88=82"; }

.fa-shopware::before { content: "=EF=96=B5"; }

.fa-creative-commons-nc::before { content: "=EF=93=A8"; }

.fa-aws::before { content: "=EF=8D=B5"; }

.fa-redhat::before { content: "=EF=9E=BC"; }

.fa-yoast::before { content: "=EF=8A=B1"; }

.fa-cloudflare::before { content: "=EE=81=BD"; }

.fa-ups::before { content: "=EF=9F=A0"; }

.fa-wpexplorer::before { content: "=EF=8B=9E"; }

.fa-dyalog::before { content: "=EF=8E=99"; }

.fa-bity::before { content: "=EF=8D=BA"; }

.fa-stackpath::before { content: "=EF=A1=82"; }

.fa-buysellads::before { content: "=EF=88=8D"; }

.fa-first-order::before { content: "=EF=8A=B0"; }

.fa-modx::before { content: "=EF=8A=85"; }

.fa-guilded::before { content: "=EE=81=BE"; }

.fa-vnv::before { content: "=EF=90=8B"; }

.fa-js-square::before, .fa-square-js::before { content: "=EF=8E=B9"; }

.fa-microsoft::before { content: "=EF=8F=8A"; }

.fa-qq::before { content: "=EF=87=96"; }

.fa-orcid::before { content: "=EF=A3=92"; }

.fa-java::before { content: "=EF=93=A4"; }

.fa-invision::before { content: "=EF=9E=B0"; }

.fa-creative-commons-pd-alt::before { content: "=EF=93=AD"; }

.fa-centercode::before { content: "=EF=8E=80"; }

.fa-glide-g::before { content: "=EF=8A=A6"; }

.fa-drupal::before { content: "=EF=86=A9"; }

.fa-hire-a-helper::before { content: "=EF=8E=B0"; }

.fa-creative-commons-by::before { content: "=EF=93=A7"; }

.fa-unity::before { content: "=EE=81=89"; }

.fa-whmcs::before { content: "=EF=90=8D"; }

.fa-rocketchat::before { content: "=EF=8F=A8"; }

.fa-vk::before { content: "=EF=86=89"; }

.fa-untappd::before { content: "=EF=90=85"; }

.fa-mailchimp::before { content: "=EF=96=9E"; }

.fa-css3-alt::before { content: "=EF=8E=8B"; }

.fa-reddit-square::before, .fa-square-reddit::before { content: "=EF=86=A2"=
; }

.fa-vimeo-v::before { content: "=EF=89=BD"; }

.fa-contao::before { content: "=EF=89=AD"; }

.fa-square-font-awesome::before { content: "=EE=96=AD"; }

.fa-deskpro::before { content: "=EF=8E=8F"; }

.fa-sistrix::before { content: "=EF=8F=AE"; }

.fa-instagram-square::before, .fa-square-instagram::before { content: "=EE=
=81=95"; }

.fa-battle-net::before { content: "=EF=A0=B5"; }

.fa-the-red-yeti::before { content: "=EF=9A=9D"; }

.fa-hacker-news-square::before, .fa-square-hacker-news::before { content: "=
=EF=8E=AF"; }

.fa-edge::before { content: "=EF=8A=82"; }

.fa-napster::before { content: "=EF=8F=92"; }

.fa-snapchat-square::before, .fa-square-snapchat::before { content: "=EF=8A=
=AD"; }

.fa-google-plus-g::before { content: "=EF=83=95"; }

.fa-artstation::before { content: "=EF=9D=BA"; }

.fa-markdown::before { content: "=EF=98=8F"; }

.fa-sourcetree::before { content: "=EF=9F=93"; }

.fa-google-plus::before { content: "=EF=8A=B3"; }

.fa-diaspora::before { content: "=EF=9E=91"; }

.fa-foursquare::before { content: "=EF=86=80"; }

.fa-stack-overflow::before { content: "=EF=85=AC"; }

.fa-github-alt::before { content: "=EF=84=93"; }

.fa-phoenix-squadron::before { content: "=EF=94=91"; }

.fa-pagelines::before { content: "=EF=86=8C"; }

.fa-algolia::before { content: "=EF=8D=AC"; }

.fa-red-river::before { content: "=EF=8F=A3"; }

.fa-creative-commons-sa::before { content: "=EF=93=AF"; }

.fa-safari::before { content: "=EF=89=A7"; }

.fa-google::before { content: "=EF=86=A0"; }

.fa-font-awesome-alt::before, .fa-square-font-awesome-stroke::before { cont=
ent: "=EF=8D=9C"; }

.fa-atlassian::before { content: "=EF=9D=BB"; }

.fa-linkedin-in::before { content: "=EF=83=A1"; }

.fa-digital-ocean::before { content: "=EF=8E=91"; }

.fa-nimblr::before { content: "=EF=96=A8"; }

.fa-chromecast::before { content: "=EF=A0=B8"; }

.fa-evernote::before { content: "=EF=A0=B9"; }

.fa-hacker-news::before { content: "=EF=87=94"; }

.fa-creative-commons-sampling::before { content: "=EF=93=B0"; }

.fa-adversal::before { content: "=EF=8D=AA"; }

.fa-creative-commons::before { content: "=EF=89=9E"; }

.fa-watchman-monitoring::before { content: "=EE=82=87"; }

.fa-fonticons::before { content: "=EF=8A=80"; }

.fa-weixin::before { content: "=EF=87=97"; }

.fa-shirtsinbulk::before { content: "=EF=88=94"; }

.fa-codepen::before { content: "=EF=87=8B"; }

.fa-git-alt::before { content: "=EF=A1=81"; }

.fa-lyft::before { content: "=EF=8F=83"; }

.fa-rev::before { content: "=EF=96=B2"; }

.fa-windows::before { content: "=EF=85=BA"; }

.fa-wizards-of-the-coast::before { content: "=EF=9C=B0"; }

.fa-square-viadeo::before, .fa-viadeo-square::before { content: "=EF=8A=AA"=
; }

.fa-meetup::before { content: "=EF=8B=A0"; }

.fa-centos::before { content: "=EF=9E=89"; }

.fa-adn::before { content: "=EF=85=B0"; }

.fa-cloudsmith::before { content: "=EF=8E=84"; }

.fa-pied-piper-alt::before { content: "=EF=86=A8"; }

.fa-dribbble-square::before, .fa-square-dribbble::before { content: "=EF=8E=
=97"; }

.fa-codiepie::before { content: "=EF=8A=84"; }

.fa-node::before { content: "=EF=90=99"; }

.fa-mix::before { content: "=EF=8F=8B"; }

.fa-steam::before { content: "=EF=86=B6"; }

.fa-cc-apple-pay::before { content: "=EF=90=96"; }

.fa-scribd::before { content: "=EF=8A=8A"; }

.fa-openid::before { content: "=EF=86=9B"; }

.fa-instalod::before { content: "=EE=82=81"; }

.fa-expeditedssl::before { content: "=EF=88=BE"; }

.fa-sellcast::before { content: "=EF=8B=9A"; }

.fa-square-twitter::before, .fa-twitter-square::before { content: "=EF=82=
=81"; }

.fa-r-project::before { content: "=EF=93=B7"; }

.fa-delicious::before { content: "=EF=86=A5"; }

.fa-freebsd::before { content: "=EF=8E=A4"; }

.fa-vuejs::before { content: "=EF=90=9F"; }

.fa-accusoft::before { content: "=EF=8D=A9"; }

.fa-ioxhost::before { content: "=EF=88=88"; }

.fa-fonticons-fi::before { content: "=EF=8E=A2"; }

.fa-app-store::before { content: "=EF=8D=AF"; }

.fa-cc-mastercard::before { content: "=EF=87=B1"; }

.fa-itunes-note::before { content: "=EF=8E=B5"; }

.fa-golang::before { content: "=EE=90=8F"; }

.fa-kickstarter::before { content: "=EF=8E=BB"; }

.fa-grav::before { content: "=EF=8B=96"; }

.fa-weibo::before { content: "=EF=86=8A"; }

.fa-uncharted::before { content: "=EE=82=84"; }

.fa-firstdraft::before { content: "=EF=8E=A1"; }

.fa-square-youtube::before, .fa-youtube-square::before { content: "=EF=90=
=B1"; }

.fa-wikipedia-w::before { content: "=EF=89=A6"; }

.fa-rendact::before, .fa-wpressr::before { content: "=EF=8F=A4"; }

.fa-angellist::before { content: "=EF=88=89"; }

.fa-galactic-republic::before { content: "=EF=94=8C"; }

.fa-nfc-directional::before { content: "=EE=94=B0"; }

.fa-skype::before { content: "=EF=85=BE"; }

.fa-joget::before { content: "=EF=8E=B7"; }

.fa-fedora::before { content: "=EF=9E=98"; }

.fa-stripe-s::before { content: "=EF=90=AA"; }

.fa-meta::before { content: "=EE=92=9B"; }

.fa-laravel::before { content: "=EF=8E=BD"; }

.fa-hotjar::before { content: "=EF=8E=B1"; }

.fa-bluetooth-b::before { content: "=EF=8A=94"; }

.fa-sticker-mule::before { content: "=EF=8F=B7"; }

.fa-creative-commons-zero::before { content: "=EF=93=B3"; }

.fa-hips::before { content: "=EF=91=92"; }

.fa-behance::before { content: "=EF=86=B4"; }

.fa-reddit::before { content: "=EF=86=A1"; }

.fa-discord::before { content: "=EF=8E=92"; }

.fa-chrome::before { content: "=EF=89=A8"; }

.fa-app-store-ios::before { content: "=EF=8D=B0"; }

.fa-cc-discover::before { content: "=EF=87=B2"; }

.fa-wpbeginner::before { content: "=EF=8A=97"; }

.fa-confluence::before { content: "=EF=9E=8D"; }

.fa-mdb::before { content: "=EF=A3=8A"; }

.fa-dochub::before { content: "=EF=8E=94"; }

.fa-accessible-icon::before { content: "=EF=8D=A8"; }

.fa-ebay::before { content: "=EF=93=B4"; }

.fa-amazon::before { content: "=EF=89=B0"; }

.fa-unsplash::before { content: "=EE=81=BC"; }

.fa-yarn::before { content: "=EF=9F=A3"; }

.fa-square-steam::before, .fa-steam-square::before { content: "=EF=86=B7"; =
}

.fa-500px::before { content: "=EF=89=AE"; }

.fa-square-vimeo::before, .fa-vimeo-square::before { content: "=EF=86=94"; =
}

.fa-asymmetrik::before { content: "=EF=8D=B2"; }

.fa-font-awesome-flag::before, .fa-font-awesome-logo-full::before, .fa-font=
-awesome::before { content: "=EF=8A=B4"; }

.fa-gratipay::before { content: "=EF=86=84"; }

.fa-apple::before { content: "=EF=85=B9"; }

.fa-hive::before { content: "=EE=81=BF"; }

.fa-gitkraken::before { content: "=EF=8E=A6"; }

.fa-keybase::before { content: "=EF=93=B5"; }

.fa-apple-pay::before { content: "=EF=90=95"; }

.fa-padlet::before { content: "=EE=92=A0"; }

.fa-amazon-pay::before { content: "=EF=90=AC"; }

.fa-github-square::before, .fa-square-github::before { content: "=EF=82=92"=
; }

.fa-stumbleupon::before { content: "=EF=86=A4"; }

.fa-fedex::before { content: "=EF=9E=97"; }

.fa-phoenix-framework::before { content: "=EF=8F=9C"; }

.fa-shopify::before { content: "=EE=81=97"; }

.fa-neos::before { content: "=EF=98=92"; }

.fa-hackerrank::before { content: "=EF=97=B7"; }

.fa-researchgate::before { content: "=EF=93=B8"; }

.fa-swift::before { content: "=EF=A3=A1"; }

.fa-angular::before { content: "=EF=90=A0"; }

.fa-speakap::before { content: "=EF=8F=B3"; }

.fa-angrycreative::before { content: "=EF=8D=AE"; }

.fa-y-combinator::before { content: "=EF=88=BB"; }

.fa-empire::before { content: "=EF=87=91"; }

.fa-envira::before { content: "=EF=8A=99"; }

.fa-gitlab-square::before, .fa-square-gitlab::before { content: "=EE=96=AE"=
; }

.fa-studiovinari::before { content: "=EF=8F=B8"; }

.fa-pied-piper::before { content: "=EF=8A=AE"; }

.fa-wordpress::before { content: "=EF=86=9A"; }

.fa-product-hunt::before { content: "=EF=8A=88"; }

.fa-firefox::before { content: "=EF=89=A9"; }

.fa-linode::before { content: "=EF=8A=B8"; }

.fa-goodreads::before { content: "=EF=8E=A8"; }

.fa-odnoklassniki-square::before, .fa-square-odnoklassniki::before { conten=
t: "=EF=89=A4"; }

.fa-jsfiddle::before { content: "=EF=87=8C"; }

.fa-sith::before { content: "=EF=94=92"; }

.fa-themeisle::before { content: "=EF=8A=B2"; }

.fa-page4::before { content: "=EF=8F=97"; }

.fa-hashnode::before { content: "=EE=92=99"; }

.fa-react::before { content: "=EF=90=9B"; }

.fa-cc-paypal::before { content: "=EF=87=B4"; }

.fa-squarespace::before { content: "=EF=96=BE"; }

.fa-cc-stripe::before { content: "=EF=87=B5"; }

.fa-creative-commons-share::before { content: "=EF=93=B2"; }

.fa-bitcoin::before { content: "=EF=8D=B9"; }

.fa-keycdn::before { content: "=EF=8E=BA"; }

.fa-opera::before { content: "=EF=89=AA"; }

.fa-itch-io::before { content: "=EF=A0=BA"; }

.fa-umbraco::before { content: "=EF=A3=A8"; }

.fa-galactic-senate::before { content: "=EF=94=8D"; }

.fa-ubuntu::before { content: "=EF=9F=9F"; }

.fa-draft2digital::before { content: "=EF=8E=96"; }

.fa-stripe::before { content: "=EF=90=A9"; }

.fa-houzz::before { content: "=EF=89=BC"; }

.fa-gg::before { content: "=EF=89=A0"; }

.fa-dhl::before { content: "=EF=9E=90"; }

.fa-pinterest-square::before, .fa-square-pinterest::before { content: "=EF=
=83=93"; }

.fa-xing::before { content: "=EF=85=A8"; }

.fa-blackberry::before { content: "=EF=8D=BB"; }

.fa-creative-commons-pd::before { content: "=EF=93=AC"; }

.fa-playstation::before { content: "=EF=8F=9F"; }

.fa-quinscape::before { content: "=EF=91=99"; }

.fa-less::before { content: "=EF=90=9D"; }

.fa-blogger-b::before { content: "=EF=8D=BD"; }

.fa-opencart::before { content: "=EF=88=BD"; }

.fa-vine::before { content: "=EF=87=8A"; }

.fa-paypal::before { content: "=EF=87=AD"; }

.fa-gitlab::before { content: "=EF=8A=96"; }

.fa-typo3::before { content: "=EF=90=AB"; }

.fa-reddit-alien::before { content: "=EF=8A=81"; }

.fa-yahoo::before { content: "=EF=86=9E"; }

.fa-dailymotion::before { content: "=EE=81=92"; }

.fa-affiliatetheme::before { content: "=EF=8D=AB"; }

.fa-pied-piper-pp::before { content: "=EF=86=A7"; }

.fa-bootstrap::before { content: "=EF=A0=B6"; }

.fa-odnoklassniki::before { content: "=EF=89=A3"; }

.fa-nfc-symbol::before { content: "=EE=94=B1"; }

.fa-ethereum::before { content: "=EF=90=AE"; }

.fa-speaker-deck::before { content: "=EF=A0=BC"; }

.fa-creative-commons-nc-eu::before { content: "=EF=93=A9"; }

.fa-patreon::before { content: "=EF=8F=99"; }

.fa-avianex::before { content: "=EF=8D=B4"; }

.fa-ello::before { content: "=EF=97=B1"; }

.fa-gofore::before { content: "=EF=8E=A7"; }

.fa-bimobject::before { content: "=EF=8D=B8"; }

.fa-facebook-f::before { content: "=EF=8E=9E"; }

.fa-google-plus-square::before, .fa-square-google-plus::before { content: "=
=EF=83=94"; }

.fa-mandalorian::before { content: "=EF=94=8F"; }

.fa-first-order-alt::before { content: "=EF=94=8A"; }

.fa-osi::before { content: "=EF=90=9A"; }

.fa-google-wallet::before { content: "=EF=87=AE"; }

.fa-d-and-d-beyond::before { content: "=EF=9B=8A"; }

.fa-periscope::before { content: "=EF=8F=9A"; }

.fa-fulcrum::before { content: "=EF=94=8B"; }

.fa-cloudscale::before { content: "=EF=8E=83"; }

.fa-forumbee::before { content: "=EF=88=91"; }

.fa-mizuni::before { content: "=EF=8F=8C"; }

.fa-schlix::before { content: "=EF=8F=AA"; }

.fa-square-xing::before, .fa-xing-square::before { content: "=EF=85=A9"; }

.fa-bandcamp::before { content: "=EF=8B=95"; }

.fa-wpforms::before { content: "=EF=8A=98"; }

.fa-cloudversify::before { content: "=EF=8E=85"; }

.fa-usps::before { content: "=EF=9F=A1"; }

.fa-megaport::before { content: "=EF=96=A3"; }

.fa-magento::before { content: "=EF=8F=84"; }

.fa-spotify::before { content: "=EF=86=BC"; }

.fa-optin-monster::before { content: "=EF=88=BC"; }

.fa-fly::before { content: "=EF=90=97"; }

.fa-aviato::before { content: "=EF=90=A1"; }

.fa-itunes::before { content: "=EF=8E=B4"; }

.fa-cuttlefish::before { content: "=EF=8E=8C"; }

.fa-blogger::before { content: "=EF=8D=BC"; }

.fa-flickr::before { content: "=EF=85=AE"; }

.fa-viber::before { content: "=EF=90=89"; }

.fa-soundcloud::before { content: "=EF=86=BE"; }

.fa-digg::before { content: "=EF=86=A6"; }

.fa-tencent-weibo::before { content: "=EF=87=95"; }

.fa-symfony::before { content: "=EF=A0=BD"; }

.fa-maxcdn::before { content: "=EF=84=B6"; }

.fa-etsy::before { content: "=EF=8B=97"; }

.fa-facebook-messenger::before { content: "=EF=8E=9F"; }

.fa-audible::before { content: "=EF=8D=B3"; }

.fa-think-peaks::before { content: "=EF=9C=B1"; }

.fa-bilibili::before { content: "=EE=8F=99"; }

.fa-erlang::before { content: "=EF=8E=9D"; }

.fa-cotton-bureau::before { content: "=EF=A2=9E"; }

.fa-dashcube::before { content: "=EF=88=90"; }

.fa-42-group::before, .fa-innosoft::before { content: "=EE=82=80"; }

.fa-stack-exchange::before { content: "=EF=86=8D"; }

.fa-elementor::before { content: "=EF=90=B0"; }

.fa-pied-piper-square::before, .fa-square-pied-piper::before { content: "=
=EE=80=9E"; }

.fa-creative-commons-nd::before { content: "=EF=93=AB"; }

.fa-palfed::before { content: "=EF=8F=98"; }

.fa-superpowers::before { content: "=EF=8B=9D"; }

.fa-resolving::before { content: "=EF=8F=A7"; }

.fa-xbox::before { content: "=EF=90=92"; }

.fa-searchengin::before { content: "=EF=8F=AB"; }

.fa-tiktok::before { content: "=EE=81=BB"; }

.fa-facebook-square::before, .fa-square-facebook::before { content: "=EF=82=
=82"; }

.fa-renren::before { content: "=EF=86=8B"; }

.fa-linux::before { content: "=EF=85=BC"; }

.fa-glide::before { content: "=EF=8A=A5"; }

.fa-linkedin::before { content: "=EF=82=8C"; }

.fa-hubspot::before { content: "=EF=8E=B2"; }

.fa-deploydog::before { content: "=EF=8E=8E"; }

.fa-twitch::before { content: "=EF=87=A8"; }

.fa-ravelry::before { content: "=EF=8B=99"; }

.fa-mixer::before { content: "=EE=81=96"; }

.fa-lastfm-square::before, .fa-square-lastfm::before { content: "=EF=88=83"=
; }

.fa-vimeo::before { content: "=EF=90=8A"; }

.fa-mendeley::before { content: "=EF=9E=B3"; }

.fa-uniregistry::before { content: "=EF=90=84"; }

.fa-figma::before { content: "=EF=9E=99"; }

.fa-creative-commons-remix::before { content: "=EF=93=AE"; }

.fa-cc-amazon-pay::before { content: "=EF=90=AD"; }

.fa-dropbox::before { content: "=EF=85=AB"; }

.fa-instagram::before { content: "=EF=85=AD"; }

.fa-cmplid::before { content: "=EE=8D=A0"; }

.fa-facebook::before { content: "=EF=82=9A"; }

.fa-gripfire::before { content: "=EF=8E=AC"; }

.fa-jedi-order::before { content: "=EF=94=8E"; }

.fa-uikit::before { content: "=EF=90=83"; }

.fa-fort-awesome-alt::before { content: "=EF=8E=A3"; }

.fa-phabricator::before { content: "=EF=8F=9B"; }

.fa-ussunnah::before { content: "=EF=90=87"; }

.fa-earlybirds::before { content: "=EF=8E=9A"; }

.fa-trade-federation::before { content: "=EF=94=93"; }

.fa-autoprefixer::before { content: "=EF=90=9C"; }

.fa-whatsapp::before { content: "=EF=88=B2"; }

.fa-slideshare::before { content: "=EF=87=A7"; }

.fa-google-play::before { content: "=EF=8E=AB"; }

.fa-viadeo::before { content: "=EF=8A=A9"; }

.fa-line::before { content: "=EF=8F=80"; }

.fa-google-drive::before { content: "=EF=8E=AA"; }

.fa-servicestack::before { content: "=EF=8F=AC"; }

.fa-simplybuilt::before { content: "=EF=88=95"; }

.fa-bitbucket::before { content: "=EF=85=B1"; }

.fa-imdb::before { content: "=EF=8B=98"; }

.fa-deezer::before { content: "=EE=81=B7"; }

.fa-raspberry-pi::before { content: "=EF=9E=BB"; }

.fa-jira::before { content: "=EF=9E=B1"; }

.fa-docker::before { content: "=EF=8E=95"; }

.fa-screenpal::before { content: "=EE=95=B0"; }

.fa-bluetooth::before { content: "=EF=8A=93"; }

.fa-gitter::before { content: "=EF=90=A6"; }

.fa-d-and-d::before { content: "=EF=8E=8D"; }

.fa-microblog::before { content: "=EE=80=9A"; }

.fa-cc-diners-club::before { content: "=EF=89=8C"; }

.fa-gg-circle::before { content: "=EF=89=A1"; }

.fa-pied-piper-hat::before { content: "=EF=93=A5"; }

.fa-kickstarter-k::before { content: "=EF=8E=BC"; }

.fa-yandex::before { content: "=EF=90=93"; }

.fa-readme::before { content: "=EF=93=95"; }

.fa-html5::before { content: "=EF=84=BB"; }

.fa-sellsy::before { content: "=EF=88=93"; }

.fa-sass::before { content: "=EF=90=9E"; }

.fa-wirsindhandwerk::before, .fa-wsh::before { content: "=EE=8B=90"; }

.fa-buromobelexperte::before { content: "=EF=8D=BF"; }

.fa-salesforce::before { content: "=EF=A0=BB"; }

.fa-octopus-deploy::before { content: "=EE=82=82"; }

.fa-medapps::before { content: "=EF=8F=86"; }

.fa-ns8::before { content: "=EF=8F=95"; }

.fa-pinterest-p::before { content: "=EF=88=B1"; }

.fa-apper::before { content: "=EF=8D=B1"; }

.fa-fort-awesome::before { content: "=EF=8A=86"; }

.fa-waze::before { content: "=EF=A0=BF"; }

.fa-cc-jcb::before { content: "=EF=89=8B"; }

.fa-snapchat-ghost::before, .fa-snapchat::before { content: "=EF=8A=AB"; }

.fa-fantasy-flight-games::before { content: "=EF=9B=9C"; }

.fa-rust::before { content: "=EE=81=BA"; }

.fa-wix::before { content: "=EF=97=8F"; }

.fa-behance-square::before, .fa-square-behance::before { content: "=EF=86=
=B5"; }

.fa-supple::before { content: "=EF=8F=B9"; }

.fa-rebel::before { content: "=EF=87=90"; }

.fa-css3::before { content: "=EF=84=BC"; }

.fa-staylinked::before { content: "=EF=8F=B5"; }

.fa-kaggle::before { content: "=EF=97=BA"; }

.fa-space-awesome::before { content: "=EE=96=AC"; }

.fa-deviantart::before { content: "=EF=86=BD"; }

.fa-cpanel::before { content: "=EF=8E=88"; }

.fa-goodreads-g::before { content: "=EF=8E=A9"; }

.fa-git-square::before, .fa-square-git::before { content: "=EF=87=92"; }

.fa-square-tumblr::before, .fa-tumblr-square::before { content: "=EF=85=B4"=
; }

.fa-trello::before { content: "=EF=86=81"; }

.fa-creative-commons-nc-jp::before { content: "=EF=93=AA"; }

.fa-get-pocket::before { content: "=EF=89=A5"; }

.fa-perbyte::before { content: "=EE=82=83"; }

.fa-grunt::before { content: "=EF=8E=AD"; }

.fa-weebly::before { content: "=EF=97=8C"; }

.fa-connectdevelop::before { content: "=EF=88=8E"; }

.fa-leanpub::before { content: "=EF=88=92"; }

.fa-black-tie::before { content: "=EF=89=BE"; }

.fa-themeco::before { content: "=EF=97=86"; }

.fa-python::before { content: "=EF=8F=A2"; }

.fa-android::before { content: "=EF=85=BB"; }

.fa-bots::before { content: "=EE=8D=80"; }

.fa-free-code-camp::before { content: "=EF=8B=85"; }

.fa-hornbill::before { content: "=EF=96=92"; }

.fa-js::before { content: "=EF=8E=B8"; }

.fa-ideal::before { content: "=EE=80=93"; }

.fa-git::before { content: "=EF=87=93"; }

.fa-dev::before { content: "=EF=9B=8C"; }

.fa-sketch::before { content: "=EF=9F=86"; }

.fa-yandex-international::before { content: "=EF=90=94"; }

.fa-cc-amex::before { content: "=EF=87=B3"; }

.fa-uber::before { content: "=EF=90=82"; }

.fa-github::before { content: "=EF=82=9B"; }

.fa-php::before { content: "=EF=91=97"; }

.fa-alipay::before { content: "=EF=99=82"; }

.fa-youtube::before { content: "=EF=85=A7"; }

.fa-skyatlas::before { content: "=EF=88=96"; }

.fa-firefox-browser::before { content: "=EE=80=87"; }

.fa-replyd::before { content: "=EF=8F=A6"; }

.fa-suse::before { content: "=EF=9F=96"; }

.fa-jenkins::before { content: "=EF=8E=B6"; }

.fa-twitter::before { content: "=EF=82=99"; }

.fa-rockrms::before { content: "=EF=8F=A9"; }

.fa-pinterest::before { content: "=EF=83=92"; }

.fa-buffer::before { content: "=EF=A0=B7"; }

.fa-npm::before { content: "=EF=8F=94"; }

.fa-yammer::before { content: "=EF=A1=80"; }

.fa-btc::before { content: "=EF=85=9A"; }

.fa-dribbble::before { content: "=EF=85=BD"; }

.fa-stumbleupon-circle::before { content: "=EF=86=A3"; }

.fa-internet-explorer::before { content: "=EF=89=AB"; }

.fa-stubber::before { content: "=EE=97=87"; }

.fa-telegram-plane::before, .fa-telegram::before { content: "=EF=8B=86"; }

.fa-old-republic::before { content: "=EF=94=90"; }

.fa-odysee::before { content: "=EE=97=86"; }

.fa-square-whatsapp::before, .fa-whatsapp-square::before { content: "=EF=90=
=8C"; }

.fa-node-js::before { content: "=EF=8F=93"; }

.fa-edge-legacy::before { content: "=EE=81=B8"; }

.fa-slack-hash::before, .fa-slack::before { content: "=EF=86=98"; }

.fa-medrt::before { content: "=EF=8F=88"; }

.fa-usb::before { content: "=EF=8A=87"; }

.fa-tumblr::before { content: "=EF=85=B3"; }

.fa-vaadin::before { content: "=EF=90=88"; }

.fa-quora::before { content: "=EF=8B=84"; }

.fa-reacteurope::before { content: "=EF=9D=9D"; }

.fa-medium-m::before, .fa-medium::before { content: "=EF=88=BA"; }

.fa-amilia::before { content: "=EF=8D=AD"; }

.fa-mixcloud::before { content: "=EF=8A=89"; }

.fa-flipboard::before { content: "=EF=91=8D"; }

.fa-viacoin::before { content: "=EF=88=B7"; }

.fa-critical-role::before { content: "=EF=9B=89"; }

.fa-sitrox::before { content: "=EE=91=8A"; }

.fa-discourse::before { content: "=EF=8E=93"; }

.fa-joomla::before { content: "=EF=86=AA"; }

.fa-mastodon::before { content: "=EF=93=B6"; }

.fa-airbnb::before { content: "=EF=A0=B4"; }

.fa-wolf-pack-battalion::before { content: "=EF=94=94"; }

.fa-buy-n-large::before { content: "=EF=A2=A6"; }

.fa-gulp::before { content: "=EF=8E=AE"; }

.fa-creative-commons-sampling-plus::before { content: "=EF=93=B1"; }

.fa-strava::before { content: "=EF=90=A8"; }

.fa-ember::before { content: "=EF=90=A3"; }

.fa-canadian-maple-leaf::before { content: "=EF=9E=85"; }

.fa-teamspeak::before { content: "=EF=93=B9"; }

.fa-pushed::before { content: "=EF=8F=A1"; }

.fa-wordpress-simple::before { content: "=EF=90=91"; }

.fa-nutritionix::before { content: "=EF=8F=96"; }

.fa-wodu::before { content: "=EE=82=88"; }

.fa-google-pay::before { content: "=EE=81=B9"; }

.fa-intercom::before { content: "=EF=9E=AF"; }

.fa-zhihu::before { content: "=EF=98=BF"; }

.fa-korvue::before { content: "=EF=90=AF"; }

.fa-pix::before { content: "=EE=90=BA"; }

.fa-steam-symbol::before { content: "=EF=8F=B6"; }

:host, :root { --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Free"; }

@font-face { font-family: "Font Awesome 6 Free"; font-style: normal; font-w=
eight: 400; font-display: block; src: url("../webfonts/fa-regular-400.woff2=
") format("woff2"), url("../webfonts/fa-regular-400.ttf") format("truetype"=
); }

.fa-regular, .far { font-weight: 400; }

:host, :root { --fa-style-family-classic: "Font Awesome 6 Free"; --fa-font-=
solid: normal 900 1em/1 "Font Awesome 6 Free"; }

@font-face { font-family: "Font Awesome 6 Free"; font-style: normal; font-w=
eight: 900; font-display: block; src: url("../webfonts/fa-solid-900.woff2")=
 format("woff2"), url("../webfonts/fa-solid-900.ttf") format("truetype"); }

.fa-solid, .fas { font-weight: 900; }

@font-face { font-family: "Font Awesome 5 Brands"; font-display: block; fon=
t-weight: 400; src: url("../webfonts/fa-brands-400.woff2") format("woff2"),=
 url("../webfonts/fa-brands-400.ttf") format("truetype"); }

@font-face { font-family: "Font Awesome 5 Free"; font-display: block; font-=
weight: 900; src: url("../webfonts/fa-solid-900.woff2") format("woff2"), ur=
l("../webfonts/fa-solid-900.ttf") format("truetype"); }

@font-face { font-family: "Font Awesome 5 Free"; font-display: block; font-=
weight: 400; src: url("../webfonts/fa-regular-400.woff2") format("woff2"), =
url("../webfonts/fa-regular-400.ttf") format("truetype"); }

@font-face { font-family: FontAwesome; font-display: block; src: url("../we=
bfonts/fa-solid-900.woff2") format("woff2"), url("../webfonts/fa-solid-900.=
ttf") format("truetype"); }

@font-face { font-family: FontAwesome; font-display: block; src: url("../we=
bfonts/fa-brands-400.woff2") format("woff2"), url("../webfonts/fa-brands-40=
0.ttf") format("truetype"); }

@font-face { font-family: FontAwesome; font-display: block; src: url("../we=
bfonts/fa-regular-400.woff2") format("woff2"), url("../webfonts/fa-regular-=
400.ttf") format("truetype"); unicode-range: U+F003, U+F006, U+F014, U+F016=
-F017, U+F01A-F01B, U+F01D, U+F022, U+F03E, U+F044, U+F046, U+F05C-F05D, U+=
F06E, U+F070, U+F087-F088, U+F08A, U+F094, U+F096-F097, U+F09D, U+F0A0, U+F=
0A2, U+F0A4-F0A7, U+F0C5, U+F0C7, U+F0E5-F0E6, U+F0EB, U+F0F6-F0F8, U+F10C,=
 U+F114-F115, U+F118-F11A, U+F11C-F11D, U+F133, U+F147, U+F14E, U+F150-F152=
, U+F185-F186, U+F18E, U+F190-F192, U+F196, U+F1C1-F1C9, U+F1D9, U+F1DB, U+=
F1E3, U+F1EA, U+F1F7, U+F1F9, U+F20A, U+F247-F248, U+F24A, U+F24D, U+F255-F=
25B, U+F25D, U+F271-F274, U+F278, U+F27B, U+F28C, U+F28E, U+F29C, U+F2B5, U=
+F2B7, U+F2BA, U+F2BC, U+F2BE, U+F2C0-F2C1, U+F2C3, U+F2D0, U+F2D2, U+F2D4,=
 U+F2DC; }

@font-face { font-family: FontAwesome; font-display: block; src: url("../we=
bfonts/fa-v4compatibility.woff2") format("woff2"), url("../webfonts/fa-v4co=
mpatibility.ttf") format("truetype"); unicode-range: U+F041, U+F047, U+F065=
-F066, U+F07D-F07E, U+F080, U+F08B, U+F08E, U+F090, U+F09A, U+F0AC, U+F0AE,=
 U+F0B2, U+F0D0, U+F0D6, U+F0E4, U+F0EC, U+F10A-F10B, U+F123, U+F13E, U+F14=
8-F149, U+F14C, U+F156, U+F15E, U+F160-F161, U+F163, U+F175-F178, U+F195, U=
+F1F8, U+F219, U+F27A; }
------MultipartBoundary--tCdgLTemwq0jS7q9jNLfyJgUywxjTTSi7GqPcLUxlP----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.analytics-dashboard { padding: 20px; }

.time-filter-section { background: white; padding: 20px; border-radius: 8px=
; margin-bottom: 20px; box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 8px; }

.filter-controls { display: flex; align-items: center; gap: 15px; }

.filter-controls label { font-weight: 600; color: rgb(73, 80, 87); }

.filter-controls select { padding: 8px 12px; border: 1px solid rgb(221, 221=
, 221); border-radius: 4px; font-size: 14px; }

.chart-section { background: white; border-radius: 8px; padding: 20px; marg=
in-bottom: 20px; box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 8px; }

.chart-header { margin-bottom: 20px; border-bottom: 1px solid rgb(233, 236,=
 239); padding-bottom: 15px; }

.chart-header h3 { color: rgb(33, 37, 41); font-size: 18px; margin-bottom: =
5px; display: flex; align-items: center; gap: 10px; }

.chart-header p { color: rgb(108, 117, 125); font-size: 14px; margin: 0px; =
}

.chart-container { position: relative; height: 200px; margin-bottom: 10px; =
}

.funnel-stats-grid { display: grid; grid-template-columns: repeat(auto-fit,=
 minmax(200px, 1fr)); gap: 15px; margin-top: 20px; }

.stat-item { text-align: center; padding: 15px; background: rgb(248, 249, 2=
50); border-radius: 8px; }

.stat-label { font-size: 12px; color: rgb(108, 117, 125); margin-bottom: 5p=
x; }

.stat-value { font-size: 24px; font-weight: bold; color: rgb(67, 97, 238); =
}

.target-cards { display: grid; grid-template-columns: repeat(auto-fit, minm=
ax(300px, 1fr)); gap: 20px; }

.target-card { background: white; border-radius: 8px; padding: 20px; border=
: 1px solid rgb(233, 236, 239); }

.target-header h4 { color: rgb(33, 37, 41); margin-bottom: 15px; display: f=
lex; align-items: center; gap: 10px; }

.target-progress { display: flex; align-items: center; gap: 20px; }

.progress-circle { width: 80px; height: 80px; border-radius: 50%; display: =
flex; align-items: center; justify-content: center; position: relative; }

.progress-text { text-align: center; }

.progress-text .percentage { display: block; font-size: 18px; font-weight: =
bold; color: rgb(33, 37, 41); }

.progress-text .label { font-size: 12px; color: rgb(108, 117, 125); }

.target-stats { flex: 1 1 0%; }

.target-stats .stat { display: flex; justify-content: space-between; margin=
-bottom: 8px; }

.target-stats .stat .label { color: rgb(108, 117, 125); font-size: 14px; }

.target-stats .stat .value { font-weight: 600; color: rgb(33, 37, 41); }

.analytics-row { margin-bottom: 20px; }

.analytics-module { background: white; border-radius: 6px; box-shadow: rgba=
(0, 0, 0, 0.1) 0px 1px 4px; overflow: hidden; height: 280px; display: flex;=
 flex-direction: column; }

.module-header { background: rgb(248, 249, 250); padding: 10px 15px; border=
-bottom: 1px solid rgb(233, 236, 239); display: flex; justify-content: spac=
e-between; align-items: center; flex-shrink: 0; }

.module-header h3 { color: rgb(33, 37, 41); font-size: 14px; margin: 0px; d=
isplay: flex; align-items: center; gap: 6px; font-weight: 600; }

.module-header h3 i { color: rgb(67, 97, 238); font-size: 12px; }

.time-selector select { padding: 3px 6px; border: 1px solid rgb(221, 221, 2=
21); border-radius: 3px; font-size: 11px; background: white; }

.module-content { padding: 15px; flex: 1 1 0%; overflow: hidden; }

.funnel-container { display: flex; flex-direction: column; align-items: cen=
ter; gap: 6px; padding: 10px 0px; }

.funnel-stage { background: rgb(108, 117, 125); color: white; text-align: c=
enter; padding: 6px 12px; position: relative; font-size: 11px; font-weight:=
 500; border-radius: 2px; box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px; }

.funnel-stage.stage-1 { width: 90%; background: rgb(108, 117, 125); }

.funnel-stage.stage-2 { width: 75%; background: rgb(108, 117, 125); }

.funnel-stage.stage-3 { width: 60%; background: rgb(108, 117, 125); }

.funnel-stage.stage-4 { width: 45%; background: rgb(108, 117, 125); }

.funnel-stage.stage-5 { width: 30%; background: rgb(108, 117, 125); }

.funnel-stats { margin-top: 8px; font-size: 10px; color: rgb(108, 117, 125)=
; text-align: center; }

@media (max-width: 768px) {
  .filter-controls { flex-direction: column; align-items: stretch; }
  .chart-container { height: 300px; }
  .target-progress { flex-direction: column; text-align: center; }
  .analytics-row { grid-template-columns: 1fr !important; }
}

.logo i { font-size: 28px; }

.logo h1 { font-size: 22px; font-weight: 600; }

.user-info { display: flex; align-items: center; gap: 12px; }

.user-avatar { width: 42px; height: 42px; background: rgba(255, 255, 255, 0=
.2); border-radius: 50%; display: flex; align-items: center; justify-conten=
t: center; font-weight: bold; font-size: 18px; }

.main-content { display: flex; flex: 1 1 0%; }

.sidebar { width: 260px; background: white; padding: 25px 20px; display: fl=
ex; flex-direction: column; box-shadow: var(--shadow); z-index: 10; }

.nav-menu { list-style: none; margin-top: 20px; }

.nav-menu li { margin-bottom: 8px; }

.nav-menu a { display: flex; align-items: center; padding: 12px 15px; text-=
decoration: none; color: var(--dark); border-radius: 8px; transition: 0.3s;=
 font-size: 15px; font-weight: 500; }

.nav-menu a i { margin-right: 12px; width: 20px; text-align: center; color:=
 var(--gray); }

.nav-menu a:hover, .nav-menu a.active { background: rgba(67, 97, 238, 0.1);=
 color: var(--primary); }

.nav-menu a.active i { color: var(--primary); }

.nav-menu a:hover i { color: var(--primary); }

.content-area { flex: 1 1 0%; padding: 30px; overflow-y: auto; }

.module-header { margin-bottom: 30px; display: flex; justify-content: space=
-between; align-items: center; }

.module-header h1 { font-size: 26px; font-weight: 700; color: var(--dark); =
display: flex; align-items: center; gap: 12px; }

.module-header h1 i { color: var(--primary); }

.dashboard-grid { display: grid; grid-template-columns: repeat(auto-fit, mi=
nmax(300px, 1fr)); gap: 25px; margin-bottom: 30px; }

.card { background: white; border-radius: var(--border-radius); box-shadow:=
 var(--shadow); overflow: hidden; }

.card-header { padding: 20px 25px; border-bottom: 1px solid var(--light-gra=
y); display: flex; justify-content: space-between; align-items: center; }

.card-header h2 { font-size: 18px; font-weight: 600; display: flex; align-i=
tems: center; gap: 10px; }

.card-header h2 i { color: var(--primary); }

.card-body { padding: 25px; }

.funnel-overview { display: grid; grid-template-columns: repeat(4, 1fr); ga=
p: 15px; }

.funnel-step { text-align: center; padding: 15px; border-radius: 8px; backg=
round: rgba(67, 97, 238, 0.05); transition: transform 0.3s; }

.funnel-step:hover { transform: translateY(-5px); background: rgba(67, 97, =
238, 0.1); }

.funnel-step h3 { font-size: 14px; color: var(--gray); margin-bottom: 10px;=
 }

.funnel-step .number { font-size: 32px; font-weight: 700; color: var(--prim=
ary); margin-bottom: 5px; }

.funnel-step .conversion { font-size: 14px; background: rgba(76, 201, 240, =
0.2); color: var(--info); padding: 4px 8px; border-radius: 20px; display: i=
nline-block; }

.chart-container { padding: 15px; height: 300px; position: relative; }

.data-management { display: grid; grid-template-columns: repeat(2, 1fr); ga=
p: 20px; }

.data-card { padding: 20px; border-radius: 8px; background: rgba(67, 97, 23=
8, 0.05); display: flex; flex-direction: column; }

.data-card h3 { font-size: 15px; color: var(--gray); margin-bottom: 15px; d=
isplay: flex; align-items: center; gap: 8px; }

.data-card h3 i { width: 24px; height: 24px; display: flex; align-items: ce=
nter; justify-content: center; background: rgba(67, 97, 238, 0.1); color: v=
ar(--primary); border-radius: 6px; }

.data-card .number { font-size: 28px; font-weight: 700; color: var(--primar=
y); margin-bottom: 10px; }

.data-card .info { font-size: 14px; color: var(--gray); margin-top: auto; p=
adding: 8px 12px; background: rgba(255, 255, 255, 0.6); border-radius: 6px;=
 display: flex; align-items: center; gap: 8px; }

.data-card .info i { color: var(--success); }

.progress-bar { height: 8px; background: var(--light-gray); border-radius: =
4px; overflow: hidden; margin-top: 5px; }

.progress { height: 100%; background: var(--success); border-radius: 4px; }

.notification { position: fixed; bottom: 30px; right: 30px; background: whi=
te; border-radius: var(--border-radius); box-shadow: var(--shadow); padding=
: 18px 22px; display: flex; align-items: center; gap: 15px; transform: tran=
slateX(150%); transition: transform 0.4s; z-index: 1000; border-left: 4px s=
olid var(--primary); }

.notification.show { transform: translateX(0px); }

.notification i { font-size: 24px; color: var(--primary); }

.btn { padding: 10px 20px; border-radius: 6px; font-weight: 500; cursor: po=
inter; border: none; transition: 0.3s; display: inline-flex; align-items: c=
enter; gap: 8px; font-size: 14px; }

.btn-primary { background: var(--primary); color: white; }

.btn-primary:hover { background: var(--secondary); }

.btn-secondary { background: white; color: var(--primary); border: 1px soli=
d var(--primary); }

.btn-secondary:hover { background: rgba(67, 97, 238, 0.1); }

.form-select { padding: 8px 12px; border: 1px solid var(--light-gray); bord=
er-radius: 6px; background: white; font-size: 14px; }

@media (max-width: 1200px) {
  .funnel-overview { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 768px) {
  .main-content { flex-direction: column; }
  .sidebar { width: 100%; padding: 15px; }
  .dashboard-grid { grid-template-columns: 1fr; }
  .funnel-overview { grid-template-columns: 1fr; }
  .data-management { grid-template-columns: 1fr; }
}
------MultipartBoundary--tCdgLTemwq0jS7q9jNLfyJgUywxjTTSi7GqPcLUxlP------
