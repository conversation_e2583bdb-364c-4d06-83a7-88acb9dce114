我是一位从事汽车经销商的经营管理者，我制作了一个《汽车销售管理系统》，现在我要开发一个与之配套的财务核算模块，功能是：可以每月设置商务政策，实现销售利润统计，现在我需先制作一个核算商务政策返利的系统。请协助我生成并完善。

表格1.xlsx，为商务政策。
单元格D1:P1为大项，大项：进销差、LCP、季度、月度，南区，名称可自定义。
单元格D2:P2为小项，名称可自定义。
大项：进销差，小项：5.5%。
大项：LCP，小项：5.6%
大项：季度，小项：重购/置换、蓝海计划、全损置换、林肯POR、零售达标、人员奖励、全景影像
大项：月度，小项：零售现金、保险补贴、Z零售达标、长库龄补贴
大项：南区，小项：8.18活动、8.18系数

小项需具备以下功能项：1、可自主设置生效的起止日期。2、属性设置：固定（实际计算）、达成率（需根据达成率，生成对应的数据）、条件（提醒）

我是一位汽车经销商的经营管理者，已经开发了一个《汽车销售管理系统》。现在需要开发一个配套的财务核算模块，专门用于商务政策返利核算。

**项目目标：**
开发一个商务政策返利核算系统，能够：
1. 每月设置和管理商务政策
2. 实现销售利润统计
3. 自动计算各项返利金额

**核心功能需求：**

**1. 商务政策配置表格（表格1.xlsx）**
- 表格结构：
  - D1:P1行：大项分类（可自定义名称）
    - 进销差
    - LCP  
    - 季度
    - 月度
    - 南区
  - D2:P2行：小项分类（可自定义名称）

**2. 具体政策项目配置：**
- 进销差 → 小项：5.5%
- LCP → 小项：5.6%
- 季度 → 小项：重购/置换、蓝海计划、全损置换、林肯POR、零售达标、人员奖励、全景影像
- 月度 → 小项：零售现金、保险补贴、Z零售达标、长库龄补贴
- 南区 → 小项：8.18活动、8.18系数

**3. 每个小项必须具备的功能：**
- **生效期管理：** 可设置起始日期和结束日期
- **属性类型设置：**
  - 固定类型：按实际金额计算
  - 达成率类型：根据达成率百分比生成对应的返利数据
  - 条件类型：设置提醒条件和触发规则

**4. 技术要求：**
- 支持Excel文件导入/导出
- 提供用户友好的配置界面
- 能够与现有销售管理系统集成
- 支持数据统计和报表生成

请协助我设计并实现这个商务政策返利核算系统的完整解决方案，包括数据结构设计、核心功能实现和用户界面。
