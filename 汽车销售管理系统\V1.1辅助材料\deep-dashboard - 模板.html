<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>控制面板 - 汽车销售漏斗管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #4361ee;
            --secondary: #3f37c9;
            --success: #4cc9f0;
            --info: #4895ef;
            --warning: #f72585;
            --danger: #e63946;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --border-radius: 10px;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #f5f7fb;
            color: #333;
            line-height: 1.6;
        }

        .container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo i {
            font-size: 28px;
        }

        .logo h1 {
            font-size: 22px;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 42px;
            height: 42px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }

        .main-content {
            display: flex;
            flex: 1;
        }

        .sidebar {
            width: 260px;
            background: white;
            padding: 25px 20px;
            display: flex;
            flex-direction: column;
            box-shadow: var(--shadow);
            z-index: 10;
        }

        .nav-menu {
            list-style: none;
            margin-top: 20px;
        }

        .nav-menu li {
            margin-bottom: 8px;
        }

        .nav-menu a {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            text-decoration: none;
            color: var(--dark);
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 15px;
            font-weight: 500;
        }

        .nav-menu a i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
            color: var(--gray);
        }

        .nav-menu a:hover, .nav-menu a.active {
            background: rgba(67, 97, 238, 0.1);
            color: var(--primary);
        }

        .nav-menu a.active i {
            color: var(--primary);
        }

        .nav-menu a:hover i {
            color: var(--primary);
        }

        .content-area {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .module-header {
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .module-header h1 {
            font-size: 26px;
            font-weight: 700;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .module-header h1 i {
            color: var(--primary);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .card-header {
            padding: 20px 25px;
            border-bottom: 1px solid var(--light-gray);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-header h2 i {
            color: var(--primary);
        }

        .card-body {
            padding: 25px;
        }

        .funnel-overview {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .funnel-step {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: rgba(67, 97, 238, 0.05);
            transition: transform 0.3s ease;
        }

        .funnel-step:hover {
            transform: translateY(-5px);
            background: rgba(67, 97, 238, 0.1);
        }

        .funnel-step h3 {
            font-size: 14px;
            color: var(--gray);
            margin-bottom: 10px;
        }

        .funnel-step .number {
            font-size: 32px;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .funnel-step .conversion {
            font-size: 14px;
            background: rgba(76, 201, 240, 0.2);
            color: var(--info);
            padding: 4px 8px;
            border-radius: 20px;
            display: inline-block;
        }

        .chart-container {
            padding: 15px;
            height: 300px;
            position: relative;
        }

        .data-management {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .data-card {
            padding: 20px;
            border-radius: 8px;
            background: rgba(67, 97, 238, 0.05);
            display: flex;
            flex-direction: column;
        }

        .data-card h3 {
            font-size: 15px;
            color: var(--gray);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .data-card h3 i {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(67, 97, 238, 0.1);
            color: var(--primary);
            border-radius: 6px;
        }

        .data-card .number {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 10px;
        }

        .data-card .info {
            font-size: 14px;
            color: var(--gray);
            margin-top: auto;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .data-card .info i {
            color: var(--success);
        }

        .progress-bar {
            height: 8px;
            background: var(--light-gray);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress {
            height: 100%;
            background: var(--success);
            border-radius: 4px;
        }

        .notification {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 18px 22px;
            display: flex;
            align-items: center;
            gap: 15px;
            transform: translateX(150%);
            transition: transform 0.4s ease;
            z-index: 1000;
            border-left: 4px solid var(--primary);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification i {
            font-size: 24px;
            color: var(--primary);
        }

        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary);
        }

        .btn-secondary {
            background: white;
            color: var(--primary);
            border: 1px solid var(--primary);
        }

        .btn-secondary:hover {
            background: rgba(67, 97, 238, 0.1);
        }

        .form-select {
            padding: 8px 12px;
            border: 1px solid var(--light-gray);
            border-radius: 6px;
            background: white;
            font-size: 14px;
        }

        /* Responsive design */
        @media (max-width: 1200px) {
            .funnel-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
                padding: 15px;
            }
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            .funnel-overview {
                grid-template-columns: 1fr;
            }
            .data-management {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <i class="fas fa-tachometer-alt"></i>
                <h1>控制面板</h1>
            </div>
            <div class="user-info">
                <div class="user-avatar">张</div>
                <div>
                    <div>张经理</div>
                    <div style="font-size: 13px; opacity: 0.8;">销售主管</div>
                </div>
            </div>
        </header>

        <div class="main-content">
            <div class="sidebar">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 13px; color: #7f8c8d; margin-bottom: 5px;">今日数据</div>
                    <div style="font-size: 28px; font-weight: 700; color: var(--secondary);" id="today-count">9</div>
                    <div style="font-size: 14px; color: var(--gray);">到店客户</div>
                </div>

                <ul class="nav-menu" id="main-menu">
                    <li><a href="index.html"><i class="fas fa-home"></i> 首页</a></li>
                    <li><a href="dashboard.html" class="active"><i class="fas fa-tachometer-alt"></i> 控制面板</a></li>
                    <li><a href="customers.html"><i class="fas fa-users"></i> 客户管理</a></li>
                    <li><a href="testDrives.html"><i class="fas fa-car-side"></i> 试驾管理</a></li>
                    <li><a href="quotes.html"><i class="fas fa-file-invoice-dollar"></i> 报价管理</a></li>
                    <li><a href="deals.html"><i class="fas fa-trophy"></i> 成交管理</a></li>
                    <li><a href="analytics.html"><i class="fas fa-chart-line"></i> 销售分析</a></li>
                    <li><a href="settings.html"><i class="fas fa-cog"></i> 系统设置</a></li>
                </ul>

                <div style="margin-top: 30px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h3 style="font-size: 15px; margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-database" style="color: var(--secondary);"></i> 数据库状态
                    </h3>
                    <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 8px;">
                        <span>已使用空间:</span>
                        <span id="db-usage">24.5 MB / 500 MB</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress" id="db-progress" style="width: 15%"></div>
                    </div>
                    <div style="font-size: 12px; color: var(--gray); margin-top: 5px; text-align: center;">
                        上次同步: <span id="last-sync">14:30</span>
                    </div>
                </div>
            </div>

            <div class="content-area">
                <div class="module-container active" id="dashboard-module">
                    <div class="module-header">
                        <h1><i class="fas fa-tachometer-alt"></i> 销售控制面板</h1>
                        <button class="btn btn-primary">
                            <i class="fas fa-chart-line"></i> 查看销售分析
                        </button>
                    </div>
                    
                    <!-- 销售漏斗概览 -->
                    <div class="dashboard-grid">
                        <div class="card">
                            <div class="card-header">
                                <h2><i class="fas fa-filter"></i> 销售漏斗概览</h2>
                                <div style="font-size: 14px; color: var(--gray);">本月数据</div>
                            </div>
                            <div class="card-body">
                                <div class="funnel-overview">
                                    <div class="funnel-step">
                                        <h3>到店客户</h3>
                                        <div class="number">42</div>
                                        <div class="conversion">67% 转化率</div>
                                    </div>
                                    <div class="funnel-step">
                                        <h3>试驾客户</h3>
                                        <div class="number">28</div>
                                        <div class="conversion">68% 转化率</div>
                                    </div>
                                    <div class="funnel-step">
                                        <h3>报价客户</h3>
                                        <div class="number">18</div>
                                        <div class="conversion">64% 转化率</div>
                                    </div>
                                    <div class="funnel-step">
                                        <h3>成交客户</h3>
                                        <div class="number">12</div>
                                        <div class="conversion">67% 转化率</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 月度销售趋势 -->
                        <div class="card">
                            <div class="card-header">
                                <h2><i class="fas fa-chart-line"></i> 月度销售趋势</h2>
                                <div>
                                    <select class="form-select">
                                        <option>本月</option>
                                        <option>上月</option>
                                        <option>本季度</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="salesTrendChart"></canvas>
                            </div>
                        </div>
                        
                        <!-- 销售顾问排名 -->
                        <div class="card">
                            <div class="card-header">
                                <h2><i class="fas fa-trophy"></i> 销售顾问排名</h2>
                                <div style="font-size: 14px; color: var(--gray);">本月成交</div>
                            </div>
                            <div class="chart-container">
                                <canvas id="advisorRankingChart"></canvas>
                            </div>
                        </div>
                        
                        <!-- 数据管理 -->
                        <div class="card">
                            <div class="card-header">
                                <h2><i class="fas fa-database"></i> 数据管理</h2>
                                <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 13px;">
                                    <i class="fas fa-plus"></i> 添加新客户
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="data-management">
                                    <div class="data-card">
                                        <h3><i class="fas fa-user-clock"></i> 待跟进客户</h3>
                                        <div class="number">8</div>
                                        <div class="info">
                                            <i class="fas fa-arrow-up"></i> 比昨天增加2位
                                        </div>
                                    </div>
                                    <div class="data-card">
                                        <h3><i class="fas fa-car"></i> 试驾安排</h3>
                                        <div class="number">5</div>
                                        <div class="info">
                                            <i class="fas fa-calendar"></i> 明天有3个试驾
                                        </div>
                                    </div>
                                    <div class="data-card">
                                        <h3><i class="fas fa-trophy"></i> 本月成交</h3>
                                        <div class="number">12</div>
                                        <div class="info">
                                            <i class="fas fa-chart-line"></i> 达成率80%
                                        </div>
                                    </div>
                                    <div class="data-card">
                                        <h3><i class="fas fa-user-slash"></i> 流失客户</h3>
                                        <div class="number">6</div>
                                        <div class="info">
                                            <i class="fas fa-search"></i> 需分析流失原因
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="notification info show">
        <i class="fas fa-tachometer-alt"></i>
        <div>
            <div style="font-weight: 600;">控制面板已加载</div>
            <div style="font-size: 14px; margin-top: 5px;">销售数据已准备就绪</div>
        </div>
    </div>

    <script>
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 月度销售趋势图
            const salesTrendCtx = document.getElementById('salesTrendChart').getContext('2d');
            const salesTrendChart = new Chart(salesTrendCtx, {
                type: 'line',
                data: {
                    labels: ['1日', '5日', '10日', '15日', '20日', '25日', '30日'],
                    datasets: [
                        {
                            label: '到店客户',
                            data: [5, 8, 6, 10, 7, 4, 2],
                            borderColor: '#4361ee',
                            backgroundColor: 'rgba(67, 97, 238, 0.1)',
                            borderWidth: 3,
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '成交客户',
                            data: [2, 3, 1, 4, 2, 3, 1],
                            borderColor: '#4cc9f0',
                            backgroundColor: 'rgba(76, 201, 240, 0.1)',
                            borderWidth: 3,
                            tension: 0.3,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                drawBorder: false
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // 销售顾问排名图
            const advisorRankingCtx = document.getElementById('advisorRankingChart').getContext('2d');
            const advisorRankingChart = new Chart(advisorRankingCtx, {
                type: 'bar',
                data: {
                    labels: ['李静善', '张静善', '王静善', '刘静善', '陈静善'],
                    datasets: [{
                        label: '成交数量',
                        data: [4.5, 4.0, 3.5, 3.0, 2.5],
                        backgroundColor: [
                            'rgba(67, 97, 238, 0.8)',
                            'rgba(67, 97, 238, 0.7)',
                            'rgba(67, 97, 238, 0.6)',
                            'rgba(67, 97, 238, 0.5)',
                            'rgba(67, 97, 238, 0.4)'
                        ],
                        borderColor: [
                            'rgba(67, 97, 238, 1)',
                            'rgba(67, 97, 238, 1)',
                            'rgba(67, 97, 238, 1)',
                            'rgba(67, 97, 238, 1)',
                            'rgba(67, 97, 238, 1)'
                        ],
                        borderWidth: 1,
                        borderRadius: 6
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                drawBorder: false
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // 隐藏通知
            setTimeout(() => {
                document.querySelector('.notification').classList.remove('show');
            }, 3000);
        });
    </script>
</body>
</html>