// 数据分析功能模块
window.analyticsModule = {
    // 缓存数据
    showroomData: [],
    leadsData: [],
    ordersData: [],
    currentAnalysisType: 'customer-flow',

    // 统一日期格式化函数 - YYYY/M/DD
    formatDate: function(dateString) {
        if (!dateString) return '';
        
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;
            
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            
            return `${year}/${month}/${day}`;
        } catch (error) {
            return dateString;
        }
    },

    // 初始化数据分析模块
    init: async function() {
        await this.loadAllData();
        this.bindEvents();
    },

    // 加载所有数据
    loadAllData: async function() {
        try {
            this.showroomData = await window.dbFunctions.getAllShowroomEntries() || [];
            this.leadsData = await window.dbFunctions.getAllLeadEntries() || [];
            this.ordersData = await window.dbFunctions.getAllOrderManagement() || [];
        } catch (error) {
            console.error('加载数据失败:', error);
        }
    },

    // 绑定事件
    bindEvents: function() {
        // 分析类型切换事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('.analysis-tab')) {
                const analysisType = e.target.dataset.analysis;
                this.switchAnalysis(analysisType);
            }
        });
    },

    // 渲染数据分析主界面
    render: function() {
        return `
            <div class="analytics-container">
                <div class="analytics-header">
                    <h2><i class="fas fa-chart-bar"></i> 数据分析</h2>
                    <div class="analysis-tabs">
                        <button class="analysis-tab ${this.currentAnalysisType === 'customer-flow' ? 'active' : ''}" data-analysis="customer-flow">
                            <i class="fas fa-users"></i> 客流分析
                        </button>
                        <button class="analysis-tab ${this.currentAnalysisType === 'vehicle-analysis' ? 'active' : ''}" data-analysis="vehicle-analysis">
                            <i class="fas fa-car"></i> 车型分析
                        </button>
                        <button class="analysis-tab ${this.currentAnalysisType === 'order-ranking' ? 'active' : ''}" data-analysis="order-ranking">
                            <i class="fas fa-trophy"></i> 订单排名
                        </button>
                        <button class="analysis-tab ${this.currentAnalysisType === 'sales-process' ? 'active' : ''}" data-analysis="sales-process">
                            <i class="fas fa-chart-line"></i> 销售过程
                        </button>
                        <button class="analysis-tab ${this.currentAnalysisType === 'sales-funnel' ? 'active' : ''}" data-analysis="sales-funnel">
                            <i class="fas fa-filter"></i> 销售漏斗
                        </button>
                        <button class="analysis-tab ${this.currentAnalysisType === 'leads-funnel' ? 'active' : ''}" data-analysis="leads-funnel">
                            <i class="fas fa-funnel-dollar"></i> 线索漏斗
                        </button>
                        <button class="analysis-tab ${this.currentAnalysisType === 'channel-analysis' ? 'active' : ''}" data-analysis="channel-analysis">
                            <i class="fas fa-share-alt"></i> 渠道分析
                        </button>
                        <button class="analysis-tab ${this.currentAnalysisType === 'trend-analysis' ? 'active' : ''}" data-analysis="trend-analysis">
                            <i class="fas fa-chart-area"></i> 趋势分析
                        </button>
                    </div>
                </div>
                
                <div class="analytics-content">
                    ${this.renderAnalysisContent()}
                </div>
            </div>
        `;
    },

    // 切换分析类型
    switchAnalysis: function(analysisType) {
        this.currentAnalysisType = analysisType;
        
        // 更新标签状态
        document.querySelectorAll('.analysis-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-analysis="${analysisType}"]`).classList.add('active');
        
        // 更新内容
        const contentContainer = document.querySelector('.analytics-content');
        if (contentContainer) {
            contentContainer.innerHTML = this.renderAnalysisContent();
        }
    },

    // 渲染分析内容
    renderAnalysisContent: function() {
        switch (this.currentAnalysisType) {
            case 'customer-flow':
                return this.renderCustomerFlowAnalysis();
            case 'vehicle-analysis':
                return this.renderVehicleAnalysis();
            case 'order-ranking':
                return this.renderOrderRanking();
            case 'sales-process':
                return this.renderSalesProcessAnalysis();
            case 'sales-funnel':
                return this.renderSalesFunnelAnalysis();
            case 'leads-funnel':
                return this.renderLeadsFunnelAnalysis();
            case 'channel-analysis':
                return this.renderChannelAnalysis();
            case 'trend-analysis':
                return this.renderTrendAnalysis();
            default:
                return '<div class="empty-state">请选择分析类型</div>';
        }
    },

    // 1. 首次再次客流分析
    renderCustomerFlowAnalysis: function() {
        const today = new Date();
        const currentMonth = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;
        const currentYear = today.getFullYear();

        // 计算客流数据
        const todayData = this.calculateCustomerFlow('today');
        const monthData = this.calculateCustomerFlow('month');
        const yearData = this.calculateCustomerFlow('year');
        const lastMonthData = this.calculateCustomerFlow('lastMonth');

        // 计算环比
        const monthComparison = this.calculateComparison(monthData, lastMonthData);

        return `
            <div class="customer-flow-analysis">
                <div class="analysis-filters">
                    <h3>首次再次客流分析</h3>
                    <div class="filter-controls">
                        <label>
                            <span>日期筛选：</span>
                            <input type="month" id="flow-month-filter" value="${currentMonth}" onchange="analyticsModule.updateCustomerFlowFilter()">
                        </label>
                        <button class="btn btn-primary" onclick="analyticsModule.refreshCustomerFlow()">刷新数据</button>
                    </div>
                </div>

                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-header">
                            <h4>当日客流</h4>
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">${todayData.firstTime}</div>
                            <div class="metric-label">首次客流</div>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">${todayData.firstInvite}</div>
                            <div class="metric-label">首次邀约客流</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-header">
                            <h4>当月客流</h4>
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">${monthData.firstTime}</div>
                            <div class="metric-label">首次客流</div>
                            <div class="metric-comparison ${monthComparison.firstTime.trend}">${monthComparison.firstTime.text}</div>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">${monthData.firstInvite}</div>
                            <div class="metric-label">首次邀约客流</div>
                            <div class="metric-comparison ${monthComparison.firstInvite.trend}">${monthComparison.firstInvite.text}</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-header">
                            <h4>全年累计</h4>
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">${yearData.firstTime}</div>
                            <div class="metric-label">首次客流</div>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">${yearData.firstInvite}</div>
                            <div class="metric-label">首次邀约客流</div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <canvas id="customer-flow-chart" width="800" height="400"></canvas>
                </div>
            </div>
        `;
    },

    // 计算客流数据
    calculateCustomerFlow: function(period) {
        const today = new Date();
        let startDate, endDate;

        switch (period) {
            case 'today':
                startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
                endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
                break;
            case 'month':
                startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                endDate = new Date(today.getFullYear(), today.getMonth() + 1, 1);
                break;
            case 'lastMonth':
                startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                endDate = new Date(today.getFullYear(), today.getMonth(), 1);
                break;
            case 'year':
                startDate = new Date(today.getFullYear(), 0, 1);
                endDate = new Date(today.getFullYear() + 1, 0, 1);
                break;
        }

        const filteredData = this.showroomData.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate >= startDate && entryDate < endDate;
        });

        // 首次客流：FU、FUD
        const firstTime = filteredData.filter(entry => 
            entry.visitType === 'FU' || entry.visitType === 'FUD'
        ).length;

        // 首次邀约客流：FUI、FUID
        const firstInvite = filteredData.filter(entry => 
            entry.visitType === 'FUI' || entry.visitType === 'FUID'
        ).length;

        return { firstTime, firstInvite };
    },

    // 计算环比
    calculateComparison: function(current, previous) {
        const calculateChange = (curr, prev) => {
            if (prev === 0) return { text: '新增', trend: 'up' };
            const change = ((curr - prev) / prev * 100).toFixed(1);
            const trend = change > 0 ? 'up' : change < 0 ? 'down' : 'stable';
            const text = `${change > 0 ? '+' : ''}${change}%`;
            return { text, trend };
        };

        return {
            firstTime: calculateChange(current.firstTime, previous.firstTime),
            firstInvite: calculateChange(current.firstInvite, previous.firstInvite)
        };
    },

    // 更新客流筛选
    updateCustomerFlowFilter: function() {
        // 重新渲染客流分析
        this.switchAnalysis('customer-flow');
    },

    // 刷新客流数据
    refreshCustomerFlow: async function() {
        await this.loadAllData();
        this.switchAnalysis('customer-flow');
    },

    // 2. 车型分析
    renderVehicleAnalysis: function() {
        const today = new Date();
        const currentMonth = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;

        // 计算车型意向分析
        const todayIntention = this.calculateVehicleIntention('today');
        const monthIntention = this.calculateVehicleIntention('month');
        const yearIntention = this.calculateVehicleIntention('year');

        // 计算试驾率分析
        const todayTestDrive = this.calculateTestDriveRate('today');
        const monthTestDrive = this.calculateTestDriveRate('month');
        const yearTestDrive = this.calculateTestDriveRate('year');

        return `
            <div class="vehicle-analysis">
                <div class="analysis-filters">
                    <h3>车型分析</h3>
                    <div class="filter-controls">
                        <label>
                            <span>日期筛选：</span>
                            <input type="month" id="vehicle-month-filter" value="${currentMonth}" onchange="analyticsModule.updateVehicleFilter()">
                        </label>
                        <button class="btn btn-primary" onclick="analyticsModule.refreshVehicleAnalysis()">刷新数据</button>
                    </div>
                </div>

                <div class="analysis-sections">
                    <div class="section-card">
                        <h4><i class="fas fa-heart"></i> 车型意向分析</h4>
                        <div class="metrics-row">
                            <div class="metric-item">
                                <div class="metric-label">当日</div>
                                <div class="metric-value">${todayIntention.total}</div>
                                <div class="metric-detail">意向客户</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">当月</div>
                                <div class="metric-value">${monthIntention.total}</div>
                                <div class="metric-detail">意向客户</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">全年</div>
                                <div class="metric-value">${yearIntention.total}</div>
                                <div class="metric-detail">意向客户</div>
                            </div>
                        </div>
                        <div class="vehicle-breakdown">
                            ${this.renderVehicleBreakdown(monthIntention.breakdown)}
                        </div>
                    </div>

                    <div class="section-card">
                        <h4><i class="fas fa-car-side"></i> 试驾率分析</h4>
                        <div class="metrics-row">
                            <div class="metric-item">
                                <div class="metric-label">当日</div>
                                <div class="metric-value">${todayTestDrive.rate}%</div>
                                <div class="metric-detail">${todayTestDrive.testDriveCount}/${todayTestDrive.totalCount}</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">当月</div>
                                <div class="metric-value">${monthTestDrive.rate}%</div>
                                <div class="metric-detail">${monthTestDrive.testDriveCount}/${monthTestDrive.totalCount}</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">全年</div>
                                <div class="metric-value">${yearTestDrive.rate}%</div>
                                <div class="metric-detail">${yearTestDrive.testDriveCount}/${yearTestDrive.totalCount}</div>
                            </div>
                        </div>
                        <div class="test-drive-chart">
                            <canvas id="test-drive-chart" width="600" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // 计算车型意向分析
    calculateVehicleIntention: function(period) {
        const filteredData = this.getFilteredDataByPeriod(period);

        // 统计来店类型为FU、FUI、FUD、FUID、BB、BBI、BBD、BBID、保有的数量
        const validVisitTypes = ['FU', 'FUI', 'FUD', 'FUID', 'BB', 'BBI', 'BBD', 'BBID', '保有'];
        const intentionData = filteredData.filter(entry =>
            validVisitTypes.includes(entry.visitType)
        );

        // 按车型分组统计
        const breakdown = {};
        intentionData.forEach(entry => {
            const model = entry.intendedModels || '未知';
            breakdown[model] = (breakdown[model] || 0) + 1;
        });

        return {
            total: intentionData.length,
            breakdown: breakdown
        };
    },

    // 计算试驾率分析
    calculateTestDriveRate: function(period) {
        const filteredData = this.getFilteredDataByPeriod(period);

        // 统计来店类型为FU、FUI、FUD、FUID、BB、BBI、BBD、BBID、保有的数量
        const validVisitTypes = ['FU', 'FUI', 'FUD', 'FUID', 'BB', 'BBI', 'BBD', 'BBID', '保有'];
        const totalCustomers = filteredData.filter(entry =>
            validVisitTypes.includes(entry.visitType)
        );

        // 统计有试驾记录的客户
        const testDriveCustomers = totalCustomers.filter(entry =>
            entry.testDrive === '是' || entry.testDrive === true
        );

        const rate = totalCustomers.length > 0 ?
            ((testDriveCustomers.length / totalCustomers.length) * 100).toFixed(1) : 0;

        return {
            rate: rate,
            testDriveCount: testDriveCustomers.length,
            totalCount: totalCustomers.length
        };
    },

    // 渲染车型分解
    renderVehicleBreakdown: function(breakdown) {
        const sortedModels = Object.entries(breakdown)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5); // 显示前5名

        return `
            <div class="vehicle-list">
                ${sortedModels.map(([model, count], index) => `
                    <div class="vehicle-item">
                        <div class="vehicle-rank">${index + 1}</div>
                        <div class="vehicle-name">${model}</div>
                        <div class="vehicle-count">${count}</div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    // 根据时间段过滤数据
    getFilteredDataByPeriod: function(period) {
        const today = new Date();
        let startDate, endDate;

        switch (period) {
            case 'today':
                startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
                endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
                break;
            case 'month':
                startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                endDate = new Date(today.getFullYear(), today.getMonth() + 1, 1);
                break;
            case 'year':
                startDate = new Date(today.getFullYear(), 0, 1);
                endDate = new Date(today.getFullYear() + 1, 0, 1);
                break;
        }

        return this.showroomData.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate >= startDate && entryDate < endDate;
        });
    },

    // 更新车型筛选
    updateVehicleFilter: function() {
        this.switchAnalysis('vehicle-analysis');
    },

    // 刷新车型分析
    refreshVehicleAnalysis: async function() {
        await this.loadAllData();
        this.switchAnalysis('vehicle-analysis');
    },

    // 3. 订单排名分析
    renderOrderRanking: function() {
        const today = new Date();
        const currentMonth = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;
        const currentYear = today.getFullYear();

        // 计算销售顾问订单统计
        const orderRanking = this.calculateOrderRanking(currentMonth);

        // 计算销售顾问交车统计
        const deliveryRanking = this.calculateDeliveryRanking(currentMonth);

        return `
            <div class="order-ranking-analysis">
                <div class="analysis-filters">
                    <h3>订单排名分析</h3>
                    <div class="filter-controls">
                        <label>
                            <span>月份筛选：</span>
                            <input type="month" id="ranking-month-filter" value="${currentMonth}" onchange="analyticsModule.updateRankingFilter()">
                        </label>
                        <label>
                            <span>年份筛选：</span>
                            <select id="ranking-year-filter" onchange="analyticsModule.updateRankingFilter()">
                                <option value="${currentYear}">${currentYear}年</option>
                                <option value="${currentYear - 1}">${currentYear - 1}年</option>
                                <option value="${currentYear - 2}">${currentYear - 2}年</option>
                            </select>
                        </label>
                        <button class="btn btn-primary" onclick="analyticsModule.refreshOrderRanking()">刷新数据</button>
                    </div>
                </div>

                <div class="ranking-sections">
                    <div class="section-card">
                        <h4><i class="fas fa-file-contract"></i> 销售顾问订单统计</h4>
                        <div class="ranking-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>销售顾问</th>
                                        <th>订单数量</th>
                                        <th>占比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${this.renderRankingTable(orderRanking)}
                                </tbody>
                            </table>
                        </div>
                        <div class="chart-container">
                            <canvas id="order-ranking-chart" width="500" height="300"></canvas>
                        </div>
                    </div>

                    <div class="section-card">
                        <h4><i class="fas fa-car-side"></i> 销售顾问交车统计</h4>
                        <div class="ranking-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>销售顾问</th>
                                        <th>交车数量</th>
                                        <th>占比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${this.renderRankingTable(deliveryRanking)}
                                </tbody>
                            </table>
                        </div>
                        <div class="chart-container">
                            <canvas id="delivery-ranking-chart" width="500" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // 计算订单排名
    calculateOrderRanking: function(monthFilter) {
        // 这里需要从订单数据中统计，暂时使用展厅录入数据中意向为O的作为订单
        const filteredData = this.showroomData.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            const filterDate = new Date(monthFilter);
            return entryDate.getFullYear() === filterDate.getFullYear() &&
                   entryDate.getMonth() === filterDate.getMonth() &&
                   entry.intention === 'O'; // 意向为O表示订单
        });

        return this.calculateAdvisorRanking(filteredData);
    },

    // 计算交车排名
    calculateDeliveryRanking: function(monthFilter) {
        // 从订单管理数据中统计已交付的订单
        const filteredOrders = this.ordersData.filter(order => {
            const deliveryDate = new Date(order.deliveryDate);
            const filterDate = new Date(monthFilter);
            return order.deliveryDate &&
                   deliveryDate.getFullYear() === filterDate.getFullYear() &&
                   deliveryDate.getMonth() === filterDate.getMonth();
        });

        return this.calculateAdvisorRanking(filteredOrders, 'salesAdvisor');
    },

    // 计算顾问排名
    calculateAdvisorRanking: function(data, advisorField = 'salesAdvisor') {
        const advisorStats = {};

        data.forEach(entry => {
            const advisor = entry[advisorField] || '未知';
            advisorStats[advisor] = (advisorStats[advisor] || 0) + 1;
        });

        const total = data.length;
        const ranking = Object.entries(advisorStats)
            .map(([advisor, count]) => ({
                advisor,
                count,
                percentage: total > 0 ? ((count / total) * 100).toFixed(1) : 0
            }))
            .sort((a, b) => b.count - a.count);

        return ranking;
    },

    // 渲染排名表格
    renderRankingTable: function(ranking) {
        return ranking.slice(0, 10).map((item, index) => `
            <tr>
                <td>
                    <span class="rank-badge rank-${index < 3 ? index + 1 : 'other'}">${index + 1}</span>
                </td>
                <td>${item.advisor}</td>
                <td>${item.count}</td>
                <td>${item.percentage}%</td>
            </tr>
        `).join('');
    },

    // 更新排名筛选
    updateRankingFilter: function() {
        this.switchAnalysis('order-ranking');
    },

    // 刷新订单排名
    refreshOrderRanking: async function() {
        await this.loadAllData();
        this.switchAnalysis('order-ranking');
    },

    // 4. 销售过程指标分析
    renderSalesProcessAnalysis: function() {
        const today = new Date();
        const currentMonth = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;

        // 获取所有销售顾问
        const advisors = this.getAllSalesAdvisors();

        return `
            <div class="sales-process-analysis">
                <div class="analysis-filters">
                    <h3>销售过程指标分析</h3>
                    <div class="filter-controls">
                        <label>
                            <span>月份筛选：</span>
                            <input type="month" id="process-month-filter" value="${currentMonth}" onchange="analyticsModule.updateProcessFilter()">
                        </label>
                        <label>
                            <span>销售顾问：</span>
                            <select id="process-advisor-filter" onchange="analyticsModule.updateProcessFilter()">
                                <option value="">全部顾问</option>
                                ${advisors.map(advisor => `<option value="${advisor}">${advisor}</option>`).join('')}
                            </select>
                        </label>
                        <button class="btn btn-primary" onclick="analyticsModule.refreshSalesProcess()">刷新数据</button>
                    </div>
                </div>

                <div class="process-sections">
                    <div class="section-card">
                        <h4><i class="fas fa-heart"></i> 意向客户统计</h4>
                        <div id="intention-stats">
                            ${this.renderIntentionStats()}
                        </div>
                    </div>

                    <div class="section-card">
                        <h4><i class="fas fa-chart-line"></i> 销售顾问过程指标</h4>
                        <div id="advisor-metrics">
                            ${this.renderAdvisorMetrics()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // 获取所有销售顾问
    getAllSalesAdvisors: function() {
        const advisors = new Set();
        this.showroomData.forEach(entry => {
            if (entry.salesAdvisor) {
                advisors.add(entry.salesAdvisor);
            }
        });
        return Array.from(advisors).sort();
    },

    // 渲染意向客户统计
    renderIntentionStats: function() {
        const monthFilter = document.getElementById('process-month-filter')?.value ||
                           `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, '0')}`;

        const filteredData = this.showroomData.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            const filterDate = new Date(monthFilter);
            return entryDate.getFullYear() === filterDate.getFullYear() &&
                   entryDate.getMonth() === filterDate.getMonth();
        });

        // 统计各销售顾问对应展厅录入中来店类型H、A、B、C、F的数量
        const intentionTypes = ['H', 'A', 'B', 'C', 'F'];
        const advisorStats = {};

        filteredData.forEach(entry => {
            if (intentionTypes.includes(entry.intention)) {
                const advisor = entry.salesAdvisor || '未知';
                if (!advisorStats[advisor]) {
                    advisorStats[advisor] = {};
                    intentionTypes.forEach(type => advisorStats[advisor][type] = 0);
                }
                advisorStats[advisor][entry.intention]++;
            }
        });

        return `
            <div class="intention-table">
                <table>
                    <thead>
                        <tr>
                            <th>销售顾问</th>
                            <th>H级意向</th>
                            <th>A级意向</th>
                            <th>B级意向</th>
                            <th>C级意向</th>
                            <th>F级意向</th>
                            <th>总计</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(advisorStats).map(([advisor, stats]) => {
                            const total = Object.values(stats).reduce((sum, count) => sum + count, 0);
                            return `
                                <tr>
                                    <td>${advisor}</td>
                                    <td>${stats.H || 0}</td>
                                    <td>${stats.A || 0}</td>
                                    <td>${stats.B || 0}</td>
                                    <td>${stats.C || 0}</td>
                                    <td>${stats.F || 0}</td>
                                    <td><strong>${total}</strong></td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    // 渲染销售顾问指标
    renderAdvisorMetrics: function() {
        const monthFilter = document.getElementById('process-month-filter')?.value ||
                           `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, '0')}`;

        const advisorFilter = document.getElementById('process-advisor-filter')?.value || '';

        const advisors = advisorFilter ? [advisorFilter] : this.getAllSalesAdvisors();
        const metrics = advisors.map(advisor => this.calculateAdvisorMetrics(advisor, monthFilter));

        return `
            <div class="advisor-metrics-table">
                <table>
                    <thead>
                        <tr>
                            <th>销售顾问</th>
                            <th>留资率</th>
                            <th>接待时长</th>
                            <th>邀约率</th>
                            <th>试驾率</th>
                            <th>成交率</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${metrics.map(metric => `
                            <tr>
                                <td>${metric.advisor}</td>
                                <td>${metric.retentionRate}%</td>
                                <td>${metric.avgReceptionTime}</td>
                                <td>${metric.invitationRate}%</td>
                                <td>${metric.testDriveRate}%</td>
                                <td>${metric.conversionRate}%</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    // 计算销售顾问指标
    calculateAdvisorMetrics: function(advisor, monthFilter) {
        const filteredData = this.showroomData.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            const filterDate = new Date(monthFilter);
            return entry.salesAdvisor === advisor &&
                   entryDate.getFullYear() === filterDate.getFullYear() &&
                   entryDate.getMonth() === filterDate.getMonth();
        });

        // 留资率：(FU+FUI+FUD+FUID中有电话的)/(FU+FUI+FUD+FUID)
        const firstVisitTypes = ['FU', 'FUI', 'FUD', 'FUID'];
        const firstVisits = filteredData.filter(entry => firstVisitTypes.includes(entry.visitType));
        const firstVisitsWithPhone = firstVisits.filter(entry => entry.phone && entry.phone.trim() !== '');
        const retentionRate = firstVisits.length > 0 ?
            ((firstVisitsWithPhone.length / firstVisits.length) * 100).toFixed(1) : 0;

        // 接待时长：(FU+FUI+FUD+FUID+BB+BBI+BBD+BBID的总时长）/总数量
        const receptionTypes = ['FU', 'FUI', 'FUD', 'FUID', 'BB', 'BBI', 'BBD', 'BBID'];
        const receptionVisits = filteredData.filter(entry => receptionTypes.includes(entry.visitType));
        const totalDuration = receptionVisits.reduce((sum, entry) => {
            // 假设滞店时间格式为"X小时Y分钟"，需要解析
            const duration = this.parseDuration(entry.stayDuration);
            return sum + duration;
        }, 0);
        const avgReceptionTime = receptionVisits.length > 0 ?
            this.formatDuration(totalDuration / receptionVisits.length) : '0分钟';

        // 邀约率：BBI/（BB+BBI）
        const bbVisits = filteredData.filter(entry => entry.visitType === 'BB').length;
        const bbiVisits = filteredData.filter(entry => entry.visitType === 'BBI').length;
        const invitationRate = (bbVisits + bbiVisits) > 0 ?
            ((bbiVisits / (bbVisits + bbiVisits)) * 100).toFixed(1) : 0;

        // 试驾率：(FU+FUI+FUD+FUID中有试驾的）/(FU+FUI+FUD+FUID)
        const firstVisitsWithTestDrive = firstVisits.filter(entry =>
            entry.testDrive === '是' || entry.testDrive === true);
        const testDriveRate = firstVisits.length > 0 ?
            ((firstVisitsWithTestDrive.length / firstVisits.length) * 100).toFixed(1) : 0;

        // 成交率：O的数据/(FU+FUI+FUD+FUID+BB+BBI+BBD+BBID)
        const orders = filteredData.filter(entry => entry.intention === 'O').length;
        const conversionRate = receptionVisits.length > 0 ?
            ((orders / receptionVisits.length) * 100).toFixed(1) : 0;

        return {
            advisor,
            retentionRate,
            avgReceptionTime,
            invitationRate,
            testDriveRate,
            conversionRate
        };
    },

    // 解析时长字符串
    parseDuration: function(durationStr) {
        if (!durationStr) return 0;

        let totalMinutes = 0;
        const hourMatch = durationStr.match(/(\d+)小时/);
        const minuteMatch = durationStr.match(/(\d+)分钟/);

        if (hourMatch) totalMinutes += parseInt(hourMatch[1]) * 60;
        if (minuteMatch) totalMinutes += parseInt(minuteMatch[1]);

        return totalMinutes;
    },

    // 格式化时长
    formatDuration: function(minutes) {
        const hours = Math.floor(minutes / 60);
        const mins = Math.round(minutes % 60);

        if (hours > 0) {
            return `${hours}小时${mins}分钟`;
        } else {
            return `${mins}分钟`;
        }
    },

    // 更新过程筛选
    updateProcessFilter: function() {
        this.switchAnalysis('sales-process');
    },

    // 刷新销售过程
    refreshSalesProcess: async function() {
        await this.loadAllData();
        this.switchAnalysis('sales-process');
    }
};

// 初始化数据分析模块
document.addEventListener('DOMContentLoaded', function() {
    if (typeof window.dbFunctions !== 'undefined') {
        window.analyticsModule.init();
    }
});
