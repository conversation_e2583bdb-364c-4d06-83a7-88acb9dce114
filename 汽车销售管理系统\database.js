// 数据库实例
let db;

// 统一数据库结构定义
async function initDB() {
    db = new Dexie('AutoSalesDB');

    // 添加数据库升级处理
    db.version(1).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        testDrives: '++id, customerId, carModel, date, time, route, salesman, status',
        activities: '++id, customerId, type, timestamp, notes'
    });

    // 保持版本3的定义以支持数据迁移
    db.version(3).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        testDrives: '++id, customerId, carModel, date, time, route, salesman, status',
        activities: '++id, customerId, type, timestamp, notes',
        visits: '++id, customerId, date, duration, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name'
    });

    // 版本4 - 保持向后兼容
    db.version(4).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes'
    });

    // 版本5 - 完整重构后的数据库结构
    db.version(5).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes',
        showroomEntries: '++id, entryDate, entryPersonnel, salesAdvisor, arrivalTime, departureTime, stayDuration, visitType, channel, customerName, gender, phone, intendedModels, intention, region, currentModel, competitorModel, finance, tradeIn, testDrive, notes',
        leadEntries: '++id, entryDate, isValid, customerName, phone, leadId, smartNumber, intendedModels, region, wechat, channel, visitDate, dealDate, salesFollow, receptionAdvisor, firstFollowDate, followStatus'
    });

    // 版本6 - 订单管理重构和配车交付功能
    db.version(6).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes',
        showroomEntries: '++id, entryDate, entryPersonnel, salesAdvisor, arrivalTime, departureTime, stayDuration, visitType, channel, customerName, gender, phone, intendedModels, intention, region, currentModel, competitorModel, finance, tradeIn, testDrive, notes',
        leadEntries: '++id, entryDate, isValid, customerName, phone, leadId, smartNumber, intendedModels, region, wechat, channel, visitDate, dealDate, salesFollow, receptionAdvisor, firstFollowDate, followStatus',
        orderManagement: '++id, orderStatus, orderDate, customerName, phone1, phone2, auditStatus, salesAdvisor, vin, carModel, configuration, exteriorColor, interiorColor, options, deliveryStatus, resourceStatus, deliveryDate',
        inventoryManagement: '++id, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        deliveryTargets: '++id, yearMonth, target, actual, completionRate',
        orderTargets: '++id, yearMonth, target, actual, completionRate',
        retailTargets: '++id, yearMonth, target, actual, completionRate',
        userManagement: '++id, account, password, userName, position, permissions, createDate, lastLogin, status',
        deliveryManagement: '++id, orderStatus, orderDate, deliveryDate, customerName, phone1, phone2, salesAdvisor, carModel, configuration, exteriorColor, interiorColor, vin',
        deliveryInventory: '++id, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes'
    });

    // 版本7 - 添加配件库管理功能
    db.version(7).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes',
        // 客户管理相关表
        showroomEntries: '++id, entryDate, entryPersonnel, salesAdvisor, arrivalTime, departureTime, stayDuration, visitType, channel, customerName, gender, phone, intendedModels, intention, region, currentModel, competitorModel, finance, tradeIn, testDrive, notes',
        leadEntries: '++id, entryDate, isValid, customerName, phone, leadId, smartNumber, intendedModels, region, wechat, channel, visitDate, dealDate, salesFollow, receptionAdvisor, firstFollowDate, followStatus',
        // 订单管理相关表 - 版本6调整
        orderManagement: '++id, serialNumber, auditStatus, orderDate, customerName, phone1, salesAdvisor, vin, carModel, configuration, exteriorColor, interiorColor, options, deliveryStatus, deliveryDate, deposit, contractPrice',
        inventoryManagement: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        deliveryTargets: '++id, yearMonth, target, actual, completionRate',
        orderTargets: '++id, yearMonth, target, actual, completionRate',
        retailTargets: '++id, yearMonth, target, actual, completionRate',
        // 用户管理表
        userManagement: '++id, serialNumber, account, password, userName, position, permissions, createDate, lastLogin, status',
        // 新增配车交付相关表
        deliveryManagement: '++id, serialNumber, orderStatus, orderDate, deliveryDate, customerName, phone1, phone2, salesAdvisor, carModel, configuration, exteriorColor, interiorColor, vin',
        deliveryInventory: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        // 配件库管理相关表
        partsSettings: '++id, serialNumber, type, partCode, partName, salePrice, guidePrice, insurancePrice, costPrice, alternatePart, targetStock, minPackage',
        partsRequests: '++id, requestPerson, type, partCode, partName, quantity, status, createTime',
        partsInbound: '++id, inboundTime, stockAge, type, locationCode, partCode, partName, notes, totalStock, availableStock, reservedStock, lentStock, salePrice, guidePrice, insurancePrice, costPrice, alternatePart, targetStock, minPackage',
        partsOutbound: '++id, outboundTime, personnelType, personnelName, customerName, vin, type, partCode, partName, quantity, salePrice, guidePrice, insurancePrice, status',
        partsLending: '++id, lendDate, lendPerson, partCode, partName, quantity, returnDate, notes, status'
    });

    // 版本8 - 配件库管理功能增强，添加属性字段
    db.version(8).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes',
        // 客户管理相关表
        showroomEntries: '++id, entryDate, entryPersonnel, salesAdvisor, arrivalTime, departureTime, stayDuration, visitType, channel, customerName, gender, phone, intendedModels, intention, region, currentModel, competitorModel, finance, tradeIn, testDrive, notes',
        leadEntries: '++id, entryDate, isValid, customerName, phone, leadId, smartNumber, intendedModels, region, wechat, channel, visitDate, dealDate, salesFollow, receptionAdvisor, firstFollowDate, followStatus',
        // 订单管理相关表 - 版本6调整
        orderManagement: '++id, serialNumber, auditStatus, orderDate, customerName, phone1, salesAdvisor, vin, carModel, configuration, exteriorColor, interiorColor, options, deliveryStatus, deliveryDate, deposit, contractPrice',
        inventoryManagement: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        deliveryTargets: '++id, yearMonth, target, actual, completionRate',
        orderTargets: '++id, yearMonth, target, actual, completionRate',
        retailTargets: '++id, yearMonth, target, actual, completionRate',
        // 用户管理表
        userManagement: '++id, serialNumber, account, password, userName, position, permissions, createDate, lastLogin, status',
        // 新增配车交付相关表
        deliveryManagement: '++id, serialNumber, orderStatus, orderDate, deliveryDate, customerName, phone1, phone2, salesAdvisor, carModel, configuration, exteriorColor, interiorColor, vin',
        deliveryInventory: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        // 配件库管理相关表 - 版本8增强
        partsSettings: '++id, serialNumber, type, attribute, partCode, partName, salePrice, guidePrice, insurancePrice, costPrice, alternatePart, targetStock, minPackage',
        partsRequests: '++id, requestPerson, type, partCode, partName, quantity, status, createTime',
        partsInbound: '++id, inboundTime, stockAge, type, attribute, locationCode, partCode, partName, notes, totalStock, availableStock, reservedStock, lentStock, salePrice, guidePrice, insurancePrice, costPrice, alternatePart, targetStock, minPackage',
        partsOutbound: '++id, outboundTime, personnelType, personnelName, customerName, vin, type, partCode, partName, quantity, salePrice, guidePrice, insurancePrice, status',
        partsLending: '++id, lendDate, lendPerson, partCode, partName, quantity, returnDate, notes, status'
    });

    // 版本9 - 展厅录入添加报价字段
    db.version(9).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes',
        // 客户管理相关表 - 添加报价字段
        showroomEntries: '++id, entryDate, entryPersonnel, salesAdvisor, arrivalTime, departureTime, stayDuration, visitType, channel, customerName, gender, phone, intendedModels, intention, region, currentModel, competitorModel, finance, tradeIn, testDrive, quote, quoteAmount, notes',
        leadEntries: '++id, entryDate, isValid, customerName, phone, leadId, smartNumber, intendedModels, region, wechat, channel, visitDate, dealDate, salesFollow, receptionAdvisor, firstFollowDate, followStatus',
        // 订单管理相关表 - 版本6调整
        orderManagement: '++id, serialNumber, auditStatus, orderDate, customerName, phone1, salesAdvisor, vin, carModel, configuration, exteriorColor, interiorColor, options, deliveryStatus, deliveryDate, deposit, contractPrice',
        inventoryManagement: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        deliveryTargets: '++id, yearMonth, target, actual, completionRate',
        orderTargets: '++id, yearMonth, target, actual, completionRate',
        retailTargets: '++id, yearMonth, target, actual, completionRate',
        // 用户管理表
        userManagement: '++id, serialNumber, account, password, userName, position, permissions, createDate, lastLogin, status',
        // 新增配车交付相关表
        deliveryManagement: '++id, serialNumber, orderStatus, orderDate, deliveryDate, customerName, phone1, phone2, salesAdvisor, carModel, configuration, exteriorColor, interiorColor, vin',
        deliveryInventory: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        // 配件库管理相关表 - 版本8增强
        partsSettings: '++id, serialNumber, type, attribute, partCode, partName, salePrice, guidePrice, insurancePrice, costPrice, alternatePart, targetStock, minPackage',
        partsRequests: '++id, requestPerson, type, partCode, partName, quantity, status, createTime',
        partsInbound: '++id, inboundTime, stockAge, type, attribute, locationCode, partCode, partName, notes, totalStock, availableStock, reservedStock, lentStock, salePrice, guidePrice, insurancePrice, costPrice, alternatePart, targetStock, minPackage',
        partsOutbound: '++id, outboundTime, personnelType, personnelName, customerName, vin, type, partCode, partName, quantity, salePrice, guidePrice, insurancePrice, status',
        partsLending: '++id, lendDate, lendPerson, partCode, partName, quantity, returnDate, notes, status',
        // 线索渠道管理表
        leadChannels: '++id, name'
    });

    // 版本10 - 添加订单号字段和配件库索引修复
    db.version(10).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes',
        // 客户管理相关表 - 添加报价字段
        showroomEntries: '++id, entryDate, entryPersonnel, salesAdvisor, arrivalTime, departureTime, stayDuration, visitType, channel, customerName, gender, phone, intendedModels, intention, region, currentModel, competitorModel, finance, tradeIn, testDrive, quote, quoteAmount, notes',
        leadEntries: '++id, entryDate, isValid, customerName, phone, leadId, smartNumber, intendedModels, region, wechat, channel, visitDate, dealDate, salesFollow, receptionAdvisor, firstFollowDate, followStatus',
        // 订单管理相关表 - 添加订单号字段
        orderManagement: '++id, orderNumber, serialNumber, auditStatus, orderDate, customerName, phone1, salesAdvisor, vin, carModel, configuration, exteriorColor, interiorColor, options, deliveryStatus, deliveryDate, deposit, contractPrice',
        inventoryManagement: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        deliveryTargets: '++id, yearMonth, target, actual, completionRate',
        orderTargets: '++id, yearMonth, target, actual, completionRate',
        retailTargets: '++id, yearMonth, target, actual, completionRate',
        // 用户管理表
        userManagement: '++id, serialNumber, account, password, userName, position, permissions, createDate, lastLogin, status',
        // 新增配车交付相关表
        deliveryManagement: '++id, serialNumber, orderStatus, orderDate, deliveryDate, customerName, phone1, phone2, salesAdvisor, carModel, configuration, exteriorColor, interiorColor, vin',
        deliveryInventory: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        // 配件库管理相关表 - 修复索引问题
        partsSettings: '++id, serialNumber, type, partCode, partName, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsRequests: '++id, requestPersonnel, type, partCode, partName, quantity, status, createTime',
        partsInbound: '++id, inboundTime, stockAge, type, locationCode, partCode, partName, notes, totalStock, availableStock, reservedStock, lentStock, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsOutbound: '++id, outboundTime, personnel, customerName, vin, type, partCode, partName, quantity, salePrice, guidePrice, insurancePrice, status',
        partsLending: '++id, lendDate, lendPersonnel, partCode, partName, quantity, returnDate, notes, status',
        // 线索渠道管理表
        leadChannels: '++id, name'
    });

    // 版本11 - 最新版本，修复所有索引问题
    db.version(11).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes',
        // 客户管理相关表 - 添加报价字段
        showroomEntries: '++id, entryDate, entryPersonnel, salesAdvisor, arrivalTime, departureTime, stayDuration, visitType, channel, customerName, gender, phone, intendedModels, intention, region, currentModel, competitorModel, finance, tradeIn, testDrive, quote, quoteAmount, notes',
        leadEntries: '++id, entryDate, isValid, customerName, phone, leadId, smartNumber, intendedModels, region, wechat, channel, visitDate, dealDate, salesFollow, receptionAdvisor, firstFollowDate, followStatus',
        // 订单管理相关表 - 添加订单号字段
        orderManagement: '++id, orderNumber, serialNumber, auditStatus, orderDate, customerName, phone1, salesAdvisor, vin, carModel, configuration, exteriorColor, interiorColor, options, deliveryStatus, deliveryDate, deposit, contractPrice',
        inventoryManagement: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        deliveryTargets: '++id, yearMonth, target, actual, completionRate',
        orderTargets: '++id, yearMonth, target, actual, completionRate',
        retailTargets: '++id, yearMonth, target, actual, completionRate',
        // 用户管理表
        userManagement: '++id, serialNumber, account, password, userName, position, permissions, createDate, lastLogin, status',
        // 新增配车交付相关表
        deliveryManagement: '++id, serialNumber, orderStatus, orderDate, deliveryDate, customerName, phone1, phone2, salesAdvisor, carModel, configuration, exteriorColor, interiorColor, vin',
        deliveryInventory: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        // 配件库管理相关表 - 修复所有索引问题
        partsSettings: '++id, serialNumber, type, partCode, partName, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsRequests: '++id, requestPersonnel, type, partCode, partName, quantity, status, createTime',
        partsInbound: '++id, inboundTime, stockAge, type, locationCode, partCode, partName, notes, totalStock, availableStock, reservedStock, lentStock, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsOutbound: '++id, outboundTime, personnel, customerName, vin, type, partCode, partName, quantity, salePrice, guidePrice, insurancePrice, status',
        partsLending: '++id, lendDate, lendPersonnel, partCode, partName, quantity, returnDate, notes, status',
        // 线索渠道管理表
        leadChannels: '++id, name',
        // 定义管理表
        definitions: '++id, visitType, definition, intention'
    });

    // 版本12 - 统一状态字段命名
    db.version(12).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes',
        // 客户管理相关表 - 添加报价字段
        showroomEntries: '++id, entryDate, entryPersonnel, salesAdvisor, arrivalTime, departureTime, stayDuration, visitType, channel, customerName, gender, phone, intendedModels, intention, region, currentModel, competitorModel, finance, tradeIn, testDrive, quote, quoteAmount, notes',
        leadEntries: '++id, entryDate, isValid, customerName, phone, leadId, smartNumber, intendedModels, region, wechat, channel, visitDate, dealDate, salesFollow, receptionAdvisor, firstFollowDate, followStatus',
        // 订单管理相关表 - 添加订单号字段
        orderManagement: '++id, orderNumber, serialNumber, auditStatus, orderDate, customerName, phone1, salesAdvisor, vin, carModel, configuration, exteriorColor, interiorColor, options, deliveryStatus, deliveryDate, deposit, contractPrice',
        inventoryManagement: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        deliveryTargets: '++id, yearMonth, target, actual, completionRate',
        orderTargets: '++id, yearMonth, target, actual, completionRate',
        retailTargets: '++id, yearMonth, target, actual, completionRate',
        // 用户管理表
        userManagement: '++id, serialNumber, account, password, userName, position, permissions, createDate, lastLogin, status',
        // 配车交付相关表 - 统一使用deliveryStatus字段
        deliveryManagement: '++id, serialNumber, deliveryStatus, orderDate, deliveryDate, customerName, phone1, phone2, salesAdvisor, carModel, configuration, exteriorColor, interiorColor, vin',
        deliveryInventory: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        // 配件库管理相关表 - 修复所有索引问题
        partsSettings: '++id, serialNumber, type, partCode, partName, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsRequests: '++id, requestPersonnel, type, partCode, partName, quantity, status, createTime',
        partsInbound: '++id, inboundTime, stockAge, type, locationCode, partCode, partName, notes, totalStock, availableStock, reservedStock, lentStock, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsOutbound: '++id, outboundTime, personnel, customerName, vin, type, partCode, partName, quantity, salePrice, guidePrice, insurancePrice, status',
        partsLending: '++id, lendDate, lendPersonnel, partCode, partName, quantity, returnDate, notes, status',
        // 线索渠道管理表
        leadChannels: '++id, name',
        // 定义管理表
        definitions: '++id, visitType, definition, intention'
    }).upgrade(trans => {
        // 数据迁移：将orderStatus字段重命名为deliveryStatus
        return trans.deliveryManagement.toCollection().modify(delivery => {
            if (delivery.orderStatus !== undefined) {
                delivery.deliveryStatus = delivery.orderStatus;
                delete delivery.orderStatus;
            }
        });
    });

    // 版本13 - 状态字段统一化：合并deliveryStatus到auditStatus
    db.version(13).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes',
        // 客户管理相关表 - 添加报价字段
        showroomEntries: '++id, entryDate, entryPersonnel, salesAdvisor, arrivalTime, departureTime, stayDuration, visitType, channel, customerName, gender, phone, intendedModels, intention, region, currentModel, competitorModel, finance, tradeIn, testDrive, quote, quoteAmount, notes',
        leadEntries: '++id, entryDate, isValid, customerName, phone, leadId, smartNumber, intendedModels, region, wechat, channel, visitDate, dealDate, salesFollow, receptionAdvisor, firstFollowDate, followStatus',
        // 订单管理相关表 - 统一使用auditStatus字段，移除deliveryStatus
        orderManagement: '++id, orderNumber, serialNumber, auditStatus, orderDate, customerName, phone1, salesAdvisor, vin, carModel, configuration, exteriorColor, interiorColor, options, deliveryDate, deposit, contractPrice',
        inventoryManagement: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        deliveryTargets: '++id, yearMonth, target, actual, completionRate',
        orderTargets: '++id, yearMonth, target, actual, completionRate',
        retailTargets: '++id, yearMonth, target, actual, completionRate',
        // 用户管理表
        userManagement: '++id, serialNumber, account, password, userName, position, permissions, createDate, lastLogin, status',
        // 配车交付相关表 - 统一使用auditStatus字段，移除deliveryStatus
        deliveryManagement: '++id, serialNumber, auditStatus, orderDate, deliveryDate, customerName, phone1, phone2, salesAdvisor, carModel, configuration, exteriorColor, interiorColor, vin',
        deliveryInventory: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        // 配件库管理相关表 - 修复所有索引问题
        partsSettings: '++id, serialNumber, type, partCode, partName, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsRequests: '++id, requestPersonnel, type, partCode, partName, quantity, status, createTime',
        partsInbound: '++id, inboundTime, stockAge, type, locationCode, partCode, partName, notes, totalStock, availableStock, reservedStock, lentStock, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsOutbound: '++id, outboundTime, personnel, customerName, vin, type, partCode, partName, quantity, salePrice, guidePrice, insurancePrice, status',
        partsLending: '++id, lendDate, lendPersonnel, partCode, partName, quantity, returnDate, notes, status',
        // 线索渠道管理表
        leadChannels: '++id, name',
        // 定义管理表
        definitions: '++id, visitType, definition, intention'
    }).upgrade(trans => {
        // 数据迁移：合并deliveryStatus到auditStatus
        return Promise.all([
            // 迁移订单管理表
            trans.orderManagement.toCollection().modify(order => {
                if (order.deliveryStatus !== undefined) {
                    // 根据deliveryStatus映射到新的auditStatus
                    if (order.deliveryStatus === '已交付') {
                        order.auditStatus = '已交车';
                    } else if (order.deliveryStatus === '已配车') {
                        order.auditStatus = '已配车';
                    } else if (order.auditStatus === '已审核' && order.deliveryStatus === '待交付') {
                        order.auditStatus = '已审核';
                    }
                    // 删除旧字段
                    delete order.deliveryStatus;
                }
            }),
            // 迁移配车交付表
            trans.deliveryManagement.toCollection().modify(delivery => {
                if (delivery.deliveryStatus !== undefined) {
                    // 根据deliveryStatus映射到新的auditStatus
                    if (delivery.deliveryStatus === '已交付') {
                        delivery.auditStatus = '已交车';
                    } else if (delivery.deliveryStatus === '已配车') {
                        delivery.auditStatus = '已配车';
                    } else if (delivery.deliveryStatus === '待配车') {
                        delivery.auditStatus = '已审核';
                    }
                    // 删除旧字段
                    delete delivery.deliveryStatus;
                }
            })
        ]);
    });

    // 版本14 - 添加线索录入"是否重复"字段
    db.version(14).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes',
        // 客户管理相关表 - 添加报价字段
        showroomEntries: '++id, entryDate, entryPersonnel, salesAdvisor, arrivalTime, departureTime, stayDuration, visitType, channel, customerName, gender, phone, intendedModels, intention, region, currentModel, competitorModel, finance, tradeIn, testDrive, quote, quoteAmount, notes',
        // 线索录入表 - 添加"是否重复"字段
        leadEntries: '++id, entryDate, isValid, isDuplicate, customerName, phone, leadId, smartNumber, intendedModels, region, wechat, channel, visitDate, dealDate, salesFollow, receptionAdvisor, firstFollowDate, followStatus',
        // 订单管理相关表 - 统一使用auditStatus字段，移除deliveryStatus
        orderManagement: '++id, orderNumber, serialNumber, auditStatus, orderDate, customerName, phone1, salesAdvisor, vin, carModel, configuration, exteriorColor, interiorColor, options, deliveryDate, deposit, contractPrice',
        inventoryManagement: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        deliveryTargets: '++id, yearMonth, target, actual, completionRate',
        orderTargets: '++id, yearMonth, target, actual, completionRate',
        retailTargets: '++id, yearMonth, target, actual, completionRate',
        // 用户管理表
        userManagement: '++id, serialNumber, account, password, userName, position, permissions, createDate, lastLogin, status',
        // 配车交付相关表 - 统一使用auditStatus字段，移除deliveryStatus
        deliveryManagement: '++id, serialNumber, auditStatus, orderDate, deliveryDate, customerName, phone1, phone2, salesAdvisor, carModel, configuration, exteriorColor, interiorColor, vin',
        deliveryInventory: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        // 配件库管理相关表 - 修复所有索引问题
        partsSettings: '++id, serialNumber, type, partCode, partName, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsRequests: '++id, requestPersonnel, type, partCode, partName, quantity, status, createTime',
        partsInbound: '++id, inboundTime, stockAge, type, locationCode, partCode, partName, notes, totalStock, availableStock, reservedStock, lentStock, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsOutbound: '++id, outboundTime, personnel, customerName, vin, type, partCode, partName, quantity, salePrice, guidePrice, insurancePrice, status',
        partsLending: '++id, lendDate, lendPersonnel, partCode, partName, quantity, returnDate, notes, status',
        // 线索渠道管理表
        leadChannels: '++id, name',
        // 定义管理表
        definitions: '++id, visitType, definition, intention'
    }).upgrade(trans => {
        // 为现有线索录入记录添加默认的isDuplicate字段
        return trans.leadEntries.toCollection().modify(entry => {
            if (entry.isDuplicate === undefined) {
                entry.isDuplicate = false; // 默认为非重复
            }
        });
    });

    // 版本16 - 订单管理表添加身份证号码和联系地址字段
    db.version(16).stores({
        customers: '++id, name, phone, status, salesman, carModel, notes, date',
        visits: '++id, customerId, date, duration, notes',
        activities: '++id, customerId, type, timestamp, notes',
        users: '++id, name, role',
        inputPersonnel: '++id, name, status',
        salesAdvisors: '++id, name, status',
        carModels: '++id, name',
        intentions: '++id, name',
        competitors: '++id, name',
        regions: '++id, name',
        channels: '++id, name',
        visitTypes: '++id, name',
        testDriveModels: '++id, name, plateNumber, configuration, vin, exteriorColor, interiorColor, factoryOptions, guidePrice, status, type, invoiceDate, reportDate, registrationDate, expiryDate, notes',
        // 客户管理相关表 - 添加报价字段
        showroomEntries: '++id, entryDate, entryPersonnel, salesAdvisor, arrivalTime, departureTime, stayDuration, visitType, channel, customerName, gender, phone, intendedModels, intention, region, currentModel, competitorModel, finance, tradeIn, testDrive, quote, quoteAmount, notes',
        // 线索录入表 - 添加"是否重复"字段
        leadEntries: '++id, entryDate, isValid, isDuplicate, customerName, phone, leadId, smartNumber, intendedModels, region, wechat, channel, visitDate, dealDate, salesFollow, receptionAdvisor, firstFollowDate, followStatus',
        // 订单管理相关表 - 添加身份证号码和联系地址字段
        orderManagement: '++id, orderNumber, serialNumber, auditStatus, orderDate, customerName, idNumber, phone1, address, salesAdvisor, vin, carModel, configuration, exteriorColor, interiorColor, options, deliveryDate, deposit, contractPrice',
        inventoryManagement: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        deliveryTargets: '++id, yearMonth, target, actual, completionRate',
        orderTargets: '++id, yearMonth, target, actual, completionRate',
        retailTargets: '++id, yearMonth, target, actual, completionRate',
        // 用户管理表
        userManagement: '++id, serialNumber, account, password, userName, position, permissions, createDate, lastLogin, status',
        // 配车交付相关表 - 统一使用auditStatus字段，移除deliveryStatus
        deliveryManagement: '++id, serialNumber, auditStatus, orderDate, deliveryDate, customerName, phone1, phone2, salesAdvisor, carModel, configuration, exteriorColor, interiorColor, vin',
        deliveryInventory: '++id, serialNumber, inventoryStatus, carModel, version, vin, exteriorColor, interiorColor, factoryOptions, standard, location, productionDate, shipmentDate, stockDate, stockAge, guidePrice, notes',
        // 配件库管理相关表 - 修复所有索引问题
        partsSettings: '++id, serialNumber, type, partCode, partName, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsRequests: '++id, requestPersonnel, type, partCode, partName, quantity, status, createTime',
        partsInbound: '++id, inboundTime, stockAge, type, locationCode, partCode, partName, notes, totalStock, availableStock, reservedStock, lentStock, salePrice, guidePrice, insurancePrice, costPrice, replacementPart, targetStock, minPackage, attribute',
        partsOutbound: '++id, outboundTime, personnel, customerName, vin, type, partCode, partName, quantity, salePrice, guidePrice, insurancePrice, status',
        partsLending: '++id, lendDate, lendPersonnel, partCode, partName, quantity, returnDate, notes, status',
        // 线索渠道管理表
        leadChannels: '++id, name',
        // 定义管理表
        definitions: '++id, visitType, definition, intention'
    }).upgrade(trans => {
        // 为现有订单记录添加默认的身份证号码和联系地址字段
        return trans.orderManagement.toCollection().modify(order => {
            if (order.idNumber === undefined) {
                order.idNumber = ''; // 默认为空
            }
            if (order.address === undefined) {
                order.address = ''; // 默认为空
            }
        });
    });

    await db.open();

    // 确保数据库实例全局可用
    window.db = db;
}

// 初始化数据库
async function seedDemoData() {
    // 检查是否已有配件设置数据
    const existingPartsSettings = await db.partsSettings.count();

    if (existingPartsSettings === 0) {
        // 添加配件设置演示数据
        const partsSettingsData = [
            {
                serialNumber: '0001',
                type: '原厂件',
                attribute: '配件',
                partCode: 'BRK001',
                partName: '前刹车片',
                salePrice: 580.00,
                guidePrice: 650.00,
                insurancePrice: 620.00,
                costPrice: 420.00,
                alternatePart: 'BRK002',
                targetStock: 20,
                minPackage: 1
            },
            {
                serialNumber: '0002',
                type: '原厂件',
                attribute: '配件',
                partCode: 'OIL001',
                partName: '机油滤清器',
                salePrice: 85.00,
                guidePrice: 95.00,
                insurancePrice: 90.00,
                costPrice: 55.00,
                alternatePart: '',
                targetStock: 50,
                minPackage: 1
            },
            {
                serialNumber: '0003',
                type: '原厂件',
                attribute: '配件',
                partCode: 'AIR001',
                partName: '空气滤清器',
                salePrice: 120.00,
                guidePrice: 135.00,
                insurancePrice: 128.00,
                costPrice: 80.00,
                alternatePart: 'AIR002',
                targetStock: 30,
                minPackage: 1
            },
            {
                serialNumber: '0004',
                type: '原厂件',
                attribute: '配件',
                partCode: 'SPK001',
                partName: '火花塞',
                salePrice: 45.00,
                guidePrice: 52.00,
                insurancePrice: 48.00,
                costPrice: 28.00,
                alternatePart: '',
                targetStock: 40,
                minPackage: 4
            },
            {
                serialNumber: '0005',
                type: '原厂件',
                attribute: '配件',
                partCode: 'BAT001',
                partName: '蓄电池',
                salePrice: 680.00,
                guidePrice: 750.00,
                insurancePrice: 720.00,
                costPrice: 480.00,
                alternatePart: 'BAT002',
                targetStock: 15,
                minPackage: 1
            },
            {
                serialNumber: '0006',
                type: '原厂件',
                attribute: '精品',
                partCode: 'ACC001',
                partName: '脚垫套装',
                salePrice: 280.00,
                guidePrice: 320.00,
                insurancePrice: 300.00,
                costPrice: 180.00,
                alternatePart: '',
                targetStock: 25,
                minPackage: 1
            },
            {
                serialNumber: '0007',
                type: '原厂件',
                attribute: '精品',
                partCode: 'ACC002',
                partName: '行车记录仪',
                salePrice: 1280.00,
                guidePrice: 1450.00,
                insurancePrice: 1350.00,
                costPrice: 880.00,
                alternatePart: '',
                targetStock: 10,
                minPackage: 1
            }
        ];

        for (const setting of partsSettingsData) {
            await db.partsSettings.add(setting);
        }

        // 添加入库记录演示数据
        const inboundData = [
            {
                inboundTime: '2024-01-15',
                type: '原厂件',
                attribute: '配件',
                locationCode: 'A01-B01',
                partCode: 'BRK001',
                partName: '前刹车片',
                totalStock: 25,
                salePrice: 580.00,
                guidePrice: 650.00,
                insurancePrice: 620.00,
                costPrice: 420.00,
                alternatePart: 'BRK002',
                targetStock: 20,
                minPackage: 1,
                notes: '新到货批次'
            },
            {
                inboundTime: '2024-01-20',
                type: '原厂件',
                attribute: '配件',
                locationCode: 'A02-B01',
                partCode: 'OIL001',
                partName: '机油滤清器',
                totalStock: 60,
                salePrice: 85.00,
                guidePrice: 95.00,
                insurancePrice: 90.00,
                costPrice: 55.00,
                alternatePart: '',
                targetStock: 50,
                minPackage: 1,
                notes: '常规补货'
            },
            {
                inboundTime: '2024-01-25',
                type: '原厂件',
                attribute: '配件',
                locationCode: 'A03-B01',
                partCode: 'AIR001',
                partName: '空气滤清器',
                totalStock: 35,
                salePrice: 120.00,
                guidePrice: 135.00,
                insurancePrice: 128.00,
                costPrice: 80.00,
                alternatePart: 'AIR002',
                targetStock: 30,
                minPackage: 1,
                notes: '季度补货'
            },
            {
                inboundTime: '2024-02-01',
                type: '原厂件',
                attribute: '配件',
                locationCode: 'A04-B01',
                partCode: 'SPK001',
                partName: '火花塞',
                totalStock: 48,
                salePrice: 45.00,
                guidePrice: 52.00,
                insurancePrice: 48.00,
                costPrice: 28.00,
                alternatePart: '',
                targetStock: 40,
                minPackage: 4,
                notes: '按最小包装数入库'
            },
            {
                inboundTime: '2024-02-05',
                type: '原厂件',
                attribute: '配件',
                locationCode: 'A05-B01',
                partCode: 'BAT001',
                partName: '蓄电池',
                totalStock: 18,
                salePrice: 680.00,
                guidePrice: 750.00,
                insurancePrice: 720.00,
                costPrice: 480.00,
                alternatePart: 'BAT002',
                targetStock: 15,
                minPackage: 1,
                notes: '高价值配件'
            },
            {
                inboundTime: '2024-02-10',
                type: '原厂件',
                attribute: '精品',
                locationCode: 'B01-A01',
                partCode: 'ACC001',
                partName: '脚垫套装',
                totalStock: 30,
                salePrice: 280.00,
                guidePrice: 320.00,
                insurancePrice: 300.00,
                costPrice: 180.00,
                alternatePart: '',
                targetStock: 25,
                minPackage: 1,
                notes: '精品配件'
            },
            {
                inboundTime: '2024-02-12',
                type: '原厂件',
                attribute: '精品',
                locationCode: 'B01-A02',
                partCode: 'ACC002',
                partName: '行车记录仪',
                totalStock: 12,
                salePrice: 1280.00,
                guidePrice: 1450.00,
                insurancePrice: 1350.00,
                costPrice: 880.00,
                alternatePart: '',
                targetStock: 10,
                minPackage: 1,
                notes: '高价值精品'
            }
        ];

        for (const inbound of inboundData) {
            await db.partsInbound.add(inbound);
        }

        console.log('配件库演示数据初始化完成');
    } else {
        console.log('配件库数据已存在，跳过演示数据初始化');
    }

    // 检查是否已有线索渠道数据
    const existingLeadChannels = await db.leadChannels.count();

    if (existingLeadChannels === 0) {
        // 添加预置线索渠道数据
        const leadChannelsData = [
            { name: '抖音' },
            { name: '懂车帝' },
            { name: '易车' },
            { name: '小红书' },
            { name: '汽车之家' },
            { name: '车展' },
            { name: '热线' },
            { name: '转介绍' },
            { name: '厂家-林肯媒体' },
            { name: '厂家-抖音' },
            { name: '厂家-太平洋汽车' },
            { name: '厂家-易车' },
            { name: '厂家-微信朋友圈' },
            { name: '厂家-汽车之家' },
            { name: '厂家-百度' },
            { name: '厂家-懂车帝' }
        ];

        for (const channel of leadChannelsData) {
            await db.leadChannels.add(channel);
        }

        console.log('线索渠道演示数据初始化完成');
    } else {
        console.log('线索渠道数据已存在，跳过演示数据初始化');
    }
}

// 获取所有客户
async function getAllCustomers() {
    return await db.customers.toArray();
}

// 获取所有客户管理数据（别名函数，用于销售分析）
async function getAllCustomerManagement() {
    return await db.customers.toArray();
}

// 添加新客户
async function addCustomer(customer) {
    return await db.customers.add(customer);
}

// 更新客户信息
async function updateCustomer(id, updates) {
    return await db.customers.update(id, updates);
}

// 添加获取试驾数据的函数
async function getAllTestDrives() {
    return await db.testDrives.toArray();
}

async function addTestDrive(testDrive) {
    return await db.testDrives.add(testDrive);
}

// 录入人员管理函数
async function getAllInputPersonnel() {
    return await db.inputPersonnel.toArray();
}

async function addInputPersonnel(personnel) {
    return await db.inputPersonnel.add(personnel);
}

async function updateInputPersonnel(id, updates) {
    return await db.inputPersonnel.update(id, updates);
}

async function getInputPersonnelById(id) {
    return await db.inputPersonnel.get(id);
}

async function deleteInputPersonnel(id) {
    return await db.inputPersonnel.delete(id);
}

// 添加获取用户数据的函数
async function getAllUsers() {
    return await db.users.toArray();
}

async function getAllSalesAdvisors() {
    return await db.salesAdvisors.toArray();
}

// 获取在职的销售顾问
async function getActiveSalesAdvisors() {
    return await db.salesAdvisors.where('status').equals('在职').toArray();
}

async function addSalesAdvisor(advisor) {
    return await db.salesAdvisors.add(advisor);
}

async function updateSalesAdvisor(id, updates) {
    return await db.salesAdvisors.update(id, updates);
}

async function getSalesAdvisorById(id) {
    return await db.salesAdvisors.get(id);
}

async function deleteSalesAdvisor(id) {
    return await db.salesAdvisors.delete(id);
}

async function getAllCarModels() {
    return await db.carModels.toArray();
}

async function addCarModel(model) {
    return await db.carModels.add(model);
}

async function updateCarModel(id, updates) {
    return await db.carModels.update(id, updates);
}

async function getCarModelById(id) {
    return await db.carModels.get(id);
}

async function deleteCarModel(id) {
    return await db.carModels.delete(id);
}

async function getAllIntentions() {
    return await db.intentions.toArray();
}

async function addIntention(intention) {
    return await db.intentions.add(intention);
}

async function updateIntention(id, updates) {
    return await db.intentions.update(id, updates);
}

async function getIntentionById(id) {
    return await db.intentions.get(id);
}

async function deleteIntention(id) {
    return await db.intentions.delete(id);
}

async function getAllCompetitors() {
    return await db.competitors.toArray();
}

async function addCompetitor(competitor) {
    return await db.competitors.add(competitor);
}

async function updateCompetitor(id, updates) {
    return await db.competitors.update(id, updates);
}

async function getCompetitorById(id) {
    return await db.competitors.get(id);
}

async function deleteCompetitor(id) {
    return await db.competitors.delete(id);
}

async function getAllRegions() {
    return await db.regions.toArray();
}

async function addRegion(region) {
    return await db.regions.add(region);
}

async function updateRegion(id, updates) {
    return await db.regions.update(id, updates);
}

async function getRegionById(id) {
    return await db.regions.get(id);
}

async function deleteRegion(id) {
    return await db.regions.delete(id);
}

async function getAllChannels() {
    return await db.channels.toArray();
}

async function addChannel(channel) {
    return await db.channels.add(channel);
}

async function updateChannel(id, updates) {
    return await db.channels.update(id, updates);
}

async function getChannelById(id) {
    return await db.channels.get(id);
}

async function deleteChannel(id) {
    return await db.channels.delete(id);
}

async function getAllVisitTypes() {
    return await db.visitTypes.toArray();
}

async function addVisitType(visitType) {
    return await db.visitTypes.add(visitType);
}

async function updateVisitType(id, updates) {
    return await db.visitTypes.update(id, updates);
}

async function getVisitTypeById(id) {
    return await db.visitTypes.get(id);
}

async function deleteVisitType(id) {
    return await db.visitTypes.delete(id);
}

async function getAllTestDriveModels() {
    return await db.testDriveModels.toArray();
}

async function addTestDriveModel(model) {
    return await db.testDriveModels.add(model);
}

async function updateTestDriveModel(id, updates) {
    return await db.testDriveModels.update(id, updates);
}

async function getTestDriveModelById(id) {
    return await db.testDriveModels.get(id);
}

async function deleteTestDriveModel(id) {
    return await db.testDriveModels.delete(id);
}

// 定义管理函数
async function getAllDefinitions() {
    return await db.definitions.toArray();
}

async function addDefinition(definition) {
    return await db.definitions.add(definition);
}

async function updateDefinition(id, updates) {
    return await db.definitions.update(id, updates);
}

async function getDefinitionById(id) {
    return await db.definitions.get(id);
}

async function deleteDefinition(id) {
    return await db.definitions.delete(id);
}

// 客户到店记录管理函数
async function getAllCustomerVisits() {
    return await db.customerVisits.orderBy('date').reverse().toArray();
}

async function addCustomerVisit(visit) {
    return await db.customerVisits.add(visit);
}

async function updateCustomerVisit(id, updates) {
    return await db.customerVisits.update(id, updates);
}

async function getCustomerVisitById(id) {
    return await db.customerVisits.get(id);
}

async function deleteCustomerVisit(id) {
    return await db.customerVisits.delete(id);
}

// 展厅录入管理函数
async function getAllShowroomEntries() {
    return await db.showroomEntries.orderBy('entryDate').reverse().toArray();
}

async function addShowroomEntry(entry) {
    return await db.showroomEntries.add(entry);
}

async function updateShowroomEntry(id, updates) {
    return await db.showroomEntries.update(id, updates);
}

async function getShowroomEntryById(id) {
    return await db.showroomEntries.get(id);
}

async function deleteShowroomEntry(id) {
    return await db.showroomEntries.delete(id);
}

// 线索录入管理函数
async function getAllLeadEntries() {
    return await db.leadEntries.orderBy('entryDate').reverse().toArray();
}

async function addLeadEntry(entry) {
    return await db.leadEntries.add(entry);
}

async function updateLeadEntry(id, updates) {
    return await db.leadEntries.update(id, updates);
}

async function getLeadEntryById(id) {
    return await db.leadEntries.get(id);
}

async function deleteLeadEntry(id) {
    return await db.leadEntries.delete(id);
}

// 订单管理函数
async function getAllOrderManagement() {
    return await db.orderManagement.orderBy('orderDate').reverse().toArray();
}

async function addOrderManagement(order) {
    return await db.orderManagement.add(order);
}

async function updateOrderManagement(id, updates) {
    return await db.orderManagement.update(id, updates);
}

async function getOrderManagementById(id) {
    return await db.orderManagement.get(id);
}

async function getOrderByOrderNumber(orderNumber) {
    return await db.orderManagement.where('orderNumber').equals(orderNumber).first();
}

async function deleteOrderManagement(id) {
    return await db.orderManagement.delete(id);
}

// 库存管理函数
async function getAllInventoryManagement() {
    return await db.inventoryManagement.orderBy('stockDate').reverse().toArray();
}

async function addInventoryManagement(inventory) {
    return await db.inventoryManagement.add(inventory);
}

async function updateInventoryManagement(id, updates) {
    return await db.inventoryManagement.update(id, updates);
}

async function getInventoryManagementById(id) {
    return await db.inventoryManagement.get(id);
}

async function deleteInventoryManagement(id) {
    return await db.inventoryManagement.delete(id);
}

// 提车目标管理函数
async function getAllDeliveryTargets() {
    return await db.deliveryTargets.orderBy('yearMonth').reverse().toArray();
}

async function addDeliveryTarget(target) {
    return await db.deliveryTargets.add(target);
}

async function updateDeliveryTarget(id, updates) {
    return await db.deliveryTargets.update(id, updates);
}

async function getDeliveryTargetById(id) {
    return await db.deliveryTargets.get(id);
}

async function deleteDeliveryTarget(id) {
    return await db.deliveryTargets.delete(id);
}

// 订单目标管理函数
async function getAllOrderTargets() {
    return await db.orderTargets.orderBy('yearMonth').reverse().toArray();
}

async function addOrderTarget(target) {
    return await db.orderTargets.add(target);
}

async function updateOrderTarget(id, updates) {
    return await db.orderTargets.update(id, updates);
}

async function getOrderTargetById(id) {
    return await db.orderTargets.get(id);
}

async function deleteOrderTarget(id) {
    return await db.orderTargets.delete(id);
}

// 零售目标管理函数
async function getAllRetailTargets() {
    return await db.retailTargets.orderBy('yearMonth').reverse().toArray();
}

async function addRetailTarget(target) {
    return await db.retailTargets.add(target);
}

async function updateRetailTarget(id, updates) {
    return await db.retailTargets.update(id, updates);
}

async function getRetailTargetById(id) {
    return await db.retailTargets.get(id);
}

async function deleteRetailTarget(id) {
    return await db.retailTargets.delete(id);
}

// 用户管理函数
async function getAllUserManagement() {
    return await db.userManagement.orderBy('createDate').reverse().toArray();
}

async function addUserManagement(user) {
    // 检查账号唯一性
    const existingUser = await db.userManagement.where('account').equals(user.account).first();
    if (existingUser) {
        throw new Error('账号已存在');
    }

    // 密码简单加密（Base64）
    if (user.password) {
        user.password = btoa(user.password);
    }

    // 添加创建日期
    user.createDate = new Date().toISOString().split('T')[0];
    user.lastLogin = '';

    return await db.userManagement.add(user);
}

async function updateUserManagement(id, updates) {
    // 如果更新密码，进行加密
    if (updates.password) {
        updates.password = btoa(updates.password);
    }

    // 如果更新账号，检查唯一性
    if (updates.account) {
        const existingUser = await db.userManagement.where('account').equals(updates.account).first();
        if (existingUser && existingUser.id !== id) {
            throw new Error('账号已存在');
        }
    }

    return await db.userManagement.update(id, updates);
}

async function getUserManagementById(id) {
    const user = await db.userManagement.get(id);
    if (user && user.password) {
        // 解密密码用于编辑
        user.password = atob(user.password);
    }
    return user;
}

async function deleteUserManagement(id) {
    return await db.userManagement.delete(id);
}

async function updateUserLastLogin(account) {
    const user = await db.userManagement.where('account').equals(account).first();
    if (user) {
        await db.userManagement.update(user.id, { lastLogin: new Date().toISOString().split('T')[0] });
    }
}

// 导出所有数据库操作函数
window.dbFunctions = {
    initDB,
    seedDemoData,
    getAllCustomers,
    getAllTestDrives,
    getAllUsers,
    addCustomer,
    updateCustomer,
    addTestDrive,
    getAllInputPersonnel,
    addInputPersonnel,
    updateInputPersonnel,
    getInputPersonnelById,
    deleteInputPersonnel,
    getAllSalesAdvisors,
    getActiveSalesAdvisors,
    addSalesAdvisor,
    updateSalesAdvisor,
    getSalesAdvisorById,
    deleteSalesAdvisor,
    getAllCarModels,
    addCarModel,
    updateCarModel,
    getCarModelById,
    deleteCarModel,
    getAllIntentions,
    addIntention,
    updateIntention,
    getIntentionById,
    deleteIntention,
    getAllCompetitors,
    addCompetitor,
    updateCompetitor,
    getCompetitorById,
    deleteCompetitor,
    getAllRegions,
    addRegion,
    updateRegion,
    getRegionById,
    deleteRegion,
    getAllChannels,
    addChannel,
    updateChannel,
    getChannelById,
    deleteChannel,
    getAllVisitTypes,
    addVisitType,
    updateVisitType,
    getVisitTypeById,
    deleteVisitType,
    getAllTestDriveModels,
    addTestDriveModel,
    updateTestDriveModel,
    getTestDriveModelById,
    deleteTestDriveModel,
    getAllDefinitions,
    addDefinition,
    updateDefinition,
    getDefinitionById,
    deleteDefinition,
    getAllCustomerVisits,
    addCustomerVisit,
    updateCustomerVisit,
    getCustomerVisitById,
    deleteCustomerVisit,
    // 新增函数
    getAllCustomerManagement,
    getAllShowroomEntries,
    addShowroomEntry,
    updateShowroomEntry,
    getShowroomEntryById,
    deleteShowroomEntry,
    getAllLeadEntries,
    addLeadEntry,
    updateLeadEntry,
    getLeadEntryById,
    deleteLeadEntry,
    getAllOrderManagement,
    addOrderManagement,
    updateOrderManagement,
    getOrderManagementById,
    getOrderByOrderNumber,
    deleteOrderManagement,
    getAllInventoryManagement,
    addInventoryManagement,
    updateInventoryManagement,
    getInventoryManagementById,
    deleteInventoryManagement,
    getAllDeliveryTargets,
    addDeliveryTarget,
    updateDeliveryTarget,
    getDeliveryTargetById,
    deleteDeliveryTarget,
    getAllOrderTargets,
    addOrderTarget,
    updateOrderTarget,
    getOrderTargetById,
    deleteOrderTarget,
    getAllRetailTargets,
    addRetailTarget,
    updateRetailTarget,
    getRetailTargetById,
    deleteRetailTarget,
    // 用户管理函数
    getAllUserManagement,
    addUserManagement,
    updateUserManagement,
    getUserManagementById,
    deleteUserManagement,
    updateUserLastLogin,
    // 配车交付管理相关函数
    getAllDeliveryManagement,
    addDeliveryManagement,
    updateDeliveryManagement,
    getDeliveryManagementById,
    deleteDeliveryManagement,
    // 配车交付库存相关函数
    getAllDeliveryInventory,
    addDeliveryInventory,
    updateDeliveryInventory,
    getDeliveryInventoryById,
    deleteDeliveryInventory,
    // 配件库管理相关函数
    getAllPartsSettings,
    addPartsSettings,
    updatePartsSettings,
    deletePartsSettings,
    getPartsSettingsById,
    getPartsSettingsByCode,
    getAllPartsRequests,
    addPartsRequest,
    updatePartsRequest,
    deletePartsRequest,
    getPartsRequestById,
    getAllPartsInbound,
    addPartsInbound,
    updatePartsInbound,
    deletePartsInbound,
    getPartsInboundById,
    getPartsInboundByCode,
    getAllPartsOutbound,
    addPartsOutbound,
    updatePartsOutbound,
    deletePartsOutbound,
    getPartsOutboundById,
    getAllPartsLending,
    addPartsLending,
    updatePartsLending,
    deletePartsLending,
    getPartsLendingById,
    // 线索渠道管理函数
    getAllLeadChannels,
    addLeadChannel,
    updateLeadChannel,
    getLeadChannelById,
    deleteLeadChannel,
    // 配件查询函数
    getPartsByAttribute,
    // 批量导入函数
    clearAndAddInputPersonnel,
    clearAndAddSalesAdvisors,
    clearAndAddCarModels,
    clearAndAddIntentions,
    clearAndAddCompetitors,
    clearAndAddRegions,
    clearAndAddChannels,
    clearAndAddLeadChannels,
    clearAndAddVisitTypes,
    clearAndAddTestDriveModels,
    clearAndAddDefinitions,
    clearAndAddCustomerManagement,
    clearAndAddShowroomEntries,
    clearAndAddLeadEntries,
    clearAndAddOrderManagement,
    clearAndAddDeliveryManagement,
    clearAndAddInventoryManagement,
    clearAndAddPartsSettings,
    clearAndAddPartsInbound,
    clearAndAddPartsOutbound,
    clearAndAddPartsLending
};

// 配车交付管理相关函数
async function getAllDeliveryManagement() {
    return await db.deliveryManagement.toArray();
}

async function addDeliveryManagement(delivery) {
    return await db.deliveryManagement.add(delivery);
}

async function updateDeliveryManagement(id, delivery) {
    return await db.deliveryManagement.update(id, delivery);
}

async function deleteDeliveryManagement(id) {
    return await db.deliveryManagement.delete(id);
}

async function getDeliveryManagementById(id) {
    return await db.deliveryManagement.get(id);
}

// 配车交付库存相关函数
async function getAllDeliveryInventory() {
    return await db.deliveryInventory.toArray();
}

async function addDeliveryInventory(inventory) {
    return await db.deliveryInventory.add(inventory);
}

async function updateDeliveryInventory(id, inventory) {
    return await db.deliveryInventory.update(id, inventory);
}

async function deleteDeliveryInventory(id) {
    return await db.deliveryInventory.delete(id);
}

async function getDeliveryInventoryById(id) {
    return await db.deliveryInventory.get(id);
}

// 配件库存设置相关函数
async function getAllPartsSettings() {
    return await db.partsSettings.orderBy('serialNumber').toArray();
}

async function addPartsSettings(part) {
    return await db.partsSettings.add(part);
}

async function updatePartsSettings(id, part) {
    return await db.partsSettings.update(id, part);
}

async function deletePartsSettings(id) {
    return await db.partsSettings.delete(id);
}

async function getPartsSettingsById(id) {
    return await db.partsSettings.get(id);
}

async function getPartsSettingsByCode(partCode) {
    return await db.partsSettings.where('partCode').equals(partCode).first();
}

// 配件需求相关函数
async function getAllPartsRequests() {
    try {
        // 尝试按createTime排序
        return await db.partsRequests.orderBy('createTime').reverse().toArray();
    } catch (error) {
        // 如果createTime字段不存在或没有索引，则按id排序
        console.warn('createTime字段索引不存在，使用id排序:', error);
        return await db.partsRequests.orderBy('id').reverse().toArray();
    }
}

async function addPartsRequest(request) {
    // 确保设置createTime字段
    if (!request.createTime) {
        request.createTime = new Date().toISOString();
    }
    return await db.partsRequests.add(request);
}

async function updatePartsRequest(id, request) {
    return await db.partsRequests.update(id, request);
}

async function deletePartsRequest(id) {
    return await db.partsRequests.delete(id);
}

async function getPartsRequestById(id) {
    return await db.partsRequests.get(id);
}

// 配件入库相关函数
async function getAllPartsInbound() {
    try {
        // 尝试按inboundTime排序
        return await db.partsInbound.orderBy('inboundTime').reverse().toArray();
    } catch (error) {
        // 如果inboundTime字段不存在或没有索引，则按id排序
        console.warn('inboundTime字段索引不存在，使用id排序:', error);
        return await db.partsInbound.orderBy('id').reverse().toArray();
    }
}

async function addPartsInbound(inbound) {
    // 确保设置inboundTime字段
    if (!inbound.inboundTime) {
        inbound.inboundTime = new Date().toISOString();
    }
    return await db.partsInbound.add(inbound);
}

async function updatePartsInbound(id, inbound) {
    return await db.partsInbound.update(id, inbound);
}

async function deletePartsInbound(id) {
    return await db.partsInbound.delete(id);
}

async function getPartsInboundById(id) {
    return await db.partsInbound.get(id);
}

async function getPartsInboundByCode(partCode) {
    return await db.partsInbound.where('partCode').equals(partCode).toArray();
}

// 配件出库相关函数
async function getAllPartsOutbound() {
    try {
        // 尝试按outboundTime排序
        return await db.partsOutbound.orderBy('outboundTime').reverse().toArray();
    } catch (error) {
        // 如果outboundTime字段不存在或没有索引，则按id排序
        console.warn('outboundTime字段索引不存在，使用id排序:', error);
        return await db.partsOutbound.orderBy('id').reverse().toArray();
    }
}

async function addPartsOutbound(outbound) {
    // 确保设置outboundTime字段
    if (!outbound.outboundTime) {
        outbound.outboundTime = new Date().toISOString();
    }
    return await db.partsOutbound.add(outbound);
}

async function updatePartsOutbound(id, outbound) {
    return await db.partsOutbound.update(id, outbound);
}

async function deletePartsOutbound(id) {
    return await db.partsOutbound.delete(id);
}

async function getPartsOutboundById(id) {
    return await db.partsOutbound.get(id);
}

// 配件借出归还相关函数
async function getAllPartsLending() {
    return await db.partsLending.orderBy('lendDate').reverse().toArray();
}

async function addPartsLending(lending) {
    return await db.partsLending.add(lending);
}

async function updatePartsLending(id, lending) {
    return await db.partsLending.update(id, lending);
}

async function deletePartsLending(id) {
    return await db.partsLending.delete(id);
}

async function getPartsLendingById(id) {
    return await db.partsLending.get(id);
}

// 线索渠道管理函数
async function getAllLeadChannels() {
    return await db.leadChannels.orderBy('id').toArray();
}

async function addLeadChannel(channel) {
    return await db.leadChannels.add(channel);
}

async function updateLeadChannel(id, updates) {
    return await db.leadChannels.update(id, updates);
}

async function getLeadChannelById(id) {
    return await db.leadChannels.get(id);
}

async function deleteLeadChannel(id) {
    return await db.leadChannels.delete(id);
}

// 根据属性获取配件
async function getPartsByAttribute(attribute) {
    return await db.partsSettings.where('attribute').equals(attribute).toArray();
}

// 批量导入函数 - 用于数据恢复
async function clearAndAddInputPersonnel(data) {
    await db.inputPersonnel.clear();
    if (data && data.length > 0) {
        await db.inputPersonnel.bulkAdd(data);
    }
}

async function clearAndAddSalesAdvisors(data) {
    await db.salesAdvisors.clear();
    if (data && data.length > 0) {
        await db.salesAdvisors.bulkAdd(data);
    }
}

async function clearAndAddCarModels(data) {
    await db.carModels.clear();
    if (data && data.length > 0) {
        await db.carModels.bulkAdd(data);
    }
}

async function clearAndAddIntentions(data) {
    await db.intentions.clear();
    if (data && data.length > 0) {
        await db.intentions.bulkAdd(data);
    }
}

async function clearAndAddCompetitors(data) {
    await db.competitors.clear();
    if (data && data.length > 0) {
        await db.competitors.bulkAdd(data);
    }
}

async function clearAndAddRegions(data) {
    await db.regions.clear();
    if (data && data.length > 0) {
        await db.regions.bulkAdd(data);
    }
}

async function clearAndAddChannels(data) {
    await db.channels.clear();
    if (data && data.length > 0) {
        await db.channels.bulkAdd(data);
    }
}

async function clearAndAddLeadChannels(data) {
    await db.leadChannels.clear();
    if (data && data.length > 0) {
        await db.leadChannels.bulkAdd(data);
    }
}

async function clearAndAddVisitTypes(data) {
    await db.visitTypes.clear();
    if (data && data.length > 0) {
        await db.visitTypes.bulkAdd(data);
    }
}

async function clearAndAddTestDriveModels(data) {
    await db.testDriveModels.clear();
    if (data && data.length > 0) {
        await db.testDriveModels.bulkAdd(data);
    }
}

async function clearAndAddDefinitions(data) {
    await db.definitions.clear();
    if (data && data.length > 0) {
        await db.definitions.bulkAdd(data);
    }
}

async function clearAndAddCustomerManagement(data) {
    await db.customerManagement.clear();
    if (data && data.length > 0) {
        await db.customerManagement.bulkAdd(data);
    }
}

async function clearAndAddShowroomEntries(data) {
    await db.showroomEntries.clear();
    if (data && data.length > 0) {
        await db.showroomEntries.bulkAdd(data);
    }
}

async function clearAndAddLeadEntries(data) {
    await db.leadEntries.clear();
    if (data && data.length > 0) {
        await db.leadEntries.bulkAdd(data);
    }
}

async function clearAndAddOrderManagement(data) {
    await db.orderManagement.clear();
    if (data && data.length > 0) {
        await db.orderManagement.bulkAdd(data);
    }
}

async function clearAndAddDeliveryManagement(data) {
    await db.deliveryManagement.clear();
    if (data && data.length > 0) {
        await db.deliveryManagement.bulkAdd(data);
    }
}

async function clearAndAddInventoryManagement(data) {
    await db.inventoryManagement.clear();
    if (data && data.length > 0) {
        await db.inventoryManagement.bulkAdd(data);
    }
}

async function clearAndAddPartsSettings(data) {
    await db.partsSettings.clear();
    if (data && data.length > 0) {
        await db.partsSettings.bulkAdd(data);
    }
}

async function clearAndAddPartsInbound(data) {
    await db.partsInbound.clear();
    if (data && data.length > 0) {
        await db.partsInbound.bulkAdd(data);
    }
}

async function clearAndAddPartsOutbound(data) {
    await db.partsOutbound.clear();
    if (data && data.length > 0) {
        await db.partsOutbound.bulkAdd(data);
    }
}

async function clearAndAddPartsLending(data) {
    await db.partsLending.clear();
    if (data && data.length > 0) {
        await db.partsLending.bulkAdd(data);
    }
}

// 确保函数立即可用
console.log('数据库模块已加载');