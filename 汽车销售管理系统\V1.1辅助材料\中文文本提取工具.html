<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文文本提取工具 - 去除数字符号只保留中文</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .container {
            width: 100%;
            max-width: 900px;
            margin: 20px auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(to right, #4e54c8, #8f94fb);
            color: white;
            padding: 25px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><path d="M0,0 L100,0 L100,100 Z" fill="rgba(255,255,255,0.1)"/></svg>');
            background-size: 100% 100%;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
            position: relative;
        }
        
        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            position: relative;
        }
        
        .content {
            padding: 30px;
        }
        
        .input-section, .output-section {
            margin-bottom: 30px;
        }
        
        h2 {
            color: #4e54c8;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #8f94fb;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        h2 i {
            font-size: 1.4rem;
        }
        
        textarea {
            width: 100%;
            height: 150px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            resize: vertical;
            font-size: 16px;
            transition: all 0.3s;
            background: #f8f9ff;
        }
        
        textarea:focus {
            border-color: #8f94fb;
            outline: none;
            box-shadow: 0 0 0 3px rgba(143, 148, 251, 0.2);
            background: #fff;
        }
        
        .note {
            background: #edf0ff;
            padding: 12px 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-size: 0.9rem;
            color: #4e54c8;
            border-left: 4px solid #8f94fb;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(to right, #4e54c8, #8f94fb);
            color: white;
            flex: 1;
        }
        
        .btn-secondary {
            background: #f1f1f1;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary:hover {
            background: linear-gradient(to right, #4348b0, #7e83e6);
        }
        
        .btn-secondary:hover {
            background: #e5e5e5;
        }
        
        .output-area {
            position: relative;
        }
        
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 8px 15px;
            background: rgba(78, 84, 200, 0.1);
            color: #4e54c8;
            border: 1px solid #8f94fb;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .copy-btn:hover {
            background: rgba(78, 84, 200, 0.2);
        }
        
        footer {
            text-align: center;
            padding: 20px;
            color: #777;
            font-size: 0.9rem;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }
        
        .example {
            background: #f0f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-size: 0.9rem;
            border-left: 4px solid #8f94fb;
        }
        
        .example h3 {
            color: #4e54c8;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .example-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .example-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .example-item .before {
            color: #e74c3c;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .example-item .after {
            color: #27ae60;
            font-weight: 500;
        }
        
        .example-item .arrow {
            text-align: center;
            color: #7f8c8d;
            margin: 5px 0;
        }
        
        .success-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transform: translateX(200%);
            transition: transform 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .success-message.show {
            transform: translateX(0);
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .buttons {
                flex-direction: column;
            }
            
            .example-content {
                grid-template-columns: 1fr;
            }
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .logo-icon {
            background: linear-gradient(to right, #4e54c8, #8f94fb);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div class="logo">
        <div class="logo-icon">
            <i class="fas fa-language"></i>
        </div>
        <h1>中文文本提取工具</h1>
    </div>
    
    <div class="container">
        <header>
            <h1>去除数字符号只保留中文</h1>
            <p class="subtitle">批量处理文本，去除开头的数字和符号，只保留中文文字内容</p>
        </header>
        
        <div class="content">
            <section class="input-section">
                <h2><i class="fas fa-keyboard"></i>输入文本（每行一条）</h2>
                <textarea id="inputText" placeholder="请输入要处理的文本，每行一条&#10;例如：&#10;1、车网垂媒&#10;2. 金融保险&#10;3) 医疗健康"></textarea>
                
                <div class="note">
                    <strong>提示：</strong> 
                    该工具会移除每行开头的数字、标点符号和空格，只保留后面的中文文字内容
                </div>
                
                <div class="buttons">
                    <button class="btn btn-primary" onclick="extractChinese()">
                        <i class="fas fa-magic"></i>
                        提取中文文本
                    </button>
                    <button class="btn btn-secondary" onclick="clearAll()">
                        <i class="fas fa-trash-alt"></i>
                        清空全部
                    </button>
                    <button class="btn btn-secondary" onclick="loadExample()">
                        <i class="fas fa-lightbulb"></i>
                        加载示例
                    </button>
                </div>
            </section>
            
            <section class="output-section">
                <h2><i class="fas fa-file-alt"></i>处理结果</h2>
                <div class="output-area">
                    <textarea id="outputText" readonly placeholder="处理结果将显示在这里"></textarea>
                    <button class="copy-btn" onclick="copyToClipboard()">
                        <i class="fas fa-copy"></i>
                        复制结果
                    </button>
                </div>
            </section>
            
            <div class="example">
                <h3><i class="fas fa-list"></i> 示例说明</h3>
                <div class="example-content">
                    <div class="example-item">
                        <div class="before">原始文本: 1、车网垂媒</div>
                        <div class="arrow">↓</div>
                        <div class="after">提取结果: 车网垂媒</div>
                    </div>
                    <div class="example-item">
                        <div class="before">原始文本: 2. 金融保险</div>
                        <div class="arrow">↓</div>
                        <div class="after">提取结果: 金融保险</div>
                    </div>
                    <div class="example-item">
                        <div class="before">原始文本: 3) 医疗健康</div>
                        <div class="arrow">↓</div>
                        <div class="after">提取结果: 医疗健康</div>
                    </div>
                    <div class="example-item">
                        <div class="before">原始文本: 4、 教育培训</div>
                        <div class="arrow">↓</div>
                        <div class="after">提取结果: 教育培训</div>
                    </div>
                </div>
            </div>
        </div>
        
        <footer>
            <p>© 2023 中文文本提取工具 | 高效处理文本数据</p>
        </footer>
    </div>
    
    <div class="success-message" id="successMessage">
        <i class="fas fa-check-circle"></i>
        结果已复制到剪贴板！
    </div>
    
    <script>
        function extractChinese() {
            const inputText = document.getElementById('inputText').value;
            const outputText = document.getElementById('outputText');
            
            if (!inputText.trim()) {
                outputText.value = '请输入要处理的文本';
                return;
            }
            
            const lines = inputText.split('\n');
            let results = [];
            
            for (let line of lines) {
                line = line.trim();
                if (!line) {
                    results.push('');
                    continue;
                }
                
                // 使用正则表达式去除开头的数字、符号和空格
                const result = line.replace(/^[\d\s、.)(·\-]+/, '').trim();
                
                if (result) {
                    results.push(result);
                } else {
                    results.push('（未检测到中文内容）');
                }
            }
            
            outputText.value = results.join('\n');
        }
        
        function clearAll() {
            document.getElementById('inputText').value = '';
            document.getElementById('outputText').value = '';
        }
        
        function loadExample() {
            const example = 
                `1、车网垂媒
2. 金融保险
3) 医疗健康
4、 教育培训
5.互联网科技
6) 房地产
7、 餐饮服务
8. 文化娱乐
9) 物流运输
10、 能源环保`;
            
            document.getElementById('inputText').value = example;
        }
        
        function copyToClipboard() {
            const outputText = document.getElementById('outputText');
            if (outputText.value) {
                outputText.select();
                document.execCommand('copy');
                
                // 显示成功消息
                const message = document.getElementById('successMessage');
                message.classList.add('show');
                
                setTimeout(() => {
                    message.classList.remove('show');
                }, 2000);
            }
        }
        
        // 初始加载示例
        window.onload = loadExample;
    </script>
</body>
</html>