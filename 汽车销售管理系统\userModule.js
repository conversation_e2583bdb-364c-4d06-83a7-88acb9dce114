// 用户管理功能模块
window.userFunctions = {
    allUsers: [],
    
    // 加载用户管理模块
    loadUsers: async function() {
        try {
            this.allUsers = await window.dbFunctions.getAllUserManagement();
            this.renderUserContent();
            this.updateUserCount();
            this.bindFilterEvents();
        } catch (error) {
            console.error('加载用户数据失败:', error);
        }
    },

    // 更新用户总数显示
    updateUserCount: function() {
        const totalUsersElement = document.getElementById('total-users');
        if (totalUsersElement) {
            totalUsersElement.textContent = this.allUsers.length;
        }
    },

    // 绑定筛选事件
    bindFilterEvents: function() {
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 移除其他按钮的active状态
                filterBtns.forEach(b => b.classList.remove('active'));
                // 添加当前按钮的active状态
                e.target.classList.add('active');

                const status = e.target.dataset.status;
                this.filterUsers(status);
            });
        });
    },

    // 筛选用户
    filterUsers: function(status) {
        let filteredUsers = this.allUsers;

        if (status !== 'all') {
            filteredUsers = this.allUsers.filter(user => {
                if (status === 'enabled') {
                    return user.status === '启用';
                } else if (status === 'disabled') {
                    return user.status === '禁用';
                }
                return true;
            });
        }

        this.renderUserContent(filteredUsers);
    },
    
    // 渲染用户管理内容
    renderUserContent: function(users = null) {
        const displayUsers = users || this.allUsers;
        const moduleContainer = document.getElementById('user-module');
        if (!moduleContainer) return;

        moduleContainer.innerHTML = `
            <div class="card">
                <div class="tab-header">
                    <h2>用户列表 (${displayUsers.length})</h2>
                    <div>
                        <input type="text" id="user-search" placeholder="搜索账号、用户名或职位..." style="padding: 8px; border-radius: 4px; border: 1px solid #ddd; margin-right: 10px;">
                        <button class="btn btn-primary" id="new-user-btn">
                            <i class="fas fa-plus"></i> 新增用户
                        </button>
                    </div>
                </div>

                <div class="user-list-content" id="all-users">
                    ${this.renderUserList(displayUsers)}
                </div>
            </div>
        `;
        
        // 绑定事件
        this.bindEvents();
    },
    
    // 渲染用户列表
    renderUserList: function(users) {
        if (users.length === 0) {
            return '<div style="text-align: center; padding: 40px; color: var(--gray);">暂无用户数据</div>';
        }
        
        return `
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>账号</th>
                            <th>用户名字</th>
                            <th>职位</th>
                            <th>权限</th>
                            <th>状态</th>
                            <th>创建日期</th>
                            <th>最后登录</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map((user, index) => `
                            <tr>
                                <td>${user.serialNumber || index + 1}</td>
                                <td>${user.account || '-'}</td>
                                <td>${user.userName || '-'}</td>
                                <td>${user.position || '-'}</td>
                                <td>${this.renderPermissionTags(user.permissions)}</td>
                                <td><span class="status status-${user.status}">${user.status || '-'}</span></td>
                                <td>${user.createDate || '-'}</td>
                                <td>${user.lastLogin || '-'}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline" onclick="editUser(${user.id})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },
    
    // 渲染权限标签
    renderPermissionTags: function(permissions) {
        if (!permissions) return '-';
        
        const permissionArray = typeof permissions === 'string' ? permissions.split(',') : permissions;
        const permissionNames = {
            'index.html': '首页',
            'salesanalytics.html': '销售分析',
            'customers.showroom': '展厅录入',
            'customers.leads': '线索录入',
            'order.orders': '订单管理',
            'order.inventory': '库存管理',
            'order.delivery': '配车交付',
            'order.targets': '目标管理',
            'order.audit': '订单审核',
            'settings.html': '系统设置',
            'user.html': '用户管理'
        };
        
        return `
            <div class="permission-tags">
                ${permissionArray.map(perm => {
                    const className = perm.replace('.html', '');
                    const displayName = permissionNames[perm] || perm;
                    return `<span class="permission-tag ${className}">${displayName}</span>`;
                }).join('')}
            </div>
        `;
    },
    
    // 绑定事件
    bindEvents: function() {
        // 新增用户按钮
        const newUserBtn = document.getElementById('new-user-btn');
        if (newUserBtn) {
            newUserBtn.addEventListener('click', () => this.showUserForm());
        }
        
        // 搜索功能
        const searchInput = document.getElementById('user-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const filteredUsers = this.allUsers.filter(user =>
                    (user.account && user.account.toLowerCase().includes(searchTerm)) ||
                    (user.userName && user.userName.toLowerCase().includes(searchTerm)) ||
                    (user.position && user.position.toLowerCase().includes(searchTerm))
                );
                
                const listContainer = document.getElementById('all-users');
                if (listContainer) {
                    listContainer.innerHTML = this.renderUserList(filteredUsers);
                }
            });
        }
    },
    
    // 显示用户表单
    showUserForm: async function(editId = null) {
        try {
            let user = {
                serialNumber: '',
                account: '',
                password: '',
                userName: '',
                position: '',
                permissions: [],
                status: '启用'
            };
            
            if (editId !== null) {
                user = await window.dbFunctions.getUserManagementById(editId) || user;
                if (typeof user.permissions === 'string') {
                    user.permissions = user.permissions.split(',');
                }
            }
            
            this.showModal('用户管理', this.renderUserForm(user, editId));
        } catch (error) {
            console.error('显示用户表单失败:', error);
            alert('显示用户表单失败: ' + error.message);
        }
    },
    
    // 显示模态框
    showModal: function(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    },
    
    // 渲染用户表单
    renderUserForm: function(user, editId) {
        const permissions = [
            { value: 'index.html', label: '首页', group: '基础功能' },
            { value: 'salesanalytics.html', label: '销售分析', group: '基础功能' },
            { value: 'customers.showroom', label: '展厅录入', group: '客户管理' },
            { value: 'customers.leads', label: '线索录入', group: '客户管理' },
            { value: 'order.orders', label: '订单管理', group: '订单管理' },
            { value: 'order.inventory', label: '库存管理', group: '订单管理' },
            { value: 'order.delivery', label: '配车交付', group: '订单管理' },
            { value: 'order.targets', label: '目标管理', group: '订单管理' },
            { value: 'order.audit', label: '订单审核', group: '订单管理' },
            { value: 'settings.html', label: '系统设置', group: '系统管理' },
            { value: 'user.html', label: '用户管理', group: '系统管理' }
        ];

        // 按组分组权限
        const groupedPermissions = permissions.reduce((groups, perm) => {
            if (!groups[perm.group]) {
                groups[perm.group] = [];
            }
            groups[perm.group].push(perm);
            return groups;
        }, {});
        
        return `
            <form id="user-form">
                <div class="form-group">
                    <label>序号</label>
                    <input type="text" name="serialNumber" value="${user.serialNumber || ''}" placeholder="自动生成">
                </div>
                <div class="form-group">
                    <label>账号 *</label>
                    <input type="text" name="account" value="${user.account || ''}" required placeholder="请输入账号">
                </div>
                <div class="form-group">
                    <label>密码 *</label>
                    <input type="password" name="password" value="${user.password || ''}" required placeholder="请输入密码">
                </div>
                <div class="form-group">
                    <label>用户名字 *</label>
                    <input type="text" name="userName" value="${user.userName || ''}" required placeholder="请输入用户名字">
                </div>
                <div class="form-group">
                    <label>职位</label>
                    <input type="text" name="position" value="${user.position || ''}" placeholder="请输入职位">
                </div>
                <div class="form-group" style="grid-column: 1 / -1;">
                    <label>权限</label>
                    <div class="permission-groups">
                        ${Object.keys(groupedPermissions).map(groupName => `
                            <div class="permission-group">
                                <h4 style="margin: 10px 0 5px 0; color: #495057; font-size: 14px;">${groupName}</h4>
                                <div class="permission-checkboxes">
                                    ${groupedPermissions[groupName].map(perm => `
                                        <div class="permission-checkbox">
                                            <input type="checkbox" name="permissions" value="${perm.value}"
                                                ${user.permissions && user.permissions.includes(perm.value) ? 'checked' : ''}>
                                            <label>${perm.label}</label>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="form-group">
                    <label>状态</label>
                    <select name="status">
                        <option value="启用" ${user.status === '启用' ? 'selected' : ''}>启用</option>
                        <option value="禁用" ${user.status === '禁用' ? 'selected' : ''}>禁用</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-primary" onclick="userFunctions.saveUser(${editId})">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </form>
        `;
    },

    // 保存用户
    saveUser: async function(editId) {
        try {
            const form = document.getElementById('user-form');
            const formData = new FormData(form);
            const userData = {};

            // 处理普通字段
            for (let [key, value] of formData.entries()) {
                if (key !== 'permissions') {
                    userData[key] = value;
                }
            }

            // 处理权限字段（多选）
            const permissionCheckboxes = form.querySelectorAll('input[name="permissions"]:checked');
            userData.permissions = Array.from(permissionCheckboxes).map(cb => cb.value).join(',');

            // 验证必填字段
            if (!userData.account || !userData.password || !userData.userName) {
                alert('请填写必填字段');
                return;
            }

            // 验证账号格式（只允许字母、数字、下划线）
            const accountRegex = /^[a-zA-Z0-9_]+$/;
            if (!accountRegex.test(userData.account)) {
                alert('账号只能包含字母、数字和下划线');
                return;
            }

            // 验证密码强度（至少6位）
            if (userData.password.length < 6) {
                alert('密码长度至少6位');
                return;
            }

            if (editId !== null) {
                await window.dbFunctions.updateUserManagement(editId, userData);
                alert('用户更新成功');
            } else {
                await window.dbFunctions.addUserManagement(userData);
                alert('用户添加成功');
            }

            // 关闭模态框
            document.querySelector('.modal-overlay').remove();

            // 重新加载数据
            this.allUsers = await window.dbFunctions.getAllUserManagement();
            this.renderUserContent();

        } catch (error) {
            console.error('保存用户失败:', error);
            alert('保存用户失败: ' + error.message);
        }
    }
};

// 全局函数
function editUser(userId) {
    window.userFunctions.showUserForm(userId);
}

function deleteUser(userId) {
    if (confirm('确定要删除这个用户吗？')) {
        window.dbFunctions.deleteUserManagement(userId)
            .then(() => {
                alert('用户删除成功');
                window.userFunctions.loadUsers();
            })
            .catch(error => {
                console.error('删除用户失败:', error);
                alert('删除用户失败: ' + error.message);
            });
    }
}
