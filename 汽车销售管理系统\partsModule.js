// 配件库管理模块
window.partsFunctions = {
    // 缓存数据
    allPartsSettings: [],
    allPartsRequests: [],
    allPartsInbound: [],
    allPartsOutbound: [],
    allPartsLending: [],
    allOrders: [],
    currentTab: 'requests',

    // 统一日期格式化函数 - YYYY/M/DD
    formatDate: function(dateString) {
        if (!dateString) return '';

        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString; // 如果不是有效日期，返回原值

            const year = date.getFullYear();
            const month = date.getMonth() + 1; // 月份从0开始，需要+1
            const day = date.getDate();

            return `${year}/${month}/${day}`;
        } catch (error) {
            return dateString; // 出错时返回原值
        }
    },

    // 入库管理筛选和分页状态
    inboundFilters: {
        type: '',
        partCode: '',
        partName: '',
        attribute: ''
    },
    inboundPagination: {
        currentPage: 1,
        pageSize: 50,
        totalPages: 1,
        totalRecords: 0
    },
    inboundColumnSettings: {
        inboundTime: true,
        stockAge: true,
        type: true,
        attribute: true,
        locationCode: true,
        partCode: true,
        partName: true,
        notes: false,
        totalStock: true,
        availableStock: true,
        reservedStock: true,
        lentStock: true,
        salePrice: false,
        guidePrice: false,
        insurancePrice: false,
        costPrice: false,
        alternatePart: false
    },

    // 初始化配件库管理模块
    init: async function() {
        try {
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();
            console.log('配件库管理模块初始化完成');
        } catch (error) {
            console.error('配件库管理模块初始化失败:', error);
            throw error;
        }
    },

    // 加载所有数据
    loadAllData: async function() {
        try {
            const [
                partsSettings,
                partsRequests,
                partsInbound,
                partsOutbound,
                partsLending,
                orders
            ] = await Promise.all([
                window.dbFunctions.getAllPartsSettings(),
                window.dbFunctions.getAllPartsRequests(),
                window.dbFunctions.getAllPartsInbound(),
                window.dbFunctions.getAllPartsOutbound(),
                window.dbFunctions.getAllPartsLending(),
                window.dbFunctions.getAllOrderManagement()
            ]);

            this.allPartsSettings = partsSettings;
            this.allPartsRequests = partsRequests;
            this.allPartsInbound = partsInbound;
            this.allPartsOutbound = partsOutbound;
            this.allPartsLending = partsLending;
            this.allOrders = orders;

            console.log('配件库数据加载完成:', {
                settings: this.allPartsSettings.length,
                requests: this.allPartsRequests.length,
                inbound: this.allPartsInbound.length,
                outbound: this.allPartsOutbound.length,
                lending: this.allPartsLending.length
            });
        } catch (error) {
            console.error('加载配件库数据失败:', error);
            throw error;
        }
    },

    // 切换标签页
    switchTab: function(tabName) {
        this.currentTab = tabName;
        this.renderCurrentModule();
    },

    // 渲染当前模块
    renderCurrentModule: function() {
        switch (this.currentTab) {
            case 'requests':
                this.renderRequestsModule();
                break;
            case 'inbound':
                this.renderInboundModule();
                break;
            case 'outbound':
                this.renderOutboundModule();
                break;
            case 'lending':
                this.renderLendingModule();
                break;
            case 'settings':
                this.renderSettingsModule();
                break;
            default:
                this.renderRequestsModule();
        }
    },

    // 渲染订单需求模块
    renderRequestsModule: function() {
        const container = document.getElementById('requests-module');
        if (!container) return;

        const pendingRequests = this.allPartsRequests.filter(req => req.status === '待匹配');

        container.innerHTML = `
            <div class="card">
                <div class="tab-header">
                    <h2><i class="fas fa-clipboard-list"></i> 订单需求管理</h2>
                    <div>
                        <button class="btn btn-primary" onclick="partsFunctions.generateRequestsFromOrders()">
                            <i class="fas fa-sync-alt"></i> 从订单生成需求
                        </button>
                    </div>
                </div>
                
                <div class="requests-content">
                    ${this.renderRequestsList(pendingRequests)}
                </div>
            </div>
        `;
    },

    // 渲染需求列表
    renderRequestsList: function(requests) {
        if (requests.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-clipboard-list" style="font-size: 48px; color: #dee2e6; margin-bottom: 16px;"></i>
                    <h3 style="color: #6c757d; margin-bottom: 8px;">暂无配件需求</h3>
                    <p style="color: #adb5bd;">点击"从订单生成需求"按钮自动生成配件需求</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>需求人员</th>
                            <th>类型</th>
                            <th>配件代码</th>
                            <th>配件名称</th>
                            <th>数量</th>
                            <th>创建时间</th>
                            <th>状态</th>
                            <th class="action-column">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${requests.map(request => `
                            <tr>
                                <td>${request.requestPerson || '-'}</td>
                                <td>${request.type || '-'}</td>
                                <td>${request.partCode || '-'}</td>
                                <td>${request.partName || '-'}</td>
                                <td>${request.quantity || 0}</td>
                                <td>${request.createTime || '-'}</td>
                                <td>
                                    <span class="status ${request.status === '待匹配' ? 'status-warning' : 'status-success'}">
                                        ${request.status || '待匹配'}
                                    </span>
                                </td>
                                <td class="action-column">
                                    <div class="action-buttons">
                                        ${request.status === '待匹配' ? `
                                            <button class="btn btn-sm btn-success" onclick="partsFunctions.matchRequest(${request.id})" title="匹配">
                                                <i class="fas fa-check"></i> 匹配
                                            </button>
                                        ` : ''}
                                        <button class="btn btn-sm btn-danger" onclick="partsFunctions.deleteRequest(${request.id})" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    // 渲染入库管理模块
    renderInboundModule: function() {
        const container = document.getElementById('inbound-module');
        if (!container) return;

        // 加载列设置
        this.loadInboundColumnSettings();

        container.innerHTML = `
            <div class="card">
                <div class="tab-header">
                    <h2><i class="fas fa-arrow-down"></i> 入库管理</h2>
                    <div class="header-actions">
                        <button class="btn btn-outline" onclick="partsFunctions.showInboundColumnSettings()" title="列设置">
                            <i class="fas fa-columns"></i> 列设置
                        </button>
                        <button class="btn btn-outline" onclick="partsFunctions.showImportInboundModal()" title="导入">
                            <i class="fas fa-upload"></i> 导入
                        </button>
                        <button class="btn btn-outline" onclick="partsFunctions.exportInboundToExcel()" title="导出">
                            <i class="fas fa-download"></i> 导出
                        </button>
                        <button class="btn btn-primary" onclick="partsFunctions.showInboundForm()">
                            <i class="fas fa-plus"></i> 新增入库
                        </button>
                    </div>
                </div>

                <!-- 搜索筛选区域 -->
                <div class="filter-section">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label>类型</label>
                            <select id="inbound-type-filter" onchange="partsFunctions.updateInboundFilter('type', this.value)">
                                <option value="">全部类型</option>
                                <option value="原厂件" ${this.inboundFilters.type === '原厂件' ? 'selected' : ''}>原厂件</option>
                                <option value="副厂件" ${this.inboundFilters.type === '副厂件' ? 'selected' : ''}>副厂件</option>
                                <option value="拆车件" ${this.inboundFilters.type === '拆车件' ? 'selected' : ''}>拆车件</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>属性</label>
                            <select id="inbound-attribute-filter" onchange="partsFunctions.updateInboundFilter('attribute', this.value)">
                                <option value="">全部属性</option>
                                <option value="配件" ${this.inboundFilters.attribute === '配件' ? 'selected' : ''}>配件</option>
                                <option value="精品" ${this.inboundFilters.attribute === '精品' ? 'selected' : ''}>精品</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>配件代码</label>
                            <input type="text" id="inbound-partcode-filter" placeholder="输入配件代码"
                                   value="${this.inboundFilters.partCode}"
                                   oninput="partsFunctions.updateInboundFilter('partCode', this.value)">
                        </div>
                        <div class="filter-group">
                            <label>配件名称</label>
                            <input type="text" id="inbound-partname-filter" placeholder="输入配件名称"
                                   value="${this.inboundFilters.partName}"
                                   oninput="partsFunctions.updateInboundFilter('partName', this.value)">
                        </div>
                        <div class="filter-group">
                            <button class="btn btn-secondary" onclick="partsFunctions.resetInboundFilters()">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>

                <div class="inbound-content">
                    ${this.renderInboundList()}
                </div>
            </div>
        `;
    },

    // 渲染入库列表
    renderInboundList: function() {
        // 获取筛选后的数据
        const filteredData = this.getFilteredInboundData();

        // 更新分页信息
        this.inboundPagination.totalRecords = filteredData.length;
        this.inboundPagination.totalPages = Math.ceil(filteredData.length / this.inboundPagination.pageSize);

        // 获取当前页数据
        const startIndex = (this.inboundPagination.currentPage - 1) * this.inboundPagination.pageSize;
        const endIndex = startIndex + this.inboundPagination.pageSize;
        const currentPageData = filteredData.slice(startIndex, endIndex);

        if (filteredData.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-arrow-down" style="font-size: 48px; color: #dee2e6; margin-bottom: 16px;"></i>
                    <h3 style="color: #6c757d; margin-bottom: 8px;">暂无入库记录</h3>
                    <p style="color: #adb5bd;">点击"新增入库"按钮添加配件入库记录</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            ${this.inboundColumnSettings.inboundTime ? '<th>入库时间</th>' : ''}
                            ${this.inboundColumnSettings.stockAge ? '<th>库龄</th>' : ''}
                            ${this.inboundColumnSettings.type ? '<th>类型</th>' : ''}
                            ${this.inboundColumnSettings.attribute ? '<th>属性</th>' : ''}
                            ${this.inboundColumnSettings.locationCode ? '<th>库位代码</th>' : ''}
                            ${this.inboundColumnSettings.partCode ? '<th>配件代码</th>' : ''}
                            ${this.inboundColumnSettings.partName ? '<th>配件名称</th>' : ''}
                            ${this.inboundColumnSettings.notes ? '<th>备注</th>' : ''}
                            ${this.inboundColumnSettings.totalStock ? '<th>库存总数</th>' : ''}
                            ${this.inboundColumnSettings.availableStock ? '<th>可用库存</th>' : ''}
                            ${this.inboundColumnSettings.reservedStock ? '<th>预订库存</th>' : ''}
                            ${this.inboundColumnSettings.lentStock ? '<th>借出库存</th>' : ''}
                            ${this.inboundColumnSettings.salePrice ? '<th>销售价</th>' : ''}
                            ${this.inboundColumnSettings.guidePrice ? '<th>指导价</th>' : ''}
                            ${this.inboundColumnSettings.insurancePrice ? '<th>保险价</th>' : ''}
                            ${this.inboundColumnSettings.costPrice ? '<th>成本价</th>' : ''}
                            ${this.inboundColumnSettings.alternatePart ? '<th>替代配件</th>' : ''}
                            <th class="action-column">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${currentPageData.map(inbound => {
                            const stockAge = this.calculateStockAge(inbound.inboundTime);
                            const availableStock = this.calculateAvailableStock(inbound);
                            const reservedStock = this.calculateReservedStock(inbound.partCode);
                            const lentStock = this.calculateLentStock(inbound.partCode);

                            return `
                                <tr>
                                    ${this.inboundColumnSettings.inboundTime ? `<td>${this.formatDate(inbound.inboundTime) || '-'}</td>` : ''}
                                    ${this.inboundColumnSettings.stockAge ? `<td>${stockAge}天</td>` : ''}
                                    ${this.inboundColumnSettings.type ? `<td>${inbound.type || '-'}</td>` : ''}
                                    ${this.inboundColumnSettings.attribute ? `<td>${inbound.attribute || '-'}</td>` : ''}
                                    ${this.inboundColumnSettings.locationCode ? `<td>${inbound.locationCode || '-'}</td>` : ''}
                                    ${this.inboundColumnSettings.partCode ? `<td>${inbound.partCode || '-'}</td>` : ''}
                                    ${this.inboundColumnSettings.partName ? `<td>${inbound.partName || '-'}</td>` : ''}
                                    ${this.inboundColumnSettings.notes ? `<td>${inbound.notes || '-'}</td>` : ''}
                                    ${this.inboundColumnSettings.totalStock ? `<td>${inbound.totalStock || 0}</td>` : ''}
                                    ${this.inboundColumnSettings.availableStock ? `<td class="text-success">${availableStock}</td>` : ''}
                                    ${this.inboundColumnSettings.reservedStock ? `<td class="text-warning">${reservedStock}</td>` : ''}
                                    ${this.inboundColumnSettings.lentStock ? `<td class="text-danger">${lentStock}</td>` : ''}
                                    ${this.inboundColumnSettings.salePrice ? `<td>¥${inbound.salePrice || 0}</td>` : ''}
                                    ${this.inboundColumnSettings.guidePrice ? `<td>¥${inbound.guidePrice || 0}</td>` : ''}
                                    ${this.inboundColumnSettings.insurancePrice ? `<td>¥${inbound.insurancePrice || 0}</td>` : ''}
                                    ${this.inboundColumnSettings.costPrice ? `<td>¥${inbound.costPrice || 0}</td>` : ''}
                                    ${this.inboundColumnSettings.alternatePart ? `<td>${inbound.alternatePart || '-'}</td>` : ''}
                                    <td class="action-column">
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-outline" onclick="partsFunctions.editInbound(${inbound.id})" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="partsFunctions.deleteInbound(${inbound.id})" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
            ${this.renderInboundPagination()}
        `;
    },

    // 渲染出库管理模块
    renderOutboundModule: function() {
        const container = document.getElementById('outbound-module');
        if (!container) return;

        container.innerHTML = `
            <div class="card">
                <div class="tab-header">
                    <h2><i class="fas fa-arrow-up"></i> 出库管理</h2>
                    <div>
                        <button class="btn btn-primary" onclick="partsFunctions.showOutboundForm()">
                            <i class="fas fa-plus"></i> 新增出库
                        </button>
                        <button class="btn btn-secondary" onclick="partsFunctions.generateOutboundFromOrders()">
                            <i class="fas fa-sync-alt"></i> 从订单生成
                        </button>
                    </div>
                </div>
                
                <div class="outbound-content">
                    ${this.renderOutboundList(this.allPartsOutbound)}
                </div>
            </div>
        `;
    },

    // 渲染出库列表
    renderOutboundList: function(outboundList) {
        if (outboundList.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-arrow-up" style="font-size: 48px; color: #dee2e6; margin-bottom: 16px;"></i>
                    <h3 style="color: #6c757d; margin-bottom: 8px;">暂无出库记录</h3>
                    <p style="color: #adb5bd;">点击"新增出库"按钮添加配件出库记录</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>出库日期</th>
                            <th>人员</th>
                            <th>客户名称</th>
                            <th>VIN</th>
                            <th>配件代码</th>
                            <th>配件名称</th>
                            <th>数量</th>
                            <th>销售价</th>
                            <th>库存总数</th>
                            <th>可用库存</th>
                            <th>预订库存</th>
                            <th>借出库存</th>
                            <th>状态</th>
                            <th class="action-column">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${outboundList.map(outbound => {
                            // 获取库存信息
                            const stockInfo = this.getPartStockInfo(outbound.partCode);
                            return `
                                <tr>
                                    <td>${this.formatOutboundDate(outbound.outboundTime)}</td>
                                    <td>${outbound.personnelName || '-'}</td>
                                    <td>${outbound.customerName || '-'}</td>
                                    <td>${outbound.vin || '-'}</td>
                                    <td>${outbound.partCode || '-'}</td>
                                    <td>${outbound.partName || '-'}</td>
                                    <td>${outbound.quantity || 0}</td>
                                    <td>¥${outbound.salePrice || 0}</td>
                                    <td>${stockInfo.totalStock}</td>
                                    <td>${stockInfo.availableStock}</td>
                                    <td>${stockInfo.reservedStock}</td>
                                    <td>${stockInfo.lentStock}</td>
                                    <td>
                                        <span class="status ${this.getOutboundStatusClass(outbound.status)}">
                                            ${outbound.status || '待出库'}
                                        </span>
                                    </td>
                                    <td class="action-column">
                                        <div class="action-buttons">
                                            ${outbound.status === '待出库' ? `
                                                <button class="btn btn-sm btn-success" onclick="partsFunctions.confirmOutbound(${outbound.id})" title="确认出库">
                                                    <i class="fas fa-check"></i> 出库
                                                </button>
                                                <button class="btn btn-sm btn-outline" onclick="partsFunctions.editOutbound(${outbound.id})" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            ` : outbound.status === '已出库' ? `
                                                <button class="btn btn-sm btn-warning" onclick="partsFunctions.returnOutbound(${outbound.id})" title="返出库">
                                                    <i class="fas fa-undo"></i> 返出库
                                                </button>
                                            ` : ''}
                                            <button class="btn btn-sm btn-danger" onclick="partsFunctions.deleteOutbound(${outbound.id})" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    // 获取配件库存信息
    getPartStockInfo: function(partCode) {
        if (!partCode) {
            return {
                totalStock: 0,
                availableStock: 0,
                reservedStock: 0,
                lentStock: 0
            };
        }

        // 计算总库存
        const totalStock = this.allPartsInbound
            .filter(inbound => inbound.partCode === partCode)
            .reduce((sum, inbound) => sum + (inbound.totalStock || 0), 0);

        // 计算可用库存
        const availableStock = this.calculateTotalAvailableStock(partCode);

        // 计算预订库存
        const reservedStock = this.calculateReservedStock(partCode);

        // 计算借出库存
        const lentStock = this.calculateLentStock(partCode);

        return {
            totalStock,
            availableStock,
            reservedStock,
            lentStock
        };
    },

    // 格式化出库日期 - YYYY/M/DD
    formatOutboundDate: function(outboundTime) {
        if (!outboundTime) return '-';

        try {
            const date = new Date(outboundTime);
            if (isNaN(date.getTime())) return outboundTime;

            const year = date.getFullYear();
            const month = date.getMonth() + 1; // 不补零
            const day = date.getDate(); // 不补零
            return `${year}/${month}/${day}`;
        } catch (error) {
            return outboundTime;
        }
    },

    // 更新侧边栏统计信息
    updateSidebarStats: function() {
        // 计算总配件数
        const totalParts = this.allPartsInbound.reduce((sum, item) => sum + (item.totalStock || 0), 0);
        document.getElementById('total-parts').textContent = totalParts;

        // 计算可用库存
        const availableStock = this.allPartsInbound.reduce((sum, item) => {
            return sum + this.calculateAvailableStock(item);
        }, 0);
        document.getElementById('available-stock').textContent = availableStock;

        // 计算预订库存
        const reservedStock = this.allPartsInbound.reduce((sum, item) => {
            return sum + this.calculateReservedStock(item.partCode);
        }, 0);
        document.getElementById('reserved-stock').textContent = reservedStock;

        // 计算借出库存
        const lentStock = this.allPartsInbound.reduce((sum, item) => {
            return sum + this.calculateLentStock(item.partCode);
        }, 0);
        document.getElementById('lent-stock').textContent = lentStock;

        // 计算待匹配需求
        const pendingRequests = this.allPartsRequests.filter(req => req.status === '待匹配').length;
        document.getElementById('pending-requests').textContent = pendingRequests;
    },

    // 计算库龄
    calculateStockAge: function(inboundTime) {
        if (!inboundTime) return 0;
        const inboundDate = new Date(inboundTime);
        const today = new Date();
        const diffTime = Math.abs(today - inboundDate);
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    },

    // 计算可用库存
    calculateAvailableStock: function(inbound) {
        const totalStock = inbound.totalStock || 0;
        const reservedStock = this.calculateReservedStock(inbound.partCode);
        const lentStock = this.calculateLentStock(inbound.partCode);
        return Math.max(0, totalStock - reservedStock - lentStock);
    },

    // 计算预订库存
    calculateReservedStock: function(partCode) {
        // 从待出库的出库记录中计算预订数量
        const outboundReserved = this.allPartsOutbound
            .filter(outbound => outbound.partCode === partCode && outbound.status === '待出库')
            .reduce((sum, outbound) => sum + (outbound.quantity || 0), 0);

        // 从已审核但未交付的订单中计算预订数量
        const orderReserved = this.calculateOrderReservedStock(partCode);

        return outboundReserved + orderReserved;
    },

    // 计算订单预定库存
    calculateOrderReservedStock: function(partCode) {
        let reservedQuantity = 0;

        // 遍历所有已审核但未交付的订单
        const approvedOrders = this.allOrders.filter(order =>
            order.auditStatus === '已审核' &&
            order.deliveryStatus !== '已交付' &&
            order.options
        );

        for (const order of approvedOrders) {
            if (!order.options) continue;
            const options = order.options.split(',').map(opt => opt.trim()).filter(opt => opt);

            for (const option of options) {
                let partName = option;
                let quantity = 1;

                // 解析数量
                if (option.includes(' x')) {
                    const parts = option.split(' x');
                    partName = parts[0].trim();
                    quantity = parseInt(parts[1]) || 1;
                }

                // 通过配件名称查找对应的配件代码
                const partSetting = this.allPartsSettings.find(setting =>
                    setting.partName === partName
                );

                // 如果找到匹配的配件且配件代码匹配
                if (partSetting && partSetting.partCode === partCode) {
                    reservedQuantity += quantity;
                }

                // 也支持直接通过配件代码匹配
                if (partName === partCode) {
                    reservedQuantity += quantity;
                }
            }
        }

        return reservedQuantity;
    },

    // 刷新库存计算（当订单状态变化时调用）
    refreshStockCalculations: async function() {
        try {
            // 重新加载订单数据
            this.allOrders = await window.dbFunctions.getAllOrderManagement();

            // 重新渲染当前模块以更新库存显示
            if (this.currentTab === 'inbound') {
                this.renderInboundManagement();
            }

            // 更新侧边栏统计
            this.updateSidebarStats();

            // 检查并自动生成缺货需求
            await this.checkAndGenerateShortageRequests();

            console.log('库存计算已刷新');
        } catch (error) {
            console.error('刷新库存计算失败:', error);
        }
    },

    // 检查并自动生成缺货需求
    checkAndGenerateShortageRequests: async function() {
        try {
            // 获取所有已审核订单的选装件需求
            const approvedOrders = this.allOrders.filter(order => order.auditStatus === '已审核');
            const partDemands = new Map(); // 配件名称 -> 总需求量

            // 统计所有已审核订单的配件需求
            for (const order of approvedOrders) {
                if (!order.options) continue;

                const options = order.options.split(',').map(opt => opt.trim()).filter(opt => opt);

                for (const option of options) {
                    let partName = option;
                    let quantity = 1;

                    // 解析数量
                    if (option.includes(' x')) {
                        const parts = option.split(' x');
                        partName = parts[0].trim();
                        quantity = parseInt(parts[1]) || 1;
                    }

                    // 累计需求量
                    const currentDemand = partDemands.get(partName) || 0;
                    partDemands.set(partName, currentDemand + quantity);
                }
            }

            // 检查每个配件的库存是否满足需求
            for (const [partName, totalDemand] of partDemands) {
                // 计算该配件的总可用库存
                const availableStock = this.calculateTotalAvailableStockByName(partName);

                // 如果需求量超过可用库存，生成缺货需求
                if (totalDemand > availableStock) {
                    const shortageQuantity = totalDemand - availableStock;
                    await this.generateShortageRequest(partName, shortageQuantity, totalDemand, availableStock);
                }
            }

        } catch (error) {
            console.error('检查缺货需求失败:', error);
        }
    },

    // 根据配件名称计算总可用库存
    calculateTotalAvailableStockByName: function(partName) {
        return this.allPartsInbound
            .filter(inbound => inbound.partName === partName)
            .reduce((sum, inbound) => sum + this.calculateAvailableStock(inbound), 0);
    },

    // 生成缺货需求记录
    generateShortageRequest: async function(partName, shortageQuantity, totalDemand, availableStock) {
        try {
            // 检查是否已存在相同的缺货需求
            const existingRequest = this.allPartsRequests.find(request =>
                request.partName === partName &&
                request.status === '待处理' &&
                request.type === '缺货补充'
            );

            if (existingRequest) {
                // 如果已存在，更新数量
                await window.dbFunctions.updatePartsRequest(existingRequest.id, {
                    quantity: shortageQuantity,
                    createTime: new Date().toISOString()
                });
                console.log(`更新缺货需求：${partName}，缺货数量：${shortageQuantity}`);
            } else {
                // 创建新的缺货需求
                const requestData = {
                    requestPersonnel: '系统自动',
                    type: '缺货补充',
                    partCode: '', // 可以通过配件设置查找对应的代码
                    partName: partName,
                    quantity: shortageQuantity,
                    status: '待处理',
                    createTime: new Date().toISOString()
                };

                // 尝试获取配件代码
                const partSetting = this.allPartsSettings.find(setting => setting.partName === partName);
                if (partSetting) {
                    requestData.partCode = partSetting.partCode;
                }

                await window.dbFunctions.addPartsRequest(requestData);
                console.log(`生成缺货需求：${partName}，缺货数量：${shortageQuantity}，总需求：${totalDemand}，可用库存：${availableStock}`);
            }

            // 重新加载需求数据
            this.allPartsRequests = await window.dbFunctions.getAllPartsRequests();

        } catch (error) {
            console.error('生成缺货需求失败:', error);
        }
    },

    // 计算借出库存
    calculateLentStock: function(partCode) {
        // 从未归还的借出记录中计算借出数量
        return this.allPartsLending
            .filter(lending => lending.partCode === partCode && lending.status === '借出中')
            .reduce((sum, lending) => sum + (lending.quantity || 0), 0);
    },

    // 获取出库状态样式类
    getOutboundStatusClass: function(status) {
        switch (status) {
            case '待出库': return 'status-warning';
            case '已出库': return 'status-success';
            case '已返回': return 'status-secondary';
            default: return 'status-secondary';
        }
    },

    // 渲染借出归还模块
    renderLendingModule: function() {
        const container = document.getElementById('lending-module');
        if (!container) return;

        container.innerHTML = `
            <div class="card">
                <div class="tab-header">
                    <h2><i class="fas fa-exchange-alt"></i> 借出归还管理</h2>
                    <div>
                        <button class="btn btn-primary" onclick="partsFunctions.showLendingForm()">
                            <i class="fas fa-plus"></i> 新增借出
                        </button>
                    </div>
                </div>

                <div class="lending-content">
                    ${this.renderLendingList(this.allPartsLending)}
                </div>
            </div>
        `;
    },

    // 渲染借出列表
    renderLendingList: function(lendingList) {
        if (lendingList.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-exchange-alt" style="font-size: 48px; color: #dee2e6; margin-bottom: 16px;"></i>
                    <h3 style="color: #6c757d; margin-bottom: 8px;">暂无借出记录</h3>
                    <p style="color: #adb5bd;">点击"新增借出"按钮添加配件借出记录</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>借出日期</th>
                            <th>借出人员</th>
                            <th>配件代码</th>
                            <th>配件名称</th>
                            <th>数量</th>
                            <th>归还日期</th>
                            <th>备注</th>
                            <th>状态</th>
                            <th class="action-column">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${lendingList.map(lending => `
                            <tr>
                                <td>${lending.lendDate || '-'}</td>
                                <td>${lending.lendPerson || '-'}</td>
                                <td>${lending.partCode || '-'}</td>
                                <td>${lending.partName || '-'}</td>
                                <td>${lending.quantity || 0}</td>
                                <td>${lending.returnDate || '-'}</td>
                                <td>${lending.notes || '-'}</td>
                                <td>
                                    <span class="status ${lending.status === '借出中' ? 'status-warning' : 'status-success'}">
                                        ${lending.status || '借出中'}
                                    </span>
                                </td>
                                <td class="action-column">
                                    <div class="action-buttons">
                                        ${lending.status === '借出中' ? `
                                            <button class="btn btn-sm btn-success" onclick="partsFunctions.returnLending(${lending.id})" title="归还">
                                                <i class="fas fa-check"></i> 归还
                                            </button>
                                            <button class="btn btn-sm btn-outline" onclick="partsFunctions.editLending(${lending.id})" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        ` : ''}
                                        <button class="btn btn-sm btn-danger" onclick="partsFunctions.deleteLending(${lending.id})" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    // 渲染库存设置模块
    renderSettingsModule: function() {
        const container = document.getElementById('settings-module');
        if (!container) return;

        container.innerHTML = `
            <div class="card">
                <div class="tab-header">
                    <h2><i class="fas fa-cogs"></i> 库存设置</h2>
                    <div class="header-actions">
                        <button class="btn btn-outline" onclick="partsFunctions.showImportSettingsModal()" title="导入">
                            <i class="fas fa-upload"></i> 导入
                        </button>
                        <button class="btn btn-outline" onclick="partsFunctions.exportSettingsToExcel()" title="导出">
                            <i class="fas fa-download"></i> 导出
                        </button>
                        <button class="btn btn-primary" onclick="partsFunctions.showSettingsForm()">
                            <i class="fas fa-plus"></i> 新增配件
                        </button>
                    </div>
                </div>

                <div class="settings-content">
                    ${this.renderSettingsList(this.allPartsSettings)}
                </div>
            </div>
        `;
    },

    // 渲染设置列表
    renderSettingsList: function(settingsList) {
        if (settingsList.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-cogs" style="font-size: 48px; color: #dee2e6; margin-bottom: 16px;"></i>
                    <h3 style="color: #6c757d; margin-bottom: 8px;">暂无配件设置</h3>
                    <p style="color: #adb5bd;">点击"新增配件"按钮添加配件基础信息</p>
                </div>
            `;
        }

        return `
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>类型</th>
                            <th>属性</th>
                            <th>配件代码</th>
                            <th>配件名称</th>
                            <th>销售价</th>
                            <th>指导价</th>
                            <th>保险价</th>
                            <th>成本价</th>
                            <th>替代配件</th>
                            <th>目标库存</th>
                            <th>最小包装数</th>
                            <th class="action-column">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${settingsList.map(setting => `
                            <tr>
                                <td>${setting.serialNumber || '-'}</td>
                                <td>${setting.type || '-'}</td>
                                <td>${setting.attribute || '-'}</td>
                                <td>${setting.partCode || '-'}</td>
                                <td>${setting.partName || '-'}</td>
                                <td>¥${setting.salePrice || 0}</td>
                                <td>¥${setting.guidePrice || 0}</td>
                                <td>¥${setting.insurancePrice || 0}</td>
                                <td>¥${setting.costPrice || 0}</td>
                                <td>${setting.alternatePart || '-'}</td>
                                <td>${setting.targetStock || 0}</td>
                                <td>${setting.minPackage || 1}</td>
                                <td class="action-column">
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline" onclick="partsFunctions.editSettings(${setting.id})" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="partsFunctions.deleteSettings(${setting.id})" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    // 从订单生成需求
    generateRequestsFromOrders: async function() {
        try {
            // 获取所有已确认的订单
            const confirmedOrders = this.allOrders.filter(order =>
                order.auditStatus === '已审核' && order.options
            );

            let generatedCount = 0;

            for (const order of confirmedOrders) {
                // 解析选装件
                if (!order.options) continue;
                const options = order.options.split(',').map(opt => opt.trim()).filter(opt => opt);

                for (const option of options) {
                    // 检查是否已存在该配件的需求
                    const existingRequest = this.allPartsRequests.find(req =>
                        req.partName === option && req.status === '待匹配'
                    );

                    if (!existingRequest) {
                        // 检查库存是否充足
                        const partSetting = this.allPartsSettings.find(setting =>
                            setting.partName === option
                        );

                        if (partSetting) {
                            const availableStock = this.calculateTotalAvailableStock(partSetting.partCode);

                            if (availableStock < 1) {
                                // 库存不足，生成需求
                                const request = {
                                    requestPerson: order.salesAdvisor || '系统',
                                    type: '选装件',
                                    partCode: partSetting.partCode,
                                    partName: option,
                                    quantity: 1,
                                    status: '待匹配',
                                    createTime: new Date().toISOString().split('T')[0]
                                };

                                await window.dbFunctions.addPartsRequest(request);
                                generatedCount++;
                            }
                        }
                    }
                }
            }

            if (generatedCount > 0) {
                alert(`成功生成 ${generatedCount} 个配件需求`);
                await this.loadAllData();
                this.renderCurrentModule();
                this.updateSidebarStats();
            } else {
                alert('没有需要生成的配件需求');
            }

        } catch (error) {
            console.error('生成配件需求失败:', error);
            alert('生成配件需求失败: ' + error.message);
        }
    },

    // 计算总可用库存
    calculateTotalAvailableStock: function(partCode) {
        return this.allPartsInbound
            .filter(inbound => inbound.partCode === partCode)
            .reduce((sum, inbound) => sum + this.calculateAvailableStock(inbound), 0);
    },

    // 匹配需求
    matchRequest: async function(requestId) {
        try {
            if (!confirm('确认匹配该配件需求？')) return;

            await window.dbFunctions.updatePartsRequest(requestId, {
                status: '已匹配'
            });

            alert('配件需求匹配成功');
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();

        } catch (error) {
            console.error('匹配需求失败:', error);
            alert('匹配需求失败: ' + error.message);
        }
    },

    // 删除需求
    deleteRequest: async function(requestId) {
        try {
            if (!confirm('确认删除该配件需求？')) return;

            await window.dbFunctions.deletePartsRequest(requestId);

            alert('配件需求删除成功');
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();

        } catch (error) {
            console.error('删除需求失败:', error);
            alert('删除需求失败: ' + error.message);
        }
    },

    // 显示入库表单
    showInboundForm: function(inboundId = null) {
        const isEdit = inboundId !== null;
        const inbound = isEdit ? this.allPartsInbound.find(item => item.id === inboundId) : {};

        const modalHtml = `
            <div class="modal-overlay" onclick="this.remove()">
                <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 800px;">
                    <div class="modal-header">
                        <h3>${isEdit ? '编辑' : '新增'}入库记录</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="inbound-form">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>入库时间 *</label>
                                    <input type="date" name="inboundTime" value="${inbound.inboundTime || new Date().toISOString().split('T')[0]}" required>
                                </div>
                                <div class="form-group">
                                    <label>类型</label>
                                    <select name="type">
                                        <option value="原厂件" ${inbound.type === '原厂件' ? 'selected' : ''}>原厂件</option>
                                        <option value="副厂件" ${inbound.type === '副厂件' ? 'selected' : ''}>副厂件</option>
                                        <option value="拆车件" ${inbound.type === '拆车件' ? 'selected' : ''}>拆车件</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>属性 <small class="text-muted">(根据配件代码自动填充)</small></label>
                                    <select name="attribute" id="inbound-attribute-field">
                                        <option value="配件" ${inbound.attribute === '配件' ? 'selected' : ''}>配件</option>
                                        <option value="精品" ${inbound.attribute === '精品' ? 'selected' : ''}>精品</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>库位代码</label>
                                    <input type="text" name="locationCode" value="${inbound.locationCode || ''}" placeholder="如：A01-B02">
                                </div>
                                <div class="form-group">
                                    <label>配件代码 *</label>
                                    <input type="text" name="partCode" id="inbound-partcode-field" value="${inbound.partCode || ''}" required
                                           onchange="partsFunctions.onPartCodeChange(this.value)"
                                           onblur="partsFunctions.onPartCodeChange(this.value)"
                                           placeholder="输入配件代码自动填充属性">
                                </div>
                                <div class="form-group">
                                    <label>配件名称 *</label>
                                    <input type="text" name="partName" id="inbound-partname-field" value="${inbound.partName || ''}" required readonly style="background-color: #f8f9fa;">
                                </div>
                                <div class="form-group">
                                    <label>库存总数 *</label>
                                    <input type="number" name="totalStock" value="${inbound.totalStock || ''}" required min="0">
                                </div>
                                <div class="form-group">
                                    <label>销售价</label>
                                    <input type="number" name="salePrice" id="inbound-saleprice-field" value="${inbound.salePrice || ''}" step="0.01" min="0" readonly style="background-color: #f8f9fa;">
                                </div>
                                <div class="form-group">
                                    <label>指导价</label>
                                    <input type="number" name="guidePrice" id="inbound-guideprice-field" value="${inbound.guidePrice || ''}" step="0.01" min="0" readonly style="background-color: #f8f9fa;">
                                </div>
                                <div class="form-group">
                                    <label>保险价</label>
                                    <input type="number" name="insurancePrice" id="inbound-insuranceprice-field" value="${inbound.insurancePrice || ''}" step="0.01" min="0" readonly style="background-color: #f8f9fa;">
                                </div>
                                <div class="form-group">
                                    <label>成本价</label>
                                    <input type="number" name="costPrice" id="inbound-costprice-field" value="${inbound.costPrice || ''}" step="0.01" min="0" readonly style="background-color: #f8f9fa;">
                                </div>
                                <div class="form-group">
                                    <label>替代配件</label>
                                    <input type="text" name="alternatePart" id="inbound-alternatepart-field" value="${inbound.alternatePart || ''}" placeholder="替代配件代码" readonly style="background-color: #f8f9fa;">
                                </div>
                                <div class="form-group">
                                    <label>目标库存</label>
                                    <input type="number" name="targetStock" id="inbound-targetstock-field" value="${inbound.targetStock || ''}" min="0" readonly style="background-color: #f8f9fa;">
                                </div>
                                <div class="form-group">
                                    <label>最小包装数</label>
                                    <input type="number" name="minPackage" id="inbound-minpackage-field" value="${inbound.minPackage || 1}" min="1" readonly style="background-color: #f8f9fa;">
                                </div>
                                <div class="form-group full-width">
                                    <label>备注</label>
                                    <textarea name="notes" rows="3">${inbound.notes || ''}</textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="partsFunctions.saveInbound(${inboundId})">${isEdit ? '更新' : '保存'}</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    },



    // 保存入库记录
    saveInbound: async function(inboundId = null) {
        try {
            const form = document.getElementById('inbound-form');
            const formData = new FormData(form);

            const inboundData = {
                inboundTime: formData.get('inboundTime'),
                type: formData.get('type'),
                attribute: formData.get('attribute'),
                locationCode: formData.get('locationCode'),
                partCode: formData.get('partCode'),
                partName: formData.get('partName'),
                totalStock: parseInt(formData.get('totalStock')) || 0,
                salePrice: parseFloat(formData.get('salePrice')) || 0,
                guidePrice: parseFloat(formData.get('guidePrice')) || 0,
                insurancePrice: parseFloat(formData.get('insurancePrice')) || 0,
                costPrice: parseFloat(formData.get('costPrice')) || 0,
                alternatePart: formData.get('alternatePart'),
                targetStock: parseInt(formData.get('targetStock')) || 0,
                minPackage: parseInt(formData.get('minPackage')) || 1,
                notes: formData.get('notes')
            };

            if (inboundId) {
                await window.dbFunctions.updatePartsInbound(inboundId, inboundData);
                alert('入库记录更新成功');
            } else {
                await window.dbFunctions.addPartsInbound(inboundData);
                alert('入库记录添加成功');
            }

            document.querySelector('.modal-overlay').remove();
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();

        } catch (error) {
            console.error('保存入库记录失败:', error);
            alert('保存入库记录失败: ' + error.message);
        }
    },

    // 编辑入库记录
    editInbound: function(inboundId) {
        this.showInboundForm(inboundId);
    },

    // 删除入库记录
    deleteInbound: async function(inboundId) {
        try {
            if (!confirm('确认删除该入库记录？删除后将无法恢复。')) return;

            await window.dbFunctions.deletePartsInbound(inboundId);

            alert('入库记录删除成功');
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();

        } catch (error) {
            console.error('删除入库记录失败:', error);
            alert('删除入库记录失败: ' + error.message);
        }
    },

    // 显示库存设置表单
    showSettingsForm: function(settingId = null) {
        const isEdit = settingId !== null;
        const setting = isEdit ? this.allPartsSettings.find(item => item.id === settingId) : {};

        const modalHtml = `
            <div class="modal-overlay" onclick="this.remove()">
                <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 700px;">
                    <div class="modal-header">
                        <h3>${isEdit ? '编辑' : '新增'}配件设置</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="settings-form">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>序号</label>
                                    <input type="text" name="serialNumber" value="${setting.serialNumber || ''}" placeholder="自动生成">
                                </div>
                                <div class="form-group">
                                    <label>类型 *</label>
                                    <select name="type" required>
                                        <option value="">请选择类型</option>
                                        <option value="原厂件" ${setting.type === '原厂件' ? 'selected' : ''}>原厂件</option>
                                        <option value="副厂件" ${setting.type === '副厂件' ? 'selected' : ''}>副厂件</option>
                                        <option value="拆车件" ${setting.type === '拆车件' ? 'selected' : ''}>拆车件</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>属性 *</label>
                                    <select name="attribute" required>
                                        <option value="">请选择属性</option>
                                        <option value="配件" ${setting.attribute === '配件' ? 'selected' : ''}>配件</option>
                                        <option value="精品" ${setting.attribute === '精品' ? 'selected' : ''}>精品</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>配件代码 *</label>
                                    <input type="text" name="partCode" value="${setting.partCode || ''}" required placeholder="唯一配件代码">
                                </div>
                                <div class="form-group">
                                    <label>配件名称 *</label>
                                    <input type="text" name="partName" value="${setting.partName || ''}" required placeholder="配件名称">
                                </div>
                                <div class="form-group">
                                    <label>销售价 *</label>
                                    <input type="number" name="salePrice" value="${setting.salePrice || ''}" required step="0.01" min="0" placeholder="0.00">
                                </div>
                                <div class="form-group">
                                    <label>指导价</label>
                                    <input type="number" name="guidePrice" value="${setting.guidePrice || ''}" step="0.01" min="0" placeholder="0.00">
                                </div>
                                <div class="form-group">
                                    <label>保险价</label>
                                    <input type="number" name="insurancePrice" value="${setting.insurancePrice || ''}" step="0.01" min="0" placeholder="0.00">
                                </div>
                                <div class="form-group">
                                    <label>成本价</label>
                                    <input type="number" name="costPrice" value="${setting.costPrice || ''}" step="0.01" min="0" placeholder="0.00">
                                </div>
                                <div class="form-group">
                                    <label>替代配件</label>
                                    <input type="text" name="alternatePart" value="${setting.replacementPart || ''}" placeholder="替代配件代码">
                                </div>
                                <div class="form-group">
                                    <label>目标库存</label>
                                    <input type="number" name="targetStock" value="${setting.targetStock || ''}" min="0" placeholder="0">
                                </div>
                                <div class="form-group">
                                    <label>最小包装数 *</label>
                                    <input type="number" name="minPackage" value="${setting.minPackage || 1}" required min="1" placeholder="1">
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="partsFunctions.saveSettings(${settingId})">${isEdit ? '更新' : '保存'}</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    // 保存库存设置
    saveSettings: async function(settingId = null) {
        try {
            const form = document.getElementById('settings-form');
            const formData = new FormData(form);

            // 检查配件代码是否重复
            const partCode = formData.get('partCode');
            const existingSetting = this.allPartsSettings.find(setting =>
                setting.partCode === partCode && setting.id !== settingId
            );

            if (existingSetting) {
                alert('配件代码已存在，请使用其他代码');
                return;
            }

            const settingData = {
                serialNumber: formData.get('serialNumber') || this.generateSerialNumber(),
                type: formData.get('type'),
                attribute: formData.get('attribute'),
                partCode: partCode,
                partName: formData.get('partName'),
                salePrice: parseFloat(formData.get('salePrice')) || 0,
                guidePrice: parseFloat(formData.get('guidePrice')) || 0,
                insurancePrice: parseFloat(formData.get('insurancePrice')) || 0,
                costPrice: parseFloat(formData.get('costPrice')) || 0,
                replacementPart: formData.get('alternatePart'),
                targetStock: parseInt(formData.get('targetStock')) || 0,
                minPackage: parseInt(formData.get('minPackage')) || 1
            };

            if (settingId) {
                await window.dbFunctions.updatePartsSettings(settingId, settingData);
                alert('配件设置更新成功');
            } else {
                await window.dbFunctions.addPartsSettings(settingData);
                alert('配件设置添加成功');
            }

            document.querySelector('.modal-overlay').remove();
            await this.loadAllData();
            this.renderCurrentModule();

        } catch (error) {
            console.error('保存配件设置失败:', error);
            alert('保存配件设置失败: ' + error.message);
        }
    },

    // 生成序号
    generateSerialNumber: function() {
        const maxSerial = this.allPartsSettings.reduce((max, setting) => {
            const serial = parseInt(setting.serialNumber) || 0;
            return serial > max ? serial : max;
        }, 0);
        return String(maxSerial + 1).padStart(4, '0');
    },

    // 编辑库存设置
    editSettings: function(settingId) {
        this.showSettingsForm(settingId);
    },

    // 删除库存设置
    deleteSettings: async function(settingId) {
        try {
            if (!confirm('确认删除该配件设置？删除后将无法恢复。')) return;

            // 检查是否有相关的入库记录
            const relatedInbound = this.allPartsInbound.find(inbound => {
                const setting = this.allPartsSettings.find(s => s.id === settingId);
                return setting && inbound.partCode === setting.partCode;
            });

            if (relatedInbound) {
                if (!confirm('该配件存在入库记录，删除设置可能影响库存管理。确认继续删除？')) {
                    return;
                }
            }

            await window.dbFunctions.deletePartsSettings(settingId);

            alert('配件设置删除成功');
            await this.loadAllData();
            this.renderCurrentModule();

        } catch (error) {
            console.error('删除配件设置失败:', error);
            alert('删除配件设置失败: ' + error.message);
        }
    },

    // 显示出库表单
    showOutboundForm: function(outboundId = null) {
        const isEdit = outboundId !== null;
        const outbound = isEdit ? this.allPartsOutbound.find(item => item.id === outboundId) : {};

        const modalHtml = `
            <div class="modal-overlay" onclick="this.remove()">
                <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 700px;">
                    <div class="modal-header">
                        <h3>${isEdit ? '编辑' : '新增'}出库记录</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="outbound-form">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>出库日期 *</label>
                                    <input type="date" name="outboundTime" value="${outbound.outboundTime || new Date().toISOString().split('T')[0]}" required>
                                </div>
                                <div class="form-group">
                                    <label>人员类型 *</label>
                                    <select name="personnelType" required>
                                        <option value="">请选择人员类型</option>
                                        <option value="销售顾问" ${outbound.personnelType === '销售顾问' ? 'selected' : ''}>销售顾问</option>
                                        <option value="服务顾问" ${outbound.personnelType === '服务顾问' ? 'selected' : ''}>服务顾问</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>人员 *</label>
                                    <input type="text" name="personnelName" value="${outbound.personnelName || ''}" required placeholder="操作人员姓名">
                                </div>
                                <div class="form-group">
                                    <label>客户名称</label>
                                    <input type="text" name="customerName" value="${outbound.customerName || ''}" placeholder="客户姓名">
                                </div>
                                <div class="form-group">
                                    <label>VIN</label>
                                    <input type="text" name="vin" value="${outbound.vin || ''}" placeholder="车辆VIN码">
                                </div>
                                <div class="form-group">
                                    <label>类型</label>
                                    <select name="type">
                                        <option value="原厂件" ${outbound.type === '原厂件' ? 'selected' : ''}>原厂件</option>
                                        <option value="副厂件" ${outbound.type === '副厂件' ? 'selected' : ''}>副厂件</option>
                                        <option value="拆车件" ${outbound.type === '拆车件' ? 'selected' : ''}>拆车件</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>配件代码 *</label>
                                    <input type="text" name="partCode" value="${outbound.partCode || ''}" required onchange="partsFunctions.onOutboundPartCodeChange(this.value)">
                                </div>
                                <div class="form-group">
                                    <label>配件名称 *</label>
                                    <input type="text" name="partName" value="${outbound.partName || ''}" required>
                                </div>
                                <div class="form-group">
                                    <label>数量 *</label>
                                    <input type="number" name="quantity" value="${outbound.quantity || ''}" required min="1">
                                </div>
                                <div class="form-group">
                                    <label>销售价</label>
                                    <input type="number" name="salePrice" value="${outbound.salePrice || ''}" step="0.01" min="0">
                                </div>
                                <div class="form-group">
                                    <label>指导价</label>
                                    <input type="number" name="guidePrice" value="${outbound.guidePrice || ''}" step="0.01" min="0">
                                </div>
                                <div class="form-group">
                                    <label>保险价</label>
                                    <input type="number" name="insurancePrice" value="${outbound.insurancePrice || ''}" step="0.01" min="0">
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="partsFunctions.saveOutbound(${outboundId})">${isEdit ? '更新' : '保存'}</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    // 出库配件代码变化时自动填充信息
    onOutboundPartCodeChange: function(partCode) {
        const partSetting = this.allPartsSettings.find(setting => setting.partCode === partCode);
        if (partSetting) {
            const form = document.getElementById('outbound-form');
            if (form) {
                form.partName.value = partSetting.partName || '';
                form.salePrice.value = partSetting.salePrice || '';
                form.guidePrice.value = partSetting.guidePrice || '';
                form.insurancePrice.value = partSetting.insurancePrice || '';
            }
        }
    },

    // 保存出库记录
    saveOutbound: async function(outboundId = null) {
        try {
            const form = document.getElementById('outbound-form');
            const formData = new FormData(form);

            const partCode = formData.get('partCode');
            const quantity = parseInt(formData.get('quantity')) || 0;

            // 检查库存是否充足
            const availableStock = this.calculateTotalAvailableStock(partCode);
            if (availableStock < quantity) {
                alert(`库存不足！当前可用库存：${availableStock}，需要数量：${quantity}`);
                return;
            }

            const outboundData = {
                outboundTime: formData.get('outboundTime'),
                personnelType: formData.get('personnelType'),
                personnelName: formData.get('personnelName'),
                customerName: formData.get('customerName'),
                vin: formData.get('vin'),
                type: formData.get('type'),
                partCode: partCode,
                partName: formData.get('partName'),
                quantity: quantity,
                salePrice: parseFloat(formData.get('salePrice')) || 0,
                guidePrice: parseFloat(formData.get('guidePrice')) || 0,
                insurancePrice: parseFloat(formData.get('insurancePrice')) || 0,
                status: '待出库'
            };

            if (outboundId) {
                await window.dbFunctions.updatePartsOutbound(outboundId, outboundData);
                alert('出库记录更新成功');
            } else {
                await window.dbFunctions.addPartsOutbound(outboundData);
                alert('出库记录添加成功');
            }

            document.querySelector('.modal-overlay').remove();
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();

        } catch (error) {
            console.error('保存出库记录失败:', error);
            alert('保存出库记录失败: ' + error.message);
        }
    },

    // 确认出库
    confirmOutbound: async function(outboundId) {
        try {
            if (!confirm('确认出库？出库后将锁定记录并减少库存。')) return;

            const outbound = this.allPartsOutbound.find(item => item.id === outboundId);
            if (!outbound) return;

            // 检查库存是否充足
            const availableStock = this.calculateTotalAvailableStock(outbound.partCode);
            if (availableStock < outbound.quantity) {
                alert(`库存不足！当前可用库存：${availableStock}，需要数量：${outbound.quantity}`);
                return;
            }

            // 更新出库状态和出库日期
            await window.dbFunctions.updatePartsOutbound(outboundId, {
                status: '已出库',
                outboundTime: new Date().toISOString().split('T')[0] // 记录实际出库日期
            });

            // 减少对应入库记录的库存
            await this.reduceInboundStock(outbound.partCode, outbound.quantity);

            alert('出库确认成功');
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();

        } catch (error) {
            console.error('确认出库失败:', error);
            alert('确认出库失败: ' + error.message);
        }
    },

    // 减少入库库存
    reduceInboundStock: async function(partCode, quantity) {
        const inboundItems = this.allPartsInbound.filter(item => item.partCode === partCode);
        let remainingQuantity = quantity;

        for (const inbound of inboundItems) {
            if (remainingQuantity <= 0) break;

            const availableStock = this.calculateAvailableStock(inbound);
            if (availableStock > 0) {
                const reduceAmount = Math.min(availableStock, remainingQuantity);
                const newTotalStock = Math.max(0, inbound.totalStock - reduceAmount);

                await window.dbFunctions.updatePartsInbound(inbound.id, {
                    totalStock: newTotalStock
                });

                remainingQuantity -= reduceAmount;
            }
        }
    },

    // 返出库
    returnOutbound: async function(outboundId) {
        try {
            if (!confirm('确认返出库？将恢复库存并允许重新编辑记录。')) return;

            const outbound = this.allPartsOutbound.find(item => item.id === outboundId);
            if (!outbound) return;

            // 更新出库状态
            await window.dbFunctions.updatePartsOutbound(outboundId, {
                status: '待出库'
            });

            // 恢复对应入库记录的库存
            await this.restoreInboundStock(outbound.partCode, outbound.quantity);

            alert('返出库成功');
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();

        } catch (error) {
            console.error('返出库失败:', error);
            alert('返出库失败: ' + error.message);
        }
    },

    // 恢复入库库存
    restoreInboundStock: async function(partCode, quantity) {
        // 找到最近的入库记录进行恢复
        const inboundItems = this.allPartsInbound
            .filter(item => item.partCode === partCode)
            .sort((a, b) => new Date(b.inboundTime) - new Date(a.inboundTime));

        if (inboundItems.length > 0) {
            const latestInbound = inboundItems[0];
            const newTotalStock = latestInbound.totalStock + quantity;

            await window.dbFunctions.updatePartsInbound(latestInbound.id, {
                totalStock: newTotalStock
            });
        }
    },

    // 编辑出库记录
    editOutbound: function(outboundId) {
        this.showOutboundForm(outboundId);
    },

    // 删除出库记录
    deleteOutbound: async function(outboundId) {
        try {
            if (!confirm('确认删除该出库记录？删除后将无法恢复。')) return;

            await window.dbFunctions.deletePartsOutbound(outboundId);

            alert('出库记录删除成功');
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();

        } catch (error) {
            console.error('删除出库记录失败:', error);
            alert('删除出库记录失败: ' + error.message);
        }
    },

    // 显示借出表单
    showLendingForm: function(lendingId = null) {
        const isEdit = lendingId !== null;
        const lending = isEdit ? this.allPartsLending.find(item => item.id === lendingId) : {};

        const modalHtml = `
            <div class="modal-overlay" onclick="this.remove()">
                <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3>${isEdit ? '编辑' : '新增'}借出记录</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="lending-form">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>借出日期 *</label>
                                    <input type="date" name="lendDate" value="${lending.lendDate || new Date().toISOString().split('T')[0]}" required>
                                </div>
                                <div class="form-group">
                                    <label>借出人员 *</label>
                                    <input type="text" name="lendPerson" value="${lending.lendPerson || ''}" required placeholder="借出人员姓名">
                                </div>
                                <div class="form-group">
                                    <label>配件代码 *</label>
                                    <input type="text" name="partCode" value="${lending.partCode || ''}" required onchange="partsFunctions.onLendingPartCodeChange(this.value)">
                                </div>
                                <div class="form-group">
                                    <label>配件名称 *</label>
                                    <input type="text" name="partName" value="${lending.partName || ''}" required>
                                </div>
                                <div class="form-group">
                                    <label>数量 *</label>
                                    <input type="number" name="quantity" value="${lending.quantity || ''}" required min="1">
                                </div>
                                <div class="form-group">
                                    <label>归还日期</label>
                                    <input type="date" name="returnDate" value="${lending.returnDate || ''}">
                                </div>
                                <div class="form-group full-width">
                                    <label>备注</label>
                                    <textarea name="notes" rows="3">${lending.notes || ''}</textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="partsFunctions.saveLending(${lendingId})">${isEdit ? '更新' : '保存'}</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    // 借出配件代码变化时自动填充信息
    onLendingPartCodeChange: function(partCode) {
        const partSetting = this.allPartsSettings.find(setting => setting.partCode === partCode);
        if (partSetting) {
            const form = document.getElementById('lending-form');
            if (form) {
                form.partName.value = partSetting.partName || '';
            }
        }
    },

    // 保存借出记录
    saveLending: async function(lendingId = null) {
        try {
            const form = document.getElementById('lending-form');
            const formData = new FormData(form);

            const partCode = formData.get('partCode');
            const quantity = parseInt(formData.get('quantity')) || 0;

            // 如果是新增借出，检查库存是否充足
            if (!lendingId) {
                const availableStock = this.calculateTotalAvailableStock(partCode);
                if (availableStock < quantity) {
                    alert(`库存不足！当前可用库存：${availableStock}，需要数量：${quantity}`);
                    return;
                }
            }

            const lendingData = {
                lendDate: formData.get('lendDate'),
                lendPerson: formData.get('lendPerson'),
                partCode: partCode,
                partName: formData.get('partName'),
                quantity: quantity,
                returnDate: formData.get('returnDate'),
                notes: formData.get('notes'),
                status: formData.get('returnDate') ? '已归还' : '借出中'
            };

            if (lendingId) {
                await window.dbFunctions.updatePartsLending(lendingId, lendingData);
                alert('借出记录更新成功');
            } else {
                await window.dbFunctions.addPartsLending(lendingData);
                alert('借出记录添加成功');
            }

            document.querySelector('.modal-overlay').remove();
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();

        } catch (error) {
            console.error('保存借出记录失败:', error);
            alert('保存借出记录失败: ' + error.message);
        }
    },

    // 归还借出
    returnLending: async function(lendingId) {
        try {
            if (!confirm('确认归还该配件？')) return;

            await window.dbFunctions.updatePartsLending(lendingId, {
                returnDate: new Date().toISOString().split('T')[0],
                status: '已归还'
            });

            alert('配件归还成功');
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();

        } catch (error) {
            console.error('归还配件失败:', error);
            alert('归还配件失败: ' + error.message);
        }
    },

    // 编辑借出记录
    editLending: function(lendingId) {
        this.showLendingForm(lendingId);
    },

    // 删除借出记录
    deleteLending: async function(lendingId) {
        try {
            if (!confirm('确认删除该借出记录？删除后将无法恢复。')) return;

            await window.dbFunctions.deletePartsLending(lendingId);

            alert('借出记录删除成功');
            await this.loadAllData();
            this.renderCurrentModule();
            this.updateSidebarStats();

        } catch (error) {
            console.error('删除借出记录失败:', error);
            alert('删除借出记录失败: ' + error.message);
        }
    },

    // 从订单生成出库记录
    generateOutboundFromOrders: async function() {
        try {
            // 获取所有已交车的订单
            const deliveredOrders = this.allOrders.filter(order =>
                order.deliveryStatus === '已交车' && order.options
            );

            let generatedCount = 0;

            for (const order of deliveredOrders) {
                // 解析选装件
                if (!order.options) continue;
                const options = order.options.split(',').map(opt => opt.trim()).filter(opt => opt);

                for (const option of options) {
                    // 检查是否已存在该配件的出库记录
                    const existingOutbound = this.allPartsOutbound.find(outbound =>
                        outbound.vin === order.vin && outbound.partName === option
                    );

                    if (!existingOutbound) {
                        // 查找配件设置
                        const partSetting = this.allPartsSettings.find(setting =>
                            setting.partName === option
                        );

                        if (partSetting) {
                            const outboundData = {
                                outboundTime: order.deliveryDate || new Date().toISOString().split('T')[0],
                                personnelType: '销售顾问',
                                personnelName: order.salesAdvisor || '系统',
                                customerName: order.customerName,
                                vin: order.vin,
                                type: partSetting.type || '原厂件',
                                partCode: partSetting.partCode,
                                partName: option,
                                quantity: 1,
                                salePrice: partSetting.salePrice || 0,
                                guidePrice: partSetting.guidePrice || 0,
                                insurancePrice: partSetting.insurancePrice || 0,
                                status: '已出库'
                            };

                            await window.dbFunctions.addPartsOutbound(outboundData);

                            // 减少库存
                            await this.reduceInboundStock(partSetting.partCode, 1);

                            generatedCount++;
                        }
                    }
                }
            }

            if (generatedCount > 0) {
                alert(`成功生成 ${generatedCount} 个出库记录`);
                await this.loadAllData();
                this.renderCurrentModule();
                this.updateSidebarStats();
            } else {
                alert('没有需要生成的出库记录');
            }

        } catch (error) {
            console.error('生成出库记录失败:', error);
            alert('生成出库记录失败: ' + error.message);
        }
    },

    // 入库管理筛选和分页相关函数
    getFilteredInboundData: function() {
        let filtered = [...this.allPartsInbound];

        // 应用筛选条件
        if (this.inboundFilters.type) {
            filtered = filtered.filter(item => item.type === this.inboundFilters.type);
        }
        if (this.inboundFilters.attribute) {
            filtered = filtered.filter(item => item.attribute === this.inboundFilters.attribute);
        }
        if (this.inboundFilters.partCode) {
            filtered = filtered.filter(item =>
                (item.partCode || '').toLowerCase().includes(this.inboundFilters.partCode.toLowerCase())
            );
        }
        if (this.inboundFilters.partName) {
            filtered = filtered.filter(item =>
                (item.partName || '').toLowerCase().includes(this.inboundFilters.partName.toLowerCase())
            );
        }

        // 按入库时间倒序排列
        filtered.sort((a, b) => new Date(b.inboundTime || 0) - new Date(a.inboundTime || 0));

        return filtered;
    },

    updateInboundFilter: function(key, value) {
        this.inboundFilters[key] = value;
        this.inboundPagination.currentPage = 1; // 重置到第一页
        this.renderInboundList();
        this.updateInboundContent();
    },

    resetInboundFilters: function() {
        this.inboundFilters = {
            type: '',
            partCode: '',
            partName: '',
            attribute: ''
        };
        this.inboundPagination.currentPage = 1;

        // 重置表单值
        document.getElementById('inbound-type-filter').value = '';
        document.getElementById('inbound-attribute-filter').value = '';
        document.getElementById('inbound-partcode-filter').value = '';
        document.getElementById('inbound-partname-filter').value = '';

        this.renderInboundList();
        this.updateInboundContent();
    },

    updateInboundContent: function() {
        const contentContainer = document.querySelector('#inbound-module .inbound-content');
        if (contentContainer) {
            contentContainer.innerHTML = this.renderInboundList();
        }
    },

    renderInboundPagination: function() {
        if (this.inboundPagination.totalPages <= 1) {
            return '';
        }

        const currentPage = this.inboundPagination.currentPage;
        const totalPages = this.inboundPagination.totalPages;
        const totalRecords = this.inboundPagination.totalRecords;
        const startRecord = (currentPage - 1) * this.inboundPagination.pageSize + 1;
        const endRecord = Math.min(currentPage * this.inboundPagination.pageSize, totalRecords);

        let paginationHtml = `
            <div class="pagination-container">
                <div class="pagination-info">
                    显示第 ${startRecord}-${endRecord} 条，共 ${totalRecords} 条记录
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-sm btn-outline" ${currentPage === 1 ? 'disabled' : ''}
                            onclick="partsFunctions.goToInboundPage(${currentPage - 1})">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </button>
        `;

        // 页码按钮
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <button class="btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-outline'}"
                        onclick="partsFunctions.goToInboundPage(${i})">
                    ${i}
                </button>
            `;
        }

        paginationHtml += `
                    <button class="btn btn-sm btn-outline" ${currentPage === totalPages ? 'disabled' : ''}
                            onclick="partsFunctions.goToInboundPage(${currentPage + 1})">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        `;

        return paginationHtml;
    },

    goToInboundPage: function(page) {
        if (page >= 1 && page <= this.inboundPagination.totalPages) {
            this.inboundPagination.currentPage = page;
            this.updateInboundContent();
        }
    },

    // 列设置相关函数
    loadInboundColumnSettings: function() {
        const saved = localStorage.getItem('inboundColumnSettings');
        if (saved) {
            this.inboundColumnSettings = { ...this.inboundColumnSettings, ...JSON.parse(saved) };
        }
    },

    saveInboundColumnSettings: function() {
        localStorage.setItem('inboundColumnSettings', JSON.stringify(this.inboundColumnSettings));
    },

    showInboundColumnSettings: function() {
        const modalHtml = `
            <div class="modal-overlay" onclick="this.remove()">
                <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 500px;">
                    <div class="modal-header">
                        <h3>列显示设置</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="column-settings">
                            ${Object.keys(this.inboundColumnSettings).map(key => {
                                const labels = {
                                    inboundTime: '入库时间',
                                    stockAge: '库龄',
                                    type: '类型',
                                    attribute: '属性',
                                    locationCode: '库位代码',
                                    partCode: '配件代码',
                                    partName: '配件名称',
                                    notes: '备注',
                                    totalStock: '库存总数',
                                    availableStock: '可用库存',
                                    reservedStock: '预订库存',
                                    lentStock: '借出库存',
                                    salePrice: '销售价',
                                    guidePrice: '指导价',
                                    insurancePrice: '保险价',
                                    costPrice: '成本价',
                                    alternatePart: '替代配件'
                                };
                                return `
                                    <div class="column-setting-item">
                                        <label>
                                            <input type="checkbox" ${this.inboundColumnSettings[key] ? 'checked' : ''}
                                                   onchange="partsFunctions.toggleInboundColumn('${key}', this.checked)">
                                            ${labels[key] || key}
                                        </label>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="partsFunctions.applyInboundColumnSettings()">应用</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    toggleInboundColumn: function(columnKey, isVisible) {
        this.inboundColumnSettings[columnKey] = isVisible;
    },

    applyInboundColumnSettings: function() {
        this.saveInboundColumnSettings();
        document.querySelector('.modal-overlay').remove();
        this.updateInboundContent();
    },

    // 导出入库数据 - 统一化导出功能
    exportInboundToExcel: function() {
        try {
            const filteredData = this.getFilteredInboundData();

            if (filteredData.length === 0) {
                alert('没有可导出的入库数据');
                return;
            }

            // 显示导出选项模态框
            const modalContent = `
                <div class="export-modal">
                    <h3><i class="fas fa-download"></i> 导出入库数据</h3>
                    <div style="margin: 20px 0;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出格式：</label>
                            <label style="margin-right: 20px;">
                                <input type="radio" name="export-format" value="csv" checked> CSV格式
                            </label>
                            <label>
                                <input type="radio" name="export-format" value="excel"> Excel格式
                            </label>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出字段：</label>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                <label><input type="checkbox" value="inboundTime" checked> 入库时间</label>
                                <label><input type="checkbox" value="stockAge" checked> 库龄</label>
                                <label><input type="checkbox" value="type" checked> 类型</label>
                                <label><input type="checkbox" value="attribute" checked> 属性</label>
                                <label><input type="checkbox" value="locationCode" checked> 库位代码</label>
                                <label><input type="checkbox" value="partCode" checked> 配件代码</label>
                                <label><input type="checkbox" value="partName" checked> 配件名称</label>
                                <label><input type="checkbox" value="notes"> 备注</label>
                                <label><input type="checkbox" value="totalStock" checked> 库存总数</label>
                                <label><input type="checkbox" value="availableStock" checked> 可用库存</label>
                                <label><input type="checkbox" value="reservedStock"> 预订库存</label>
                                <label><input type="checkbox" value="lentStock"> 借出库存</label>
                                <label><input type="checkbox" value="salePrice" checked> 销售价</label>
                                <label><input type="checkbox" value="guidePrice"> 指导价</label>
                                <label><input type="checkbox" value="insurancePrice"> 保险价</label>
                                <label><input type="checkbox" value="costPrice"> 成本价</label>
                                <label><input type="checkbox" value="alternatePart"> 替代配件</label>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button class="btn btn-primary" id="confirm-export-btn">确认导出</button>
                    </div>
                </div>
            `;

            this.showModal('导出数据', modalContent);

            // 绑定导出确认事件
            document.getElementById('confirm-export-btn').addEventListener('click', () => {
                const format = document.querySelector('input[name="export-format"]:checked').value;
                const selectedFields = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                    .map(cb => cb.value);

                if (selectedFields.length === 0) {
                    alert('请至少选择一个字段');
                    return;
                }

                this.performInboundExport(format, selectedFields, filteredData);
                const modal = document.querySelector('.modal-overlay');
                if (modal) modal.remove();
            });

        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败: ' + error.message);
        }
    },

    // 执行入库数据导出
    performInboundExport: function(format, selectedFields, data) {
        try {
            // 字段名映射
            const fieldNames = {
                inboundTime: '入库时间',
                stockAge: '库龄(天)',
                type: '类型',
                attribute: '属性',
                locationCode: '库位代码',
                partCode: '配件代码',
                partName: '配件名称',
                notes: '备注',
                totalStock: '库存总数',
                availableStock: '可用库存',
                reservedStock: '预订库存',
                lentStock: '借出库存',
                salePrice: '销售价',
                guidePrice: '指导价',
                insurancePrice: '保险价',
                costPrice: '成本价',
                alternatePart: '替代配件'
            };

            // 准备导出数据
            const exportData = data.map(inbound => {
                const row = {};
                selectedFields.forEach(field => {
                    let value = inbound[field] || '';

                    // 特殊处理计算字段
                    if (field === 'stockAge') {
                        value = this.calculateStockAge(inbound.inboundTime);
                    } else if (field === 'availableStock') {
                        value = this.calculateAvailableStock(inbound);
                    } else if (field === 'reservedStock') {
                        value = this.calculateReservedStock(inbound.partCode);
                    } else if (field === 'lentStock') {
                        value = this.calculateLentStock(inbound.partCode);
                    }

                    row[fieldNames[field]] = value;
                });
                return row;
            });

            if (format === 'csv') {
                this.exportInboundToCSV(exportData);
            } else {
                this.exportInboundToExcel_New(exportData);
            }

        } catch (error) {
            console.error('导出处理失败:', error);
            alert('导出处理失败: ' + error.message);
        }
    },

    // 导出入库数据到CSV
    exportInboundToCSV: function(data) {
        if (data.length === 0) return;

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');

        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `入库数据_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('CSV文件导出成功！');
    },

    // 导出入库数据到Excel（新版本）
    exportInboundToExcel_New: function(data) {
        if (data.length === 0) return;

        try {
            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Inbound');

            // 设置列宽
            const colWidths = Object.keys(data[0]).map(() => ({ wch: 15 }));
            worksheet['!cols'] = colWidths;

            XLSX.writeFile(workbook, `入库数据_${new Date().toISOString().split('T')[0]}.xlsx`);
            alert('Excel文件导出成功！');

        } catch (error) {
            console.error('Excel导出失败:', error);
            alert('Excel导出失败，请确保已加载XLSX库: ' + error.message);
        }
    },

    // 显示导入模态框
    showImportInboundModal: function() {
        const modalHtml = `
            <div class="modal-overlay" onclick="this.remove()">
                <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3>导入入库记录</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="import-section">
                            <div class="form-group">
                                <label>选择Excel文件</label>
                                <input type="file" id="inbound-import-file" accept=".xlsx,.xls" onchange="partsFunctions.handleInboundFileSelect(this)">
                                <small class="text-muted">支持.xlsx和.xls格式文件</small>
                            </div>

                            <div class="import-template">
                                <h4>导入模板说明</h4>
                                <p>Excel文件应包含以下列（按顺序）：</p>
                                <ul>
                                    <li>入库时间 (格式: YYYY-MM-DD)</li>
                                    <li>类型 (原厂件/副厂件/拆车件)</li>
                                    <li>属性 (配件/精品)</li>
                                    <li>库位代码</li>
                                    <li>配件代码 (必填)</li>
                                    <li>配件名称 (必填)</li>
                                    <li>库存总数 (必填)</li>
                                    <li>销售价</li>
                                    <li>指导价</li>
                                    <li>保险价</li>
                                    <li>成本价</li>
                                    <li>替代配件</li>
                                    <li>目标库存</li>
                                    <li>最小包装数</li>
                                    <li>备注</li>
                                </ul>
                                <button class="btn btn-outline btn-sm" onclick="partsFunctions.downloadInboundTemplate()">
                                    <i class="fas fa-download"></i> 下载模板
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="partsFunctions.importInboundFromExcel()" disabled id="import-inbound-btn">导入</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    // 处理文件选择
    handleInboundFileSelect: function(input) {
        const importBtn = document.getElementById('import-inbound-btn');
        if (input.files && input.files[0]) {
            importBtn.disabled = false;
        } else {
            importBtn.disabled = true;
        }
    },

    // 下载导入模板
    downloadInboundTemplate: function() {
        const templateData = [{
            '入库时间': '2024-01-01',
            '类型': '原厂件',
            '属性': '配件',
            '库位代码': 'A01-B01',
            '配件代码': 'PART001',
            '配件名称': '示例配件',
            '库存总数': 10,
            '销售价': 100.00,
            '指导价': 120.00,
            '保险价': 110.00,
            '成本价': 80.00,
            '替代配件': '',
            '目标库存': 20,
            '最小包装数': 1,
            '备注': '示例数据'
        }];

        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(templateData);
        XLSX.utils.book_append_sheet(wb, ws, '入库记录模板');
        XLSX.writeFile(wb, '入库记录导入模板.xlsx');
    },

    // 导出库存设置数据 - 统一化导出功能
    exportSettingsToExcel: function() {
        try {
            if (this.allPartsSettings.length === 0) {
                alert('没有可导出的库存设置数据');
                return;
            }

            // 显示导出选项模态框
            const modalContent = `
                <div class="export-modal">
                    <h3><i class="fas fa-download"></i> 导出库存设置数据</h3>
                    <div style="margin: 20px 0;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出格式：</label>
                            <label style="margin-right: 20px;">
                                <input type="radio" name="export-format" value="csv" checked> CSV格式
                            </label>
                            <label>
                                <input type="radio" name="export-format" value="excel"> Excel格式
                            </label>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 10px; font-weight: bold;">选择导出字段：</label>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                <label><input type="checkbox" value="serialNumber" checked> 序号</label>
                                <label><input type="checkbox" value="type" checked> 类型</label>
                                <label><input type="checkbox" value="attribute" checked> 属性</label>
                                <label><input type="checkbox" value="partCode" checked> 配件代码</label>
                                <label><input type="checkbox" value="partName" checked> 配件名称</label>
                                <label><input type="checkbox" value="salePrice" checked> 销售价</label>
                                <label><input type="checkbox" value="guidePrice" checked> 指导价</label>
                                <label><input type="checkbox" value="insurancePrice"> 保险价</label>
                                <label><input type="checkbox" value="costPrice"> 成本价</label>
                                <label><input type="checkbox" value="alternatePart"> 替代配件</label>
                                <label><input type="checkbox" value="targetStock" checked> 目标库存</label>
                                <label><input type="checkbox" value="minPackage" checked> 最小包装数</label>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button class="btn btn-primary" id="confirm-export-btn">确认导出</button>
                    </div>
                </div>
            `;

            this.showModal('导出数据', modalContent);

            // 绑定导出确认事件
            document.getElementById('confirm-export-btn').addEventListener('click', () => {
                const format = document.querySelector('input[name="export-format"]:checked').value;
                const selectedFields = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                    .map(cb => cb.value);

                if (selectedFields.length === 0) {
                    alert('请至少选择一个字段');
                    return;
                }

                this.performSettingsExport(format, selectedFields);
                const modal = document.querySelector('.modal-overlay');
                if (modal) modal.remove();
            });

        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败: ' + error.message);
        }
    },

    // 执行库存设置导出
    performSettingsExport: function(format, selectedFields) {
        try {
            // 字段名映射
            const fieldNames = {
                serialNumber: '序号',
                type: '类型',
                attribute: '属性',
                partCode: '配件代码',
                partName: '配件名称',
                salePrice: '销售价',
                guidePrice: '指导价',
                insurancePrice: '保险价',
                costPrice: '成本价',
                alternatePart: '替代配件',
                targetStock: '目标库存',
                minPackage: '最小包装数'
            };

            // 准备导出数据
            const exportData = this.allPartsSettings.map(setting => {
                const row = {};
                selectedFields.forEach(field => {
                    let value = setting[field] || '';

                    // 确保数值字段的正确格式
                    if (['salePrice', 'guidePrice', 'insurancePrice', 'costPrice', 'targetStock', 'minPackage'].includes(field)) {
                        value = Number(value) || 0;
                    } else {
                        value = String(value);
                    }

                    row[fieldNames[field]] = value;
                });
                return row;
            });

            if (format === 'csv') {
                this.exportSettingsToCSV(exportData);
            } else {
                this.exportSettingsToExcel_New(exportData);
            }

        } catch (error) {
            console.error('导出处理失败:', error);
            alert('导出处理失败: ' + error.message);
        }
    },

    // 导出库存设置到CSV
    exportSettingsToCSV: function(data) {
        if (data.length === 0) return;

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');

        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `库存设置数据_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('CSV文件导出成功！');
    },

    // 导出库存设置到Excel（新版本）
    exportSettingsToExcel_New: function(data) {
        if (data.length === 0) return;

        try {
            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Settings');

            // 设置列宽
            const colWidths = Object.keys(data[0]).map(() => ({ wch: 15 }));
            worksheet['!cols'] = colWidths;

            XLSX.writeFile(workbook, `库存设置数据_${new Date().toISOString().split('T')[0]}.xlsx`);
            alert('Excel文件导出成功！');

        } catch (error) {
            console.error('Excel导出失败:', error);
            alert('Excel导出失败，请确保已加载XLSX库: ' + error.message);
        }
    },

    // 显示导入库存设置模态框
    showImportSettingsModal: function() {
        const modalHtml = `
            <div class="modal-overlay" onclick="this.remove()">
                <div class="modal-content" onclick="event.stopPropagation()" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3>导入库存设置</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="import-section">
                            <div class="form-group">
                                <label>选择Excel文件</label>
                                <input type="file" id="settings-import-file" accept=".xlsx,.xls" onchange="partsFunctions.handleSettingsFileSelect(this)">
                                <small class="text-muted">支持.xlsx和.xls格式文件</small>
                            </div>

                            <div class="import-template">
                                <h4>导入模板说明</h4>
                                <p>Excel文件应包含以下列（按顺序）：</p>
                                <ul>
                                    <li>序号</li>
                                    <li>类型 (原厂件/副厂件/拆车件)</li>
                                    <li>属性 (配件/精品)</li>
                                    <li>配件代码 (必填，唯一)</li>
                                    <li>配件名称 (必填)</li>
                                    <li>销售价 (必填)</li>
                                    <li>指导价</li>
                                    <li>保险价</li>
                                    <li>成本价</li>
                                    <li>替代配件</li>
                                    <li>目标库存</li>
                                    <li>最小包装数</li>
                                </ul>
                                <button class="btn btn-outline btn-sm" onclick="partsFunctions.downloadSettingsTemplate()">
                                    <i class="fas fa-download"></i> 下载模板
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="partsFunctions.importSettingsFromExcel()" disabled id="import-settings-btn">导入</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    // 处理库存设置文件选择
    handleSettingsFileSelect: function(input) {
        const importBtn = document.getElementById('import-settings-btn');
        if (input.files && input.files[0]) {
            importBtn.disabled = false;
        } else {
            importBtn.disabled = true;
        }
    },

    // 下载库存设置导入模板
    downloadSettingsTemplate: function() {
        const templateData = [{
            '序号': '0001',
            '类型': '原厂件',
            '属性': '配件',
            '配件代码': 'PART001',
            '配件名称': '示例配件',
            '销售价': 100.00,
            '指导价': 120.00,
            '保险价': 110.00,
            '成本价': 80.00,
            '替代配件': '',
            '目标库存': 20,
            '最小包装数': 1
        }];

        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(templateData);
        XLSX.utils.book_append_sheet(wb, ws, '库存设置模板');
        XLSX.writeFile(wb, '库存设置导入模板.xlsx');
    },

    // 从Excel导入库存设置
    importSettingsFromExcel: async function() {
        try {
            const fileInput = document.getElementById('settings-import-file');
            if (!fileInput.files || !fileInput.files[0]) {
                alert('请选择要导入的文件');
                return;
            }

            const file = fileInput.files[0];
            const reader = new FileReader();

            reader.onload = async (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);

                    if (jsonData.length === 0) {
                        alert('Excel文件中没有数据');
                        return;
                    }

                    let successCount = 0;
                    let errorCount = 0;
                    const errors = [];

                    for (let i = 0; i < jsonData.length; i++) {
                        const row = jsonData[i];
                        try {
                            // 验证必填字段
                            if (!row['配件代码'] || !row['配件名称'] || !row['销售价']) {
                                throw new Error('配件代码、配件名称和销售价为必填字段');
                            }

                            // 检查配件代码是否重复
                            const existingSetting = this.allPartsSettings.find(setting =>
                                setting.partCode === row['配件代码']
                            );
                            if (existingSetting) {
                                throw new Error(`配件代码 ${row['配件代码']} 已存在`);
                            }

                            const settingData = {
                                serialNumber: row['序号'] || this.generateSerialNumber(),
                                type: row['类型'] || '原厂件',
                                attribute: row['属性'] || '配件',
                                partCode: row['配件代码'],
                                partName: row['配件名称'],
                                salePrice: parseFloat(row['销售价']) || 0,
                                guidePrice: parseFloat(row['指导价']) || 0,
                                insurancePrice: parseFloat(row['保险价']) || 0,
                                costPrice: parseFloat(row['成本价']) || 0,
                                alternatePart: row['替代配件'] || '',
                                targetStock: parseInt(row['目标库存']) || 0,
                                minPackage: parseInt(row['最小包装数']) || 1
                            };

                            await window.dbFunctions.addPartsSettings(settingData);
                            successCount++;

                        } catch (error) {
                            errorCount++;
                            errors.push(`第${i + 2}行: ${error.message}`);
                        }
                    }

                    document.querySelector('.modal-overlay').remove();

                    if (successCount > 0) {
                        await this.loadAllData();
                        this.renderCurrentModule();
                    }

                    let message = `导入完成！成功: ${successCount} 条，失败: ${errorCount} 条`;
                    if (errors.length > 0 && errors.length <= 5) {
                        message += '\n\n错误详情:\n' + errors.join('\n');
                    } else if (errors.length > 5) {
                        message += '\n\n错误详情:\n' + errors.slice(0, 5).join('\n') + `\n... 还有 ${errors.length - 5} 个错误`;
                    }

                    alert(message);

                } catch (error) {
                    console.error('解析Excel文件失败:', error);
                    alert('解析Excel文件失败: ' + error.message);
                }
            };

            reader.readAsArrayBuffer(file);

        } catch (error) {
            console.error('导入失败:', error);
            alert('导入失败: ' + error.message);
        }
    },

    // 从Excel导入入库记录
    importInboundFromExcel: async function() {
        try {
            const fileInput = document.getElementById('inbound-import-file');
            if (!fileInput.files || !fileInput.files[0]) {
                alert('请选择要导入的文件');
                return;
            }

            const file = fileInput.files[0];
            const reader = new FileReader();

            reader.onload = async (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);

                    if (jsonData.length === 0) {
                        alert('Excel文件中没有数据');
                        return;
                    }

                    let successCount = 0;
                    let errorCount = 0;
                    const errors = [];

                    for (let i = 0; i < jsonData.length; i++) {
                        const row = jsonData[i];
                        try {
                            // 验证必填字段
                            if (!row['配件代码'] || !row['配件名称'] || !row['库存总数']) {
                                throw new Error('配件代码、配件名称和库存总数为必填字段');
                            }

                            const inboundData = {
                                inboundTime: row['入库时间'] || new Date().toISOString().split('T')[0],
                                type: row['类型'] || '原厂件',
                                attribute: row['属性'] || '配件',
                                locationCode: row['库位代码'] || '',
                                partCode: row['配件代码'],
                                partName: row['配件名称'],
                                totalStock: parseInt(row['库存总数']) || 0,
                                salePrice: parseFloat(row['销售价']) || 0,
                                guidePrice: parseFloat(row['指导价']) || 0,
                                insurancePrice: parseFloat(row['保险价']) || 0,
                                costPrice: parseFloat(row['成本价']) || 0,
                                alternatePart: row['替代配件'] || '',
                                targetStock: parseInt(row['目标库存']) || 0,
                                minPackage: parseInt(row['最小包装数']) || 1,
                                notes: row['备注'] || ''
                            };

                            await window.dbFunctions.addPartsInbound(inboundData);
                            successCount++;

                        } catch (error) {
                            errorCount++;
                            errors.push(`第${i + 2}行: ${error.message}`);
                        }
                    }

                    document.querySelector('.modal-overlay').remove();

                    if (successCount > 0) {
                        await this.loadAllData();
                        this.renderCurrentModule();
                        this.updateSidebarStats();
                    }

                    let message = `导入完成！成功: ${successCount} 条，失败: ${errorCount} 条`;
                    if (errors.length > 0 && errors.length <= 5) {
                        message += '\n\n错误详情:\n' + errors.join('\n');
                    } else if (errors.length > 5) {
                        message += '\n\n错误详情:\n' + errors.slice(0, 5).join('\n') + `\n... 还有 ${errors.length - 5} 个错误`;
                    }

                    alert(message);

                } catch (error) {
                    console.error('解析Excel文件失败:', error);
                    alert('解析Excel文件失败: ' + error.message);
                }
            };

            reader.readAsArrayBuffer(file);

        } catch (error) {
            console.error('导入失败:', error);
            alert('导入失败: ' + error.message);
        }
    },

    // 显示模态框
    showModal: function(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        `;

        // 点击背景关闭模态框
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        });

        document.body.appendChild(modal);
    },

    // 配件代码变化时自动填充属性
    onPartCodeChange: function(partCode) {
        try {
            if (!partCode || partCode.trim() === '') {
                return;
            }

            // 从库存设置表中查询配件代码对应的属性信息
            const partSetting = this.allPartsSettings.find(setting =>
                setting.partCode === partCode.trim()
            );

            if (partSetting) {
                // 找到匹配的配件设置，自动填充所有相关字段
                const fieldsToFill = [
                    { id: 'inbound-attribute-field', value: partSetting.attribute, name: '属性' },
                    { id: 'inbound-partname-field', value: partSetting.partName, name: '配件名称' },
                    { id: 'inbound-saleprice-field', value: partSetting.salePrice, name: '销售价' },
                    { id: 'inbound-guideprice-field', value: partSetting.guidePrice, name: '指导价' },
                    { id: 'inbound-insuranceprice-field', value: partSetting.insurancePrice, name: '保险价' },
                    { id: 'inbound-costprice-field', value: partSetting.costPrice, name: '成本价' },
                    { id: 'inbound-alternatepart-field', value: partSetting.replacementPart, name: '替代配件' },
                    { id: 'inbound-targetstock-field', value: partSetting.targetStock, name: '目标库存' },
                    { id: 'inbound-minpackage-field', value: partSetting.minPackage, name: '最小包装数' }
                ];

                const filledFields = [];
                fieldsToFill.forEach(field => {
                    const element = document.getElementById(field.id);
                    if (element) {
                        // 只有当值不为空时才填充
                        if (field.value !== undefined && field.value !== null && field.value !== '') {
                            element.value = field.value;
                            filledFields.push(field.name);
                        } else {
                            // 如果值为空，清空字段
                            element.value = '';
                        }
                    }
                });

                // 显示成功提示，包含填充的字段信息
                this.showPartCodeMatchInfo(partSetting, filledFields);
                console.log(`配件代码 ${partCode} 匹配成功，已自动填充字段: ${filledFields.join(', ')}`);
            } else {
                // 没有找到匹配的配件代码
                console.log(`配件代码 ${partCode} 在库存设置中未找到匹配项`);
                this.showPartCodeNotFoundInfo(partCode);
            }
        } catch (error) {
            console.error('配件代码查询失败:', error);
        }
    },

    // 显示配件代码匹配成功的信息
    showPartCodeMatchInfo: function(partSetting, filledFields = []) {
        // 创建临时提示信息
        const infoDiv = document.createElement('div');
        infoDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 10px 15px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 300px;
        `;

        const fieldsText = filledFields.length > 0
            ? `<br><small>已填充字段: ${filledFields.join(', ')}</small>`
            : '';

        infoDiv.innerHTML = `
            <i class="fas fa-check-circle"></i>
            配件代码匹配成功！<br>
            <small>代码: ${partSetting.partCode} | 名称: ${partSetting.partName}</small>
            ${fieldsText}
        `;

        document.body.appendChild(infoDiv);

        // 4秒后自动移除提示
        setTimeout(() => {
            if (infoDiv.parentNode) {
                infoDiv.parentNode.removeChild(infoDiv);
            }
        }, 4000);
    },

    // 显示配件代码未找到的信息
    showPartCodeNotFoundInfo: function(partCode) {
        // 创建临时提示信息
        const infoDiv = document.createElement('div');
        infoDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px 15px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        `;
        infoDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            配件代码 "${partCode}" 在库存设置中未找到<br>
            <small>请检查配件代码是否正确，或先在库存设置中添加该配件</small>
        `;

        document.body.appendChild(infoDiv);

        // 5秒后自动移除提示
        setTimeout(() => {
            if (infoDiv.parentNode) {
                infoDiv.parentNode.removeChild(infoDiv);
            }
        }, 5000);
    }
};

// 确保模块立即可用
console.log('配件库管理模块已加载');
