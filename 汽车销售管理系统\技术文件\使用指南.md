# 展厅录入和线索录入功能使用指南

## 概述

本指南介绍了修复后的展厅录入和线索录入功能的使用方法，包括导入导出、分页功能和新增字段的操作说明。

## 新功能特性

### 🆕 主要改进
- ✅ 完整的导入导出功能，支持所有字段
- ✅ 高效的分页显示（每页50条记录）
- ✅ 线索录入新增"是否重复"字段
- ✅ 优化的用户界面和操作体验

## 展厅录入功能

### 1. 导出功能

#### 1.1 导出操作步骤
1. 进入展厅录入页面
2. 点击"导出"按钮
3. 选择导出格式（CSV或Excel）
4. 选择要导出的字段（支持全选/全不选）
5. 点击"确认导出"

#### 1.2 导出字段说明
导出功能包含以下20个字段（按顺序）：
- 录入日期、录入人员、销售顾问
- 来店时间、离店时间、滞店时间
- 来店类型、来店渠道
- 客户名称、性别、电话
- 意向车型、意向、区域
- 现有车型、对比车型
- 金融、置换、试驾、备注

### 2. 导入功能

#### 2.1 导入操作步骤
1. 准备导入文件（CSV或Excel格式）
2. 确保文件包含正确的列标题
3. 点击"导入"按钮
4. 选择文件
5. 系统自动处理字段映射和数据验证

#### 2.2 导入文件格式要求
- **文件格式**：支持CSV和Excel（.xlsx）
- **编码格式**：UTF-8（推荐）
- **列标题**：使用中文标题，如"客户名称"、"电话"等
- **数据格式**：
  - 日期：YYYY-MM-DD格式
  - 布尔值：使用"是"/"否"
  - 文本：正常文本格式

#### 2.3 关键字段映射
| 列标题 | 字段类型 | 示例值 |
|--------|----------|--------|
| 来店渠道 | 下拉选择 | 网络平台、电话咨询 |
| 性别 | 选择 | 男、女 |
| 意向车型 | 车型选择 | 林肯Z、冒险家 |
| 现有车型 | 车型选择 | 丰田凯美瑞 |
| 对比车型 | 车型选择 | 宝马3系 |
| 置换 | 是/否 | 是、否 |
| 试驾 | 是/否 | 是、否 |
| 备注 | 文本 | 任意文本内容 |

### 3. 分页功能

#### 3.1 分页特性
- **每页记录数**：50条
- **页码导航**：首页、上一页、下一页、末页
- **快速跳转**：输入页码直接跳转
- **记录统计**：显示总记录数和当前页信息

#### 3.2 分页操作
1. **页码导航**：点击页码按钮切换页面
2. **快速跳转**：在跳转框输入页码，点击"确定"
3. **搜索重置**：搜索或筛选时自动回到第一页

## 线索录入功能

### 1. 新增字段：是否重复

#### 1.1 字段说明
- **位置**：在"是否有效"字段后面
- **类型**：是/否选择
- **用途**：标记线索是否为重复线索
- **默认值**：否

#### 1.2 使用场景
- 识别重复提交的线索
- 线索去重管理
- 数据质量控制

### 2. 导出功能

#### 2.1 导出字段
线索录入导出包含以下17个字段：
- 录入日期、是否有效、**是否重复**（新增）
- 客户名称、电话、线索ID、智慧号
- 意向车型、区域、微信、渠道
- 到店日期、成交日期
- 转销售跟进、接待顾问
- 首次跟进日期、跟进情况

### 3. 导入功能

#### 3.1 导入字段映射
支持"是否重复"字段的导入，列标题使用"是否重复"，值使用"是"/"否"。

### 4. 分页功能
与展厅录入相同，支持每页50条记录的分页显示。

## 测试和验证

### 1. 功能测试
使用提供的测试文件验证功能：
- `test-import-export.html`：功能测试页面
- `test-data/展厅录入测试数据.csv`：展厅录入测试数据
- `test-data/线索录入测试数据.csv`：线索录入测试数据

### 2. 验证脚本
在浏览器控制台运行验证脚本：
```javascript
// 加载验证脚本后运行
verifyFunctions.runAllVerifications()
```

## 常见问题解答

### Q1: 导入时提示字段映射错误怎么办？
**A**: 检查CSV文件的列标题是否使用中文，如"客户名称"而不是"customerName"。

### Q2: 导出的Excel文件乱码怎么办？
**A**: 确保使用UTF-8编码保存CSV文件，或直接使用Excel格式导出。

### Q3: 分页功能不显示怎么办？
**A**: 确保数据量超过50条，少于50条时不显示分页控件。

### Q4: "是否重复"字段在旧数据中显示什么？
**A**: 旧数据的"是否重复"字段默认为"否"，可以手动编辑修改。

### Q5: 导入大量数据时性能如何？
**A**: 系统支持批量导入，建议单次导入不超过1000条记录以确保最佳性能。

## 技术支持

如果遇到问题，请：
1. 查看浏览器控制台的错误信息
2. 使用验证脚本检查功能状态
3. 检查数据格式是否符合要求
4. 联系技术支持团队

## 更新日志

### 版本 1.1 (2025-01-22)
- ✅ 修复展厅录入导入导出字段映射问题
- ✅ 实现分页功能（每页50条记录）
- ✅ 新增线索录入"是否重复"字段
- ✅ 优化用户界面和操作体验
- ✅ 完善数据验证机制

---

*本指南基于系统版本1.1编写，如有更新请参考最新版本文档。*
