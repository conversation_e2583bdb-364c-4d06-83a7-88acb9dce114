<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>销售分析 - 汽车销售漏斗管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dexie@3.2.2/dist/dexie.min.js"></script>
    <script src="xlsx.full.min.js"></script>
    <link rel="stylesheet" href="styles.css" />
    <style>
        /* 销售分析页面特定样式 */
        .analytics-dashboard {
            padding: 20px;
        }

        .time-filter-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .filter-controls label {
            font-weight: 600;
            color: #495057;
        }

        .filter-controls select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .chart-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .chart-header {
            margin-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 15px;
        }

        .chart-header h3 {
            color: #212529;
            font-size: 18px;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-header p {
            color: #6c757d;
            font-size: 14px;
            margin: 0;
        }

        .chart-container {
            position: relative;
            height: 200px;
            margin-bottom: 10px;
        }

        .funnel-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #4361ee;
        }

        .target-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .target-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .target-header h4 {
            color: #212529;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .target-progress {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .progress-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .progress-text {
            text-align: center;
        }

        .progress-text .percentage {
            display: block;
            font-size: 18px;
            font-weight: bold;
            color: #212529;
        }

        .progress-text .label {
            font-size: 12px;
            color: #6c757d;
        }

        .target-stats {
            flex: 1;
        }

        .target-stats .stat {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .target-stats .stat .label {
            color: #6c757d;
            font-size: 14px;
        }

        .target-stats .stat .value {
            font-weight: 600;
            color: #212529;
        }

        /* 新的分析模块样式 */
        .analytics-row {
            margin-bottom: 20px;
        }

        .analytics-module {
            background: white;
            border-radius: 6px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            height: 280px;
            display: flex;
            flex-direction: column;
        }

        .module-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .module-header h3 {
            color: #212529;
            font-size: 14px;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 600;
        }

        .module-header h3 i {
            color: #4361ee;
            font-size: 12px;
        }

        .time-selector select {
            padding: 3px 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 11px;
            background: white;
        }

        .module-content {
            padding: 15px;
            flex: 1;
            overflow: hidden;
        }

        /* 漏斗图样式 */
        .funnel-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            padding: 10px 0;
        }

        .funnel-stage {
            background: #6c757d;
            color: white;
            text-align: center;
            padding: 6px 12px;
            position: relative;
            font-size: 11px;
            font-weight: 500;
            border-radius: 2px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .funnel-stage.stage-1 { width: 90%; background: #6c757d; }
        .funnel-stage.stage-2 { width: 75%; background: #6c757d; }
        .funnel-stage.stage-3 { width: 60%; background: #6c757d; }
        .funnel-stage.stage-4 { width: 45%; background: #6c757d; }
        .funnel-stage.stage-5 { width: 30%; background: #6c757d; }

        .funnel-stats {
            margin-top: 8px;
            font-size: 10px;
            color: #6c757d;
            text-align: center;
        }

        @media (max-width: 768px) {
            .filter-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .chart-container {
                height: 300px;
            }

            .target-progress {
                flex-direction: column;
                text-align: center;
            }

            .analytics-row {
                grid-template-columns: 1fr !important;
            }
        }
            box-shadow: var(--shadow);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo i {
            font-size: 28px;
        }

        .logo h1 {
            font-size: 22px;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 42px;
            height: 42px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }

        .main-content {
            display: flex;
            flex: 1;
        }

        .sidebar {
            width: 260px;
            background: white;
            padding: 25px 20px;
            display: flex;
            flex-direction: column;
            box-shadow: var(--shadow);
            z-index: 10;
        }

        .nav-menu {
            list-style: none;
            margin-top: 20px;
        }

        .nav-menu li {
            margin-bottom: 8px;
        }

        .nav-menu a {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            text-decoration: none;
            color: var(--dark);
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 15px;
            font-weight: 500;
        }

        .nav-menu a i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
            color: var(--gray);
        }

        .nav-menu a:hover, .nav-menu a.active {
            background: rgba(67, 97, 238, 0.1);
            color: var(--primary);
        }

        .nav-menu a.active i {
            color: var(--primary);
        }

        .nav-menu a:hover i {
            color: var(--primary);
        }

        .content-area {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .module-header {
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .module-header h1 {
            font-size: 26px;
            font-weight: 700;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .module-header h1 i {
            color: var(--primary);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .card-header {
            padding: 20px 25px;
            border-bottom: 1px solid var(--light-gray);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-header h2 i {
            color: var(--primary);
        }

        .card-body {
            padding: 25px;
        }

        .funnel-overview {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .funnel-step {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: rgba(67, 97, 238, 0.05);
            transition: transform 0.3s ease;
        }

        .funnel-step:hover {
            transform: translateY(-5px);
            background: rgba(67, 97, 238, 0.1);
        }

        .funnel-step h3 {
            font-size: 14px;
            color: var(--gray);
            margin-bottom: 10px;
        }

        .funnel-step .number {
            font-size: 32px;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .funnel-step .conversion {
            font-size: 14px;
            background: rgba(76, 201, 240, 0.2);
            color: var(--info);
            padding: 4px 8px;
            border-radius: 20px;
            display: inline-block;
        }

        .chart-container {
            padding: 15px;
            height: 300px;
            position: relative;
        }

        .data-management {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .data-card {
            padding: 20px;
            border-radius: 8px;
            background: rgba(67, 97, 238, 0.05);
            display: flex;
            flex-direction: column;
        }

        .data-card h3 {
            font-size: 15px;
            color: var(--gray);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .data-card h3 i {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(67, 97, 238, 0.1);
            color: var(--primary);
            border-radius: 6px;
        }

        .data-card .number {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 10px;
        }

        .data-card .info {
            font-size: 14px;
            color: var(--gray);
            margin-top: auto;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .data-card .info i {
            color: var(--success);
        }

        .progress-bar {
            height: 8px;
            background: var(--light-gray);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress {
            height: 100%;
            background: var(--success);
            border-radius: 4px;
        }

        .notification {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 18px 22px;
            display: flex;
            align-items: center;
            gap: 15px;
            transform: translateX(150%);
            transition: transform 0.4s ease;
            z-index: 1000;
            border-left: 4px solid var(--primary);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification i {
            font-size: 24px;
            color: var(--primary);
        }

        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary);
        }

        .btn-secondary {
            background: white;
            color: var(--primary);
            border: 1px solid var(--primary);
        }

        .btn-secondary:hover {
            background: rgba(67, 97, 238, 0.1);
        }

        .form-select {
            padding: 8px 12px;
            border: 1px solid var(--light-gray);
            border-radius: 6px;
            background: white;
            font-size: 14px;
        }

        /* Responsive design */
        @media (max-width: 1200px) {
            .funnel-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
                padding: 15px;
            }
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            .funnel-overview {
                grid-template-columns: 1fr;
            }
            .data-management {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body style="height: 100vh; margin: 0;">
    <div class="container" style="flex: 1; display: flex; flex-direction: column; height: 100%;">
        <header style="background: linear-gradient(135deg, #4361ee, #3f37c9); color: white; padding: 15px 30px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
            <div class="logo" style="display: flex; align-items: center; gap: 15px;">
                <i class="fas fa-chart-line" style="font-size: 28px;"></i>
                <h1 style="font-size: 22px; font-weight: 600;">销售分析</h1>
            </div>
            <div class="user-info" style="display: flex; align-items: center; gap: 12px;">
                <div class="user-avatar" style="width: 42px; height: 42px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 18px;">张</div>
                <div>
                    <div>张经理</div>
                    <div style="font-size: 13px; opacity: 0.8;">销售主管</div>
                </div>
            </div>
        </header>

        <div class="main-content" style="flex: 1; display: flex; overflow: hidden;">
            <div class="sidebar" style="width: 260px; background: white; padding: 25px 20px; display: flex; flex-direction: column; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); z-index: 10; height: 100%; overflow-y: auto;">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 13px; color: #6c757d; margin-bottom: 5px;">数据概览</div>
                    <div style="font-size: 28px; font-weight: 700; color: #3f37c9;" id="total-data-points">0</div>
                    <div style="font-size: 14px; color: #6c757d;">分析维度</div>
                </div>

                <ul class="nav-menu" style="list-style: none; margin-top: 20px;">
                    <li style="margin-bottom: 8px;"><a href="index.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-home" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 首页</a></li>
                    <li style="margin-bottom: 8px;"><a href="salesanalytics.html" class="active" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #4361ee; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-chart-line" style="margin-right: 12px; width: 20px; text-align: center; color: #4361ee;"></i> 销售分析</a></li>
                    <li style="margin-bottom: 8px;"><a href="customers.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 客户管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="order.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-shopping-cart" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 订单管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="parts.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-boxes" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 配件库管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="settings.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 系统设置</a></li>
                    <li style="margin-bottom: 8px;"><a href="user.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 用户管理</a></li>
                </ul>

                <div style="margin-top: 30px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h3 style="font-size: 15px; margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-calendar-alt" style="color: #3f37c9;"></i> 快速筛选
                    </h3>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <button class="btn btn-outline quick-filter-btn" data-period="today">今日数据</button>
                        <button class="btn btn-outline quick-filter-btn" data-period="week">本周数据</button>
                        <button class="btn btn-outline quick-filter-btn" data-period="month">本月数据</button>
                        <button class="btn btn-outline quick-filter-btn" data-period="quarter">本季度数据</button>
                    </div>
                </div>
            </div>

            <div class="content-area" style="flex: 1; padding: 20px; overflow-y: auto; background: #f5f7fb;">
                <div id="analytics-content">
                    <!-- 销售分析内容 -->
                    <div class="analytics-dashboard">
                        <!-- 时间筛选区域 -->
                        <div class="time-filter-section">
                            <div class="filter-controls">
                                <label>时间范围：</label>
                                <select id="time-range-select">
                                    <option value="本月">本月</option>
                                    <option value="上月">上月</option>
                                    <option value="本季度">本季度</option>
                                    <option value="本年度">本年度</option>
                                </select>
                            </div>
                        </div>

                        <!-- 第一行：展厅客流、销售顾问排名、销售顾问成果分析 -->
                        <div class="analytics-row" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <!-- 展厅客流 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-users"></i> 展厅客流</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="showroom-traffic-table"></div>
                                </div>
                            </div>

                            <!-- 销售顾问排名 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-trophy"></i> 销售顾问排名</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div class="chart-container">
                                        <canvas id="advisor-ranking-chart"></canvas>
                                    </div>
                                </div>
                            </div>

                            <!-- 销售顾问成果分析 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-chart-pie"></i> 销售顾问成果分析</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="advisor-performance-table"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 第二行：线索渠道概览 -->
                        <div class="analytics-row" style="display: grid; grid-template-columns: 1fr; gap: 20px; margin-bottom: 20px;">
                            <!-- 线索渠道概览 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-chart-bar"></i> 线索渠道概览</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div class="chart-container">
                                        <canvas id="lead-channel-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 第三行：销售趋势、线索趋势 -->
                        <div class="analytics-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- 销售趋势 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-chart-line"></i> 销售趋势</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div class="chart-container">
                                        <canvas id="sales-trend-chart"></canvas>
                                    </div>
                                </div>
                            </div>

                            <!-- 线索趋势 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-chart-line"></i> 线索趋势</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div class="chart-container">
                                        <canvas id="lead-trend-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 第四行：车型意向分析、试驾率分析 -->
                        <div class="analytics-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- 车型意向分析 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-car"></i> 车型意向分析</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="vehicle-intention-table"></div>
                                </div>
                            </div>

                            <!-- 试驾率分析 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-key"></i> 试驾率分析</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="test-drive-analysis"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 第五行：销售顾问订单排名、交车统计 -->
                        <div class="analytics-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- 销售顾问订单排名 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-trophy"></i> 销售顾问订单排名</h3>
                                    <div class="time-selector">
                                        <select id="order-ranking-time-selector" class="form-select">
                                            <option value="本月">本月</option>
                                            <option value="本年">本年</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="order-ranking-table"></div>
                                </div>
                            </div>

                            <!-- 交车统计 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-handshake"></i> 交车统计</h3>
                                    <div class="time-selector">
                                        <select id="delivery-stats-time-selector" class="form-select">
                                            <option value="本月">本月</option>
                                            <option value="本年">本年</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="delivery-statistics-table"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 第六行：意向客户统计、销售顾问关键指标 -->
                        <div class="analytics-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- 意向客户统计 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-heart"></i> 意向客户统计</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="intention-customer-stats"></div>
                                </div>
                            </div>

                            <!-- 销售顾问关键指标 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-chart-bar"></i> 销售顾问关键指标</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="sales-advisor-kpis"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 第七行：销售漏斗分析 -->
                        <div class="analytics-row" style="display: grid; grid-template-columns: 1fr; gap: 20px;">
                            <!-- 销售漏斗分析 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-filter"></i> 销售漏斗分析</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="sales-funnel-analysis"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 第八行：线索漏斗分析 -->
                        <div class="analytics-row" style="display: grid; grid-template-columns: 1fr; gap: 20px;">
                            <!-- 线索漏斗分析 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-funnel-dollar"></i> 线索漏斗分析</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="leads-funnel-analysis"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 第九行：线索渠道分析、趋势分析 -->
                        <div class="analytics-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- 线索渠道分析 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-share-alt"></i> 线索渠道分析</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本月">本月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="lead-channel-analysis"></div>
                                </div>
                            </div>

                            <!-- 趋势分析 -->
                            <div class="analytics-module">
                                <div class="module-header">
                                    <h3><i class="fas fa-chart-area"></i> 趋势分析</h3>
                                    <div class="time-selector">
                                        <select class="form-select">
                                            <option value="本年">本年</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="module-content">
                                    <div id="trend-analysis"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="database.js"></script>
    <script src="notification.js"></script>
    <script src="salesAnalyticsModule.js"></script>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 等待所有脚本加载完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 检查必要的函数是否存在
                if (!window.dbFunctions) {
                    throw new Error('数据库函数未加载');
                }

                // 初始化数据库
                await window.dbFunctions.initDB();

                // 初始化销售分析模块
                await window.salesAnalytics.init();

                console.log('销售分析页面初始化完成');
            } catch (error) {
                console.error('初始化失败:', error);
                if (window.showNotification) {
                    showNotification('初始化失败', '系统初始化时出错: ' + error.message, 'danger');
                } else {
                    alert('初始化失败: ' + error.message);
                }
            }
        });

        // 快速筛选按钮事件
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('quick-filter-btn')) {
                // 移除其他按钮的active状态
                document.querySelectorAll('.quick-filter-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                // 添加当前按钮的active状态
                e.target.classList.add('active');

                const period = e.target.dataset.period;
                console.log('快速筛选:', period);
                // 这里可以添加具体的筛选逻辑
            }
        });
    </script>
</body>
</html>