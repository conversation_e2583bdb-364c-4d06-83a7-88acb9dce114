<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>汽车销售漏斗管理系统 - 模块化设计</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dexie@3.2.2/dist/dexie.min.js"></script>
    <link rel="stylesheet" href="styles.css" />
</head>
<body style="height: 100vh; margin: 0;">
    <div class="container" style="flex: 1; display: flex; flex-direction: column; height: 100%;">
        <header style="background: linear-gradient(135deg, #4361ee, #3f37c9); color: white; padding: 15px 30px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
            <div class="logo" style="display: flex; align-items: center; gap: 15px;">
                <i class="fas fa-car" style="font-size: 28px;"></i>
                <h1 style="font-size: 22px; font-weight: 600;">汽车销售漏斗管理系统</h1>
            </div>
            <div class="user-info" style="display: flex; align-items: center; gap: 12px;">
                <div class="user-avatar" style="width: 42px; height: 42px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 18px;">张</div>
                <div>
                    <div>张经理</div>
                    <div style="font-size: 13px; opacity: 0.8;">销售主管</div>
                </div>
            </div>
        </header>
        
        <div class="main-content" style="flex: 1; display: flex; overflow: hidden;">
            <div class="sidebar" style="width: 260px; background: white; padding: 25px 20px; display: flex; flex-direction: column; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); z-index: 10; height: 100%; overflow-y: auto;">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 13px; color: #6c757d; margin-bottom: 5px;">今日数据</div>
                    <div style="font-size: 28px; font-weight: 700; color: #3f37c9;" id="today-count">0</div>
                    <div style="font-size: 14px; color: #6c757d;">到店客户</div>
                </div>
                
                <ul class="nav-menu" style="list-style: none; margin-top: 20px;">
                    <li style="margin-bottom: 8px;"><a href="index.html" class="active" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #4361ee; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-home" style="margin-right: 12px; width: 20px; text-align: center; color: #4361ee;"></i> 首页</a></li>
                    <li style="margin-bottom: 8px;"><a href="salesanalytics.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-chart-line" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 销售分析</a></li>
                    <li style="margin-bottom: 8px;"><a href="customers.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 客户管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="order.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-shopping-cart" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 订单管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="rebate.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-calculator" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 返利核算</a></li>
                    <li style="margin-bottom: 8px;"><a href="parts.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-boxes" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 配件库管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="settings.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 系统设置</a></li>
                    <li style="margin-bottom: 8px;"><a href="user.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 用户管理</a></li>
                </ul>
                
                <div style="margin-top: 30px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h3 style="font-size: 15px; margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-database" style="color: #3f37c9;"></i> 数据库状态
                    </h3>
                    <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 8px;">
                        <span>已使用空间:</span>
                        <span id="db-usage">0 MB / 500 MB</span>
                    </div>
                    <div class="progress-bar" style="height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden; margin-top: 5px;">
                        <div class="progress" id="db-progress" style="height: 100%; background: #4cc9f0; border-radius: 4px; width: 0%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #6c757d; margin-top: 5px; text-align: center;">
                        上次同步: <span id="last-sync">--:--</span>
                    </div>
                </div>
            </div>
            
            <div class="content-area" style="flex: 1; padding: 30px; overflow-y: auto;">
                <div class="module-container active">
                    <div class="module-header">
                        <h1><i class="fas fa-home"></i> 汽车销售漏斗管理系统</h1>
                        <div style="font-size: 14px; color: #6c757d;">欢迎使用销售管理系统</div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-rocket"></i> 快速导航</h2>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; padding: 20px;">
                            <a href="salesanalytics.html" class="nav-card">
                                <div class="nav-card-icon"><i class="fas fa-chart-line"></i></div>
                                <h3>销售分析</h3>
                                <p>查看销售数据概览和趋势分析</p>
                            </a>

                            <a href="customers.html" class="nav-card">
                                <div class="nav-card-icon"><i class="fas fa-users"></i></div>
                                <h3>客户管理</h3>
                                <p>管理客户信息和跟进状态</p>
                            </a>

                            <a href="order.html" class="nav-card">
                                <div class="nav-card-icon"><i class="fas fa-shopping-cart"></i></div>
                                <h3>订单管理</h3>
                                <p>管理订单、库存和目标</p>
                            </a>

                            <a href="parts.html" class="nav-card">
                                <div class="nav-card-icon"><i class="fas fa-boxes"></i></div>
                                <h3>配件库管理</h3>
                                <p>管理配件库存和出入库</p>
                            </a>

                            <a href="settings.html" class="nav-card">
                                <div class="nav-card-icon"><i class="fas fa-cog"></i></div>
                                <h3>系统设置</h3>
                                <p>配置系统参数和用户设置</p>
                            </a>

                            <a href="user.html" class="nav-card">
                                <div class="nav-card-icon"><i class="fas fa-users-cog"></i></div>
                                <h3>用户管理</h3>
                                <p>管理系统用户和权限设置</p>
                            </a>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-info-circle"></i> 系统信息</h2>
                        </div>
                        
                        <div style="padding: 20px;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                                <div>
                                    <h4><i class="fas fa-database"></i> 数据库状态</h4>
                                    <p>系统运行正常，数据同步完成</p>
                                </div>
                                <div>
                                    <h4><i class="fas fa-users"></i> 用户统计</h4>
                                    <p>当前活跃用户：<span id="active-users">1</span> 人</p>
                                </div>
                                <div>
                                    <h4><i class="fas fa-calendar"></i> 最后更新</h4>
                                    <p id="last-update">刚刚</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
