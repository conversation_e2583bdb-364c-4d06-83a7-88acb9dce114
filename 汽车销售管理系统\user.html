<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 汽车销售漏斗管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/dexie@3.2.2/dist/dexie.min.js"></script>
    <link rel="stylesheet" href="styles.css" />
    <style>
        /* 用户管理特定样式 */
        .permission-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .permission-tag {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .permission-tag.index { background-color: #e3f2fd; color: #1976d2; }
        .permission-tag.salesanalytics { background-color: #f3e5f5; color: #7b1fa2; }
        .permission-tag.customers { background-color: #e8f5e8; color: #388e3c; }
        .permission-tag.order { background-color: #fff3e0; color: #f57c00; }
        .permission-tag.settings { background-color: #fce4ec; color: #c2185b; }
        .permission-tag.user { background-color: #e0f2f1; color: #00796b; }
        
        .status-enabled { background-color: #d4edda; color: #155724; }
        .status-disabled { background-color: #f8d7da; color: #721c24; }
        
        .password-display {
            font-family: monospace;
            letter-spacing: 2px;
        }
        
        .permission-checkboxes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .permission-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .permission-checkbox input[type="checkbox"] {
            width: auto;
        }
        
        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            border-radius: 8px;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .modal-header h3 {
            margin: 0;
            color: #212529;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #6c757d;
        }
        
        .modal-close:hover {
            color: #212529;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #212529;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4361ee;
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
        }
        
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }
        
        .data-table td {
            font-size: 14px;
            color: #212529;
        }
        
        .data-table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            margin-right: 4px;
        }

        /* 权限组样式 */
        .permission-groups {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8f9fa;
        }

        .permission-group {
            margin-bottom: 15px;
        }

        .permission-group:last-child {
            margin-bottom: 0;
        }

        .permission-checkboxes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 5px;
        }

        .permission-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .permission-checkbox input[type="checkbox"] {
            margin: 0;
        }

        .permission-checkbox label {
            margin: 0;
            font-size: 13px;
            cursor: pointer;
        }
    </style>
</head>
<body style="height: 100vh; margin: 0;">
    <div class="container" style="flex: 1; display: flex; flex-direction: column; height: 100%;">
        <header style="background: linear-gradient(135deg, #4361ee, #3f37c9); color: white; padding: 15px 30px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
            <div class="logo" style="display: flex; align-items: center; gap: 15px;">
                <i class="fas fa-users-cog" style="font-size: 28px;"></i>
                <h1 style="font-size: 22px; font-weight: 600;">用户管理</h1>
            </div>
            <div class="user-info" style="display: flex; align-items: center; gap: 12px;">
                <div class="user-avatar" style="width: 42px; height: 42px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 18px;">张</div>
                <div>
                    <div>张经理</div>
                    <div style="font-size: 13px; opacity: 0.8;">销售主管</div>
                </div>
            </div>
        </header>

        <div class="main-content" style="flex: 1; display: flex; overflow: hidden;">
            <div class="sidebar" style="width: 260px; background: white; padding: 25px 20px; display: flex; flex-direction: column; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); z-index: 10; height: 100%; overflow-y: auto;">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 13px; color: #6c757d; margin-bottom: 5px;">用户总数</div>
                    <div style="font-size: 28px; font-weight: 700; color: #3f37c9;" id="total-users">0</div>
                    <div style="font-size: 14px; color: #6c757d;">管理用户</div>
                </div>

                <ul class="nav-menu" style="list-style: none; margin-top: 20px;">
                    <li style="margin-bottom: 8px;"><a href="index.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-home" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 首页</a></li>
                    <li style="margin-bottom: 8px;"><a href="salesanalytics.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-chart-line" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 销售分析</a></li>
                    <li style="margin-bottom: 8px;"><a href="customers.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 客户管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="order.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-shopping-cart" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 订单管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="parts.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-boxes" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 配件库管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="settings.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 系统设置</a></li>
                    <li style="margin-bottom: 8px;"><a href="user.html" class="active" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #4361ee; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #4361ee;"></i> 用户管理</a></li>
                </ul>

                <div style="margin-top: 30px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h3 style="font-size: 15px; margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-filter" style="color: #3f37c9;"></i> 用户筛选
                    </h3>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <button class="btn btn-outline filter-btn" data-status="all">全部用户</button>
                        <button class="btn btn-outline filter-btn" data-status="enabled">启用用户</button>
                        <button class="btn btn-outline filter-btn" data-status="disabled">禁用用户</button>
                    </div>
                </div>
            </div>

            <div class="content-area" style="flex: 1; padding: 30px; overflow-y: auto;">
                <div class="module-container active" id="user-module">
                    <!-- 内容由userModule.js动态生成 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 引入必要的脚本 -->
    <script src="database.js"></script>
    <script src="notification.js"></script>
    <script src="userModule.js"></script>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 等待所有脚本加载完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 检查必要的函数是否存在
                if (!window.dbFunctions) {
                    throw new Error('数据库函数未加载');
                }

                if (!window.userFunctions) {
                    throw new Error('用户模块未加载');
                }

                // 初始化数据库
                await window.dbFunctions.initDB();

                // 加载用户模块
                await window.userFunctions.loadUsers();

                console.log('用户管理页面初始化完成');
            } catch (error) {
                console.error('初始化失败:', error);
                if (window.showNotification) {
                    showNotification('初始化失败', '系统初始化时出错: ' + error.message, 'danger');
                } else {
                    alert('初始化失败: ' + error.message);
                }
            }
        });
    </script>
</body>
</html>
