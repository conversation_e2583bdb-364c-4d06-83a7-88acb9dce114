<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>销售分析功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f7fb;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #4361ee;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3f37c9;
        }
    </style>
</head>
<body>
    <h1>销售分析功能测试</h1>
    
    <div class="test-container">
        <h2>模块加载测试</h2>
        <button onclick="testModuleLoading()">测试模块加载</button>
        <div id="module-test-results"></div>
    </div>

    <div class="test-container">
        <h2>数据库连接测试</h2>
        <button onclick="testDatabaseConnection()">测试数据库连接</button>
        <div id="db-test-results"></div>
    </div>

    <div class="test-container">
        <h2>图表渲染测试</h2>
        <button onclick="testChartRendering()">测试图表渲染</button>
        <div id="chart-test-results"></div>
    </div>

    <div class="test-container">
        <h2>数据计算测试</h2>
        <button onclick="testDataCalculation()">测试数据计算</button>
        <div id="data-test-results"></div>
    </div>

    <div class="test-container">
        <h2>页面布局测试</h2>
        <button onclick="testPageLayout()">测试页面布局</button>
        <div id="layout-test-results"></div>
    </div>

    <script src="database.js"></script>
    <script src="salesAnalyticsModule.js"></script>
    <script>
        function addTestResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        async function testModuleLoading() {
            const container = document.getElementById('module-test-results');
            container.innerHTML = '';

            try {
                // 测试销售分析模块是否存在
                if (typeof window.salesAnalytics !== 'undefined') {
                    addTestResult('module-test-results', '✓ 销售分析模块加载成功', 'success');
                } else {
                    addTestResult('module-test-results', '✗ 销售分析模块未加载', 'error');
                }

                // 测试数据库函数是否存在
                if (typeof window.dbFunctions !== 'undefined') {
                    addTestResult('module-test-results', '✓ 数据库模块加载成功', 'success');
                } else {
                    addTestResult('module-test-results', '✗ 数据库模块未加载', 'error');
                }

                // 测试Chart.js是否存在
                if (typeof Chart !== 'undefined') {
                    addTestResult('module-test-results', '✓ Chart.js库加载成功', 'success');
                } else {
                    addTestResult('module-test-results', '✗ Chart.js库未加载', 'error');
                }

            } catch (error) {
                addTestResult('module-test-results', `✗ 模块加载测试失败: ${error.message}`, 'error');
            }
        }

        async function testDatabaseConnection() {
            const container = document.getElementById('db-test-results');
            container.innerHTML = '';

            try {
                if (window.dbFunctions) {
                    await window.dbFunctions.initDB();
                    addTestResult('db-test-results', '✓ 数据库初始化成功', 'success');

                    // 测试数据读取
                    const customers = await window.dbFunctions.getAllCustomerManagement();
                    addTestResult('db-test-results', `✓ 客户数据读取成功，共 ${customers.length} 条记录`, 'success');

                    const showroomEntries = await window.dbFunctions.getAllShowroomEntries();
                    addTestResult('db-test-results', `✓ 展厅数据读取成功，共 ${showroomEntries.length} 条记录`, 'success');

                    const leadEntries = await window.dbFunctions.getAllLeadEntries();
                    addTestResult('db-test-results', `✓ 线索数据读取成功，共 ${leadEntries.length} 条记录`, 'success');

                } else {
                    addTestResult('db-test-results', '✗ 数据库模块未加载', 'error');
                }
            } catch (error) {
                addTestResult('db-test-results', `✗ 数据库连接测试失败: ${error.message}`, 'error');
            }
        }

        async function testChartRendering() {
            const container = document.getElementById('chart-test-results');
            container.innerHTML = '';

            try {
                if (window.salesAnalytics) {
                    // 测试数据加载
                    await window.salesAnalytics.loadAllData();
                    addTestResult('chart-test-results', '✓ 销售分析数据加载成功', 'success');

                    // 测试各个渲染函数是否存在
                    const renderFunctions = [
                        'renderShowroomTrafficTable',
                        'renderAdvisorRankingChart',
                        'renderAdvisorPerformanceTable',
                        'renderSalesFunnelOverview',
                        'renderLeadFunnelOverview',
                        'renderLeadChannelChart',
                        'renderSalesTrendChart',
                        'renderLeadTrendChart'
                    ];

                    renderFunctions.forEach(funcName => {
                        if (typeof window.salesAnalytics[funcName] === 'function') {
                            addTestResult('chart-test-results', `✓ ${funcName} 函数存在`, 'success');
                        } else {
                            addTestResult('chart-test-results', `✗ ${funcName} 函数不存在`, 'error');
                        }
                    });

                } else {
                    addTestResult('chart-test-results', '✗ 销售分析模块未加载', 'error');
                }
            } catch (error) {
                addTestResult('chart-test-results', `✗ 图表渲染测试失败: ${error.message}`, 'error');
            }
        }

        async function testDataCalculation() {
            const container = document.getElementById('data-test-results');
            container.innerHTML = '';

            try {
                if (window.salesAnalytics) {
                    await window.salesAnalytics.loadAllData();

                    // 测试销售漏斗数据计算
                    const funnelData = window.salesAnalytics.getSalesFunnelData();
                    addTestResult('data-test-results', `✓ 销售漏斗数据计算成功: 线索${funnelData.leads}个，订单${funnelData.orders}个`, 'success');

                    // 测试月度趋势数据计算
                    const trendData = window.salesAnalytics.getMonthlySalesTrend(6);
                    addTestResult('data-test-results', `✓ 月度趋势数据计算成功: ${trendData.length}个月的数据`, 'success');

                    // 测试销售顾问业绩数据计算
                    const advisorData = window.salesAnalytics.getSalesAdvisorPerformance();
                    addTestResult('data-test-results', `✓ 销售顾问业绩数据计算成功: ${Object.keys(advisorData).length}个顾问`, 'success');

                } else {
                    addTestResult('data-test-results', '✗ 销售分析模块未加载', 'error');
                }
            } catch (error) {
                addTestResult('data-test-results', `✗ 数据计算测试失败: ${error.message}`, 'error');
            }
        }

        function testPageLayout() {
            const container = document.getElementById('layout-test-results');
            container.innerHTML = '';

            try {
                // 在新窗口中打开销售分析页面进行布局测试
                const testWindow = window.open('/salesanalytics.html', '_blank', 'width=1200,height=800');
                
                setTimeout(() => {
                    if (testWindow) {
                        const doc = testWindow.document;
                        
                        // 检查关键元素是否存在
                        const elements = [
                            'showroom-traffic-table',
                            'advisor-ranking-chart',
                            'advisor-performance-table',
                            'sales-funnel-container',
                            'lead-funnel-container',
                            'lead-channel-chart',
                            'sales-trend-chart',
                            'lead-trend-chart'
                        ];

                        elements.forEach(elementId => {
                            const element = doc.getElementById(elementId);
                            if (element) {
                                addTestResult('layout-test-results', `✓ 元素 ${elementId} 存在`, 'success');
                            } else {
                                addTestResult('layout-test-results', `✗ 元素 ${elementId} 不存在`, 'error');
                            }
                        });

                        // 检查网格布局
                        const analyticsRows = doc.querySelectorAll('.analytics-row');
                        if (analyticsRows.length >= 3) {
                            addTestResult('layout-test-results', `✓ 页面布局正确: ${analyticsRows.length}行`, 'success');
                        } else {
                            addTestResult('layout-test-results', `✗ 页面布局不正确: 只有${analyticsRows.length}行`, 'error');
                        }

                        testWindow.close();
                    } else {
                        addTestResult('layout-test-results', '✗ 无法打开测试窗口', 'error');
                    }
                }, 2000);

                addTestResult('layout-test-results', '✓ 页面布局测试已启动，请等待结果...', 'info');

            } catch (error) {
                addTestResult('layout-test-results', `✗ 页面布局测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testModuleLoading();
            }, 1000);
        });
    </script>
</body>
</html>
