// 控制面板功能模块
window.dashboardFunctions = {
    // 图表实例管理
    chartInstances: {},
    
    // 销毁图表
    destroyChart: function(chartId) {
        if (this.chartInstances[chartId]) {
            this.chartInstances[chartId].destroy();
            delete this.chartInstances[chartId];
        }
    },
    
    // 销毁所有图表
    destroyAllCharts: function() {
        Object.keys(this.chartInstances).forEach(chartId => {
            this.destroyChart(chartId);
        });
    },
    
    // 加载控制面板
    loadDashboard: async function() {
        try {
            const customers = await getAllCustomers();
            const testDrives = await getAllTestDrives();
            const quotes = await getAllQuotes();
            const deals = await getAllDeals();
            
            this.renderDashboardContent(customers, testDrives, quotes, deals);
            this.renderCharts(customers, testDrives, quotes, deals);
            
        } catch (error) {
            console.error('加载控制面板失败:', error);
        }
    },
    
    // 渲染控制面板内容
    renderDashboardContent: function(customers, testDrives, quotes, deals) {
        const moduleContainer = document.getElementById('dashboard-module');
        if (!moduleContainer) return;
        
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // 计算今日数据
        const todayCustomers = customers.filter(c => {
            const custDate = new Date(c.date);
            custDate.setHours(0, 0, 0, 0);
            return custDate.getTime() === today.getTime();
        });
        
        const todayTestDrives = testDrives.filter(td => {
            const tdDate = new Date(td.date);
            tdDate.setHours(0, 0, 0, 0);
            return tdDate.getTime() === today.getTime();
        });
        
        moduleContainer.innerHTML = `
            <div class="module-header">
                <h1><i class="fas fa-tachometer-alt"></i> 销售控制面板</h1>
                <div style="font-size: 14px; color: var(--gray);">今日数据概览</div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-filter"></i> 销售漏斗概览</h2>
                    <div style="font-size: 14px; color: var(--gray);">本月数据</div>
                </div>
                
                <div class="funnel-steps" id="funnel-steps">
                    ${this.renderFunnelSteps(customers, testDrives, quotes, deals)}
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-chart-bar"></i> 数据统计</h2>
                    <div>
                        <button class="btn btn-primary" onclick="window.location.href='customers.html'">
                            <i class="fas fa-plus"></i> 添加新客户
                        </button>
                    </div>
                </div>
                
                <div class="stats-grid" id="stats-grid">
                    ${this.renderStatsGrid(customers, testDrives, quotes, deals)}
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-chart-line"></i> 销售趋势</h2>
                </div>
                <div class="chart-container">
                    <canvas id="dashboard-trend-chart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-user-friends"></i> 今日新增客户</h2>
                    <div><i class="fas fa-history"></i></div>
                </div>
                
                <div class="customer-list-content" id="today-customers">
                    ${this.renderTodayCustomers(todayCustomers)}
                </div>
            </div>
        `;
    },
    
    // 渲染漏斗步骤
    renderFunnelSteps: function(customers, testDrives, quotes, deals) {
        const visitCount = customers.length;
        const testDriveCount = testDrives.length;
        const quoteCount = quotes.length;
        const dealCount = deals.length;
        
        const steps = [
            { 
                id: 'visit', 
                title: '到店客户', 
                icon: 'fa-door-open', 
                count: visitCount,
                rate: 100
            },
            { 
                id: 'test-drive', 
                title: '试驾客户', 
                icon: 'fa-steering-wheel', 
                count: testDriveCount,
                rate: visitCount > 0 ? Math.round((testDriveCount / visitCount) * 100) : 0
            },
            { 
                id: 'quote', 
                title: '报价客户', 
                icon: 'fa-file-invoice', 
                count: quoteCount,
                rate: testDriveCount > 0 ? Math.round((quoteCount / testDriveCount) * 100) : 0
            },
            { 
                id: 'deal', 
                title: '成交客户', 
                icon: 'fa-handshake', 
                count: dealCount,
                rate: quoteCount > 0 ? Math.round((dealCount / quoteCount) * 100) : 0
            }
        ];
        
        return steps.map(step => `
            <div class="funnel-step">
                <div class="step-icon">
                    <i class="fas ${step.icon}"></i>
                </div>
                <div class="step-count">${step.count}</div>
                <div class="step-title">${step.title}</div>
                <div class="progress-bar">
                    <div class="progress" style="width: ${step.rate}%; background: ${this.getStatusColor(step.id)};"></div>
                </div>
                <div style="font-size: 13px; color: var(--gray);">
                    ${step.rate}% 转化率
                </div>
            </div>
        `).join('');
    },
    
    // 渲染统计网格
    renderStatsGrid: function(customers, testDrives, quotes, deals) {
        const pendingCustomers = customers.filter(c => c.status === '初次接触' || c.status === '意向客户').length;
        const todayTestDrives = testDrives.filter(td => {
            const today = new Date();
            const tdDate = new Date(td.date);
            return tdDate.toDateString() === today.toDateString();
        }).length;
        const monthlyDeals = deals.filter(d => {
            const dealDate = new Date(d.date);
            const now = new Date();
            return dealDate.getMonth() === now.getMonth() && dealDate.getFullYear() === now.getFullYear();
        }).length;
        const lostCustomers = customers.filter(c => c.status === '已流失').length;
        
        const stats = [
            { title: '待跟进客户', value: pendingCustomers, trend: '需要及时跟进', status: 'info' },
            { title: '今日试驾', value: todayTestDrives, trend: '试驾安排', status: 'warning' },
            { title: '本月成交', value: monthlyDeals, trend: '目标达成中', status: 'success' },
            { title: '流失客户', value: lostCustomers, trend: '需分析原因', status: 'danger' }
        ];
        
        return stats.map(stat => `
            <div class="stat-card ${stat.status}">
                <h3>${stat.title}</h3>
                <div class="value">${stat.value}</div>
                <div style="margin-top: 10px; color: ${this.getStatusColor(stat.status)}; font-size: 14px;">
                    ${stat.trend}
                </div>
            </div>
        `).join('');
    },
    
    // 渲染今日客户
    renderTodayCustomers: function(todayCustomers) {
        if (todayCustomers.length === 0) {
            return '<div style="text-align: center; padding: 20px; color: var(--gray);">今日暂无新客户</div>';
        }
        
        return todayCustomers.map(customer => `
            <div class="customer-item">
                <div class="customer-avatar">${customer.name.charAt(0)}</div>
                <div class="customer-info">
                    <h4>${customer.name}</h4>
                    <p>电话: ${customer.phone} | ${customer.carModel}</p>
                </div>
                <div class="customer-status status-${this.getStatusClass(customer.status)}">
                    ${customer.status}
                </div>
            </div>
        `).join('');
    },
    
    // 渲染图表
    renderCharts: function(customers, testDrives, quotes, deals) {
        this.renderTrendChart(customers, deals);
        // 调用analyticsModule.js中的图表渲染函数
        if (window.analyticsFunctions) {
            window.analyticsFunctions.renderSalesAdvisorChart();
            window.analyticsFunctions.renderMonthlySalesChart(customers, deals);
        }
    },
    
    // 渲染趋势图表
    renderTrendChart: function(customers, deals) {
        const canvas = document.getElementById('dashboard-trend-chart');
        if (!canvas) return;
        
        // 销毁现有图表
        this.destroyChart('dashboard-trend-chart');
        
        const ctx = canvas.getContext('2d');
        
        // 生成最近7天的数据
        const last7Days = [];
        const customerCounts = [];
        const dealCounts = [];
        
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            date.setHours(0, 0, 0, 0);
            
            last7Days.push(date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }));
            
            const dayCustomers = customers.filter(c => {
                const custDate = new Date(c.date);
                custDate.setHours(0, 0, 0, 0);
                return custDate.getTime() === date.getTime();
            }).length;
            
            const dayDeals = deals.filter(d => {
                const dealDate = new Date(d.date);
                dealDate.setHours(0, 0, 0, 0);
                return dealDate.getTime() === date.getTime();
            }).length;
            
            customerCounts.push(dayCustomers);
            dealCounts.push(dayDeals);
        }
        
        this.chartInstances['dashboard-trend-chart'] = new Chart(ctx, {
            type: 'line',
            data: {
                labels: last7Days,
                datasets: [{
                    label: '新增客户',
                    data: customerCounts,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.3,
                    fill: true
                }, {
                    label: '成交客户',
                    data: dealCounts,
                    borderColor: '#2ecc71',
                    backgroundColor: 'rgba(46, 204, 113, 0.1)',
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    },
    
    // 获取状态颜色
    getStatusColor: function(status) {
        const colors = {
            'visit': '#3498db',
            'test-drive': '#f39c12',
            'quote': '#2ecc71',
            'deal': '#27ae60',
            'info': '#3498db',
            'warning': '#f39c12',
            'success': '#2ecc71',
            'danger': '#e74c3c'
        };
        return colors[status] || '#95a5a6';
    },
    
    // 获取状态类名
    getStatusClass: function(status) {
        const statusMap = {
            '初次接触': 'new',
            '意向客户': 'test-drive',
            '试驾客户': 'quote',
            '成交客户': 'deal',
            '已流失': 'lost'
        };
        return statusMap[status] || 'new';
    }
};

// 页面卸载时清理图表
window.addEventListener('beforeunload', function() {
    if (window.dashboardFunctions) {
        window.dashboardFunctions.destroyAllCharts();
    }
});