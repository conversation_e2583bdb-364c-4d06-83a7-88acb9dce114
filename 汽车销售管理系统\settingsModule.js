// 系统设置模块
async function loadSettings() {
    const settingsModule = document.getElementById('settings-module');
    const advisors = await window.db.salesAdvisors.toArray();
    const carModels = await window.db.carModels.toArray();
    
    settingsModule.innerHTML = `
        <div class="module-header">
            <h1><i class="fas fa-cog"></i> 系统设置</h1>
            <div style="font-size: 14px; color: var(--gray);">系统配置与管理</div>
        </div>
        
        <div class="settings-tabs">
            <div class="settings-tab active" data-tab="input-personnel">录入人员</div>
            <div class="settings-tab" data-tab="sales-advisors">销售顾问</div>
            <div class="settings-tab" data-tab="car-models">车型管理</div>
            <div class="settings-tab" data-tab="intentions">意向管理</div>
            <div class="settings-tab" data-tab="competitors">竞品管理</div>
            <div class="settings-tab" data-tab="regions">区域管理</div>
            <div class="settings-tab" data-tab="channels">渠道管理</div>
            <div class="settings-tab" data-tab="lead-channels">线索渠道</div>
            <div class="settings-tab" data-tab="visit-types">来店类型</div>
            <div class="settings-tab" data-tab="test-drive-models">试驾车型</div>
            <div class="settings-tab" data-tab="definitions">定义</div>
        </div>
        
        <div class="settings-content" id="settings-content">
        </div>
    `;
    
    // 绑定标签页点击事件
    const tabs = settingsModule.querySelectorAll('.settings-tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', async () => {
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            const tabName = tab.getAttribute('data-tab');
            switch(tabName) {
                case 'input-personnel':
                    await loadInputPersonnel();
                    break;
                case 'sales-advisors':
                    await loadSalesAdvisors();
                    break;
                case 'car-models':
                    await loadCarModels();
                    break;
                case 'intentions':
                    await loadIntentions();
                    break;
                case 'competitors':
                    await loadCompetitors();
                    break;
                case 'regions':
                    await loadRegions();
                    break;
                case 'channels':
                    await loadChannels();
                    break;
                case 'lead-channels':
                    await loadLeadChannels();
                    break;
                case 'visit-types':
                    await loadVisitTypes();
                    break;
                case 'test-drive-models':
                    await loadTestDriveModels();
                    break;
                case 'definitions':
                    await loadDefinitions();
                    break;
                default:
                    break;
            }
        });
    });
    
    // 默认加载录入人员模块
    loadInputPersonnel();
}

// 录入人员管理模块
async function loadInputPersonnel() {
    const settingsContent = document.getElementById('settings-content');
    try {
        const personnel = await window.dbFunctions.getAllInputPersonnel();
        settingsContent.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-user-edit"></i> 录入人员管理</h2>
                    <button class="btn btn-primary" id="add-personnel-btn">
                        <i class="fas fa-plus"></i> 添加录入人员
                    </button>
                </div>
                <div class="config-table-container">
                    <table class="config-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>姓名</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="personnel-table">
                            ${renderPersonnelTable(personnel)}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        document.getElementById('add-personnel-btn').onclick = showPersonnelForm;
    } catch (error) {
        console.error('加载录入人员失败:', error);
        settingsContent.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-user-edit"></i> 录入人员管理</h2>
                </div>
                <div style="padding: 20px; text-align: center; color: var(--danger);">
                    <p>加载录入人员数据失败，请刷新页面重试</p>
                </div>
            </div>
        `;
    }
}

function renderPersonnelTable(personnel) {
    if (!personnel || personnel.length === 0) {
        return `<tr><td colspan="4" style="text-align:center; color: var(--gray);">暂无录入人员数据</td></tr>`;
    }
    return personnel.map((item, index) => `
        <tr>
            <td>IP${item.id.toString().padStart(3, '0')}</td>
            <td>${item.name}</td>
            <td><span class="status-badge ${item.status === '在职' ? 'active' : 'inactive'}">${item.status}</span></td>
            <td class="action-buttons">
                <button class="btn btn-outline" onclick="editPersonnel(${item.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deletePersonnel(${item.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function showPersonnelForm() {
    const settingsModule = document.getElementById('settings-module');
    const formHtml = `
        <div class="card" id="personnel-form-card">
            <div class="card-header">
                <h2><i class="fas fa-user-edit"></i> 添加录入人员</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-row">
                    <div class="form-group">
                        <label>姓名 *</label>
                        <input type="text" id="personnel-name" placeholder="请输入录入人员姓名" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>状态 *</label>
                        <select id="personnel-status" style="width: 100%;">
                            <option value="在职">在职</option>
                            <option value="离职">离职</option>
                        </select>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="save-personnel-btn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button class="btn" id="cancel-personnel-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;
    settingsModule.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('save-personnel-btn').onclick = savePersonnel;
    document.getElementById('cancel-personnel-btn').onclick = () => {
        const formCard = document.getElementById('personnel-form-card');
        if (formCard) formCard.remove();
    };
}

async function savePersonnel() {
    const name = document.getElementById('personnel-name').value.trim();
    const status = document.getElementById('personnel-status').value;

    if (!name || !status) {
        showNotification('输入错误', '请填写所有必填字段', 'danger');
        return;
    }

    try {
        await window.dbFunctions.addInputPersonnel({
            name,
            status
        });
        showNotification('保存成功', '录入人员已添加', 'success');
        const formCard = document.getElementById('personnel-form-card');
        if (formCard) formCard.remove();
        loadInputPersonnel();
    } catch (error) {
        console.error('保存录入人员失败:', error);
        showNotification('保存失败', '保存录入人员时出错', 'danger');
    }
}

async function editPersonnel(id) {
    const settingsModule = document.getElementById('settings-module');
    const personnel = await window.dbFunctions.getInputPersonnelById(id);
    if (!personnel) {
        showNotification('错误', '未找到该录入人员', 'danger');
        return;
    }

    const formHtml = `
        <div class="card" id="personnel-form-card">
            <div class="card-header">
                <h2><i class="fas fa-user-edit"></i> 编辑录入人员</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-row">
                    <div class="form-group">
                        <label>姓名 *</label>
                        <input type="text" id="personnel-name" value="${personnel.name}" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>状态 *</label>
                        <select id="personnel-status" style="width: 100%;">
                            <option value="在职" ${personnel.status === '在职' ? 'selected' : ''}>在职</option>
                            <option value="离职" ${personnel.status === '离职' ? 'selected' : ''}>离职</option>
                        </select>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="update-personnel-btn">
                        <i class="fas fa-save"></i> 更新
                    </button>
                    <button class="btn" id="cancel-personnel-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;
    const oldForm = document.getElementById('personnel-form-card');
    if (oldForm) oldForm.remove();
    settingsModule.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('update-personnel-btn').onclick = () => updatePersonnel(id);
    document.getElementById('cancel-personnel-btn').onclick = () => {
        const formCard = document.getElementById('personnel-form-card');
        if (formCard) formCard.remove();
    };
}

async function updatePersonnel(id) {
    const name = document.getElementById('personnel-name').value.trim();
    const status = document.getElementById('personnel-status').value;

    if (!name || !status) {
        showNotification('输入错误', '请填写所有必填字段', 'danger');
        return;
    }

    try {
        await window.dbFunctions.updateInputPersonnel(id, {
            name,
            status
        });
        showNotification('更新成功', '录入人员信息已更新', 'success');
        const formCard = document.getElementById('personnel-form-card');
        if (formCard) formCard.remove();
        loadInputPersonnel();
    } catch (error) {
        console.error('更新录入人员失败:', error);
        showNotification('更新失败', '更新录入人员信息时出错', 'danger');
    }
}

async function deletePersonnel(id) {
    if (confirm('确定要删除这个录入人员吗？')) {
        try {
            await window.dbFunctions.deleteInputPersonnel(id);
            showNotification('删除成功', '录入人员已删除', 'success');
            loadInputPersonnel();
        } catch (error) {
            console.error('删除录入人员失败:', error);
            showNotification('删除失败', '删除录入人员时出错', 'danger');
        }
    }
}

// 销售顾问管理模块
async function loadSalesAdvisors() {
    const settingsContent = document.getElementById('settings-content');
    const advisors = await window.db.salesAdvisors.toArray();
    settingsContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-users-cog"></i> 销售顾问管理</h2>
                <button class="btn btn-primary" id="add-advisor-btn">
                    <i class="fas fa-plus"></i> 添加顾问
                </button>
            </div>
            <div class="config-table-container">
                <table class="config-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>姓名</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="advisors-table">
                        ${renderAdvisorsTable(advisors)}
                    </tbody>
                </table>
            </div>
        </div>
    `;
    document.getElementById('add-advisor-btn').onclick = showAdvisorForm;
}

// 单独渲染销售顾问模块内容


// 单独渲染车型管理模块内容
function loadSettingsModuleCarModels(carModels) {
    const settingsContent = document.getElementById('settings-content');
    settingsContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-car"></i> 车型管理</h2>
                <button class="btn btn-primary" id="add-car-model-btn">
                    <i class="fas fa-plus"></i> 添加车型
                </button>
            </div>
            <div class="config-table-container">
                <table class="config-table">
                    <thead>
                        <tr>
                            <th>车型名称</th>
                            <th>类别</th>
                            <th>价格区间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="car-models-table">
                        ${renderCarModelsTable(carModels)}
                    </tbody>
                </table>
            </div>
        </div>
    `;
    document.getElementById('add-car-model-btn').onclick = showCarModelForm;
}

function renderAdvisorsTable(advisors) {
    if (!advisors || advisors.length === 0) {
        return `<tr><td colspan="4" style="text-align:center; color: var(--gray);">暂无销售顾问数据</td></tr>`;
    }
    return advisors.map(advisor => `
        <tr>
            <td>SA${advisor.id.toString().padStart(3, '0')}</td>
            <td>${advisor.name}</td>
            <td><span class="status-badge ${advisor.status === '在职' ? 'active' : 'inactive'}">${advisor.status}</span></td>
            <td class="action-buttons">
                <button class="btn btn-outline" onclick="editAdvisor(${advisor.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteAdvisor(${advisor.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function renderCarModelsTable(carModels) {
    if (!carModels || carModels.length === 0) {
        return `<tr><td colspan="2" style="text-align:center; color: var(--gray);">暂无车型数据</td></tr>`;
    }
    return carModels.map(model => `
        <tr>
            <td>${model.name}</td>
            <td class="action-buttons">
                <button class="btn btn-outline" onclick="editCarModel(${model.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteCarModel(${model.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function showAdvisorForm() {
    const settingsModule = document.getElementById('settings-module');
    const formHtml = `
        <div class="card" id="advisor-form-card">
            <div class="card-header">
                <h2><i class="fas fa-user-tie"></i> 添加销售顾问</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-row">
                    <div class="form-group">
                        <label>姓名 *</label>
                        <input type="text" id="advisor-name" placeholder="请输入销售顾问姓名" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>状态 *</label>
                        <select id="advisor-status" style="width: 100%;">
                            <option value="在职">在职</option>
                            <option value="离职">离职</option>
                        </select>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="save-advisor-btn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button class="btn" id="cancel-advisor-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;
    settingsModule.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('save-advisor-btn').onclick = saveAdvisor;
    document.getElementById('cancel-advisor-btn').onclick = () => {
        const formCard = document.getElementById('advisor-form-card');
        if (formCard) formCard.remove();
    };
}

async function saveAdvisor() {
    const name = document.getElementById('advisor-name').value.trim();
    const status = document.getElementById('advisor-status').value;

    if (!name || !status) {
        showNotification('输入错误', '请填写所有必填字段', 'danger');
        return;
    }

    try {
        await window.dbFunctions.addSalesAdvisor({
            name,
            status
        });
        showNotification('保存成功', '销售顾问已添加', 'success');
        const formCard = document.getElementById('advisor-form-card');
        if (formCard) formCard.remove();
        loadSalesAdvisors();
    } catch (error) {
        console.error('保存销售顾问失败:', error);
        showNotification('保存失败', '保存销售顾问时出错', 'danger');
    }
}

// 车型管理模块
async function loadCarModels() {
    const settingsContent = document.getElementById('settings-content');
    const carModels = await window.db.carModels.toArray();
    settingsContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-car"></i> 车型管理</h2>
                <button class="btn btn-primary" id="add-car-model-btn">
                    <i class="fas fa-plus"></i> 添加车型
                </button>
            </div>
            <div class="config-table-container">
                <table class="config-table">
                    <thead>
                        <tr>
                            <th>车型名称</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="car-models-table">
                        ${renderCarModelsTable(carModels)}
                    </tbody>
                </table>
            </div>
        </div>
    `;
    document.getElementById('add-car-model-btn').onclick = showCarModelForm;
}

function showCarModelForm() {
    const settingsModule = document.getElementById('settings-module');
    const formHtml = `
        <div class="card" id="car-model-form-card">
            <div class="card-header">
                <h2><i class="fas fa-car"></i> 添加车型</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-row">
                    <div class="form-group">
                        <label>车型名称 *</label>
                        <input type="text" id="car-model-name" placeholder="请输入车型名称" style="width: 100%;">
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="save-car-model-btn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button class="btn" id="cancel-car-model-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;
    settingsModule.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('save-car-model-btn').onclick = saveCarModel;
    document.getElementById('cancel-car-model-btn').onclick = () => {
        const formCard = document.getElementById('car-model-form-card');
        if (formCard) formCard.remove();
    };
}

async function saveCarModel() {
    const name = document.getElementById('car-model-name').value.trim();

    if (!name) {
        showNotification('输入错误', '请填写车型名称', 'danger');
        return;
    }

    try {
        await window.dbFunctions.addCarModel({
            name
        });
        showNotification('保存成功', '车型已添加', 'success');
        const formCard = document.getElementById('car-model-form-card');
        if (formCard) formCard.remove();
        loadCarModels();
    } catch (error) {
        console.error('保存车型失败:', error);
        showNotification('保存失败', '保存车型时出错', 'danger');
    }
}

async function editCarModel(id) {
    const settingsModule = document.getElementById('settings-module');
    const model = await window.dbFunctions.getCarModelById(id);
    if (!model) {
        showNotification('错误', '未找到该车型', 'danger');
        return;
    }

    const formHtml = `
        <div class="card" id="car-model-form-card">
            <div class="card-header">
                <h2><i class="fas fa-car"></i> 编辑车型</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-row">
                    <div class="form-group">
                        <label>车型名称 *</label>
                        <input type="text" id="car-model-name" value="${model.name}" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>类别 *</label>
                        <select id="car-model-category" style="width: 100%;">
                            <option value="SUV" ${model.category === 'SUV' ? 'selected' : ''}>SUV</option>
                            <option value="轿车" ${model.category === '轿车' ? 'selected' : ''}>轿车</option>
                            <option value="新能源" ${model.category === '新能源' ? 'selected' : ''}>新能源</option>
                            <option value="商务" ${model.category === '商务' ? 'selected' : ''}>商务</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>价格区间 *</label>
                        <input type="text" id="car-model-price-range" value="${model.priceRange}" style="width: 100%;">
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="update-car-model-btn">
                        <i class="fas fa-save"></i> 更新
                    </button>
                    <button class="btn" id="cancel-car-model-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;
    const oldForm = document.getElementById('car-model-form-card');
    if (oldForm) oldForm.remove();
    settingsModule.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('update-car-model-btn').onclick = () => updateCarModel(id);
    document.getElementById('cancel-car-model-btn').onclick = () => {
        const formCard = document.getElementById('car-model-form-card');
        if (formCard) formCard.remove();
    };
}

async function updateCarModel(id) {
    const name = document.getElementById('car-model-name').value.trim();
    const category = document.getElementById('car-model-category').value;
    const priceRange = document.getElementById('car-model-price-range').value.trim();

    if (!name || !category || !priceRange) {
        showNotification('输入错误', '请填写所有必填字段', 'danger');
        return;
    }

    try {
        await window.dbFunctions.updateCarModel(id, {
            name,
            category,
            priceRange
        });
        showNotification('更新成功', '车型信息已更新', 'success');
        const formCard = document.getElementById('car-model-form-card');
        if (formCard) formCard.remove();
        loadSettings();
    } catch (error) {
        console.error('更新车型失败:', error);
        showNotification('更新失败', '更新车型信息时出错', 'danger');
    }
}

async function deleteCarModel(id) {
    if (confirm('确定要删除这个车型吗？')) {
        try {
            await window.dbFunctions.deleteCarModel(id);
            showNotification('删除成功', '车型已删除', 'success');
            loadCarModels();
        } catch (error) {
            console.error('删除车型失败:', error);
            showNotification('删除失败', '删除车型时出错', 'danger');
        }
    }
}

function backupData() {
    showNotification('备份完成', '数据已成功导出到本地', 'success');
}

function restoreData() {
    const fileInput = document.getElementById('restore-file');
    if (fileInput.files.length === 0) {
        showNotification('请选择文件', '请先选择要恢复的备份文件', 'warning');
        return;
    }
    showNotification('恢复完成', '数据已成功恢复', 'success');
}

async function editAdvisor(id) {
    const settingsModule = document.getElementById('settings-module');
    const advisor = await window.dbFunctions.getSalesAdvisorById(id);
    if (!advisor) {
        showNotification('错误', '未找到该销售顾问', 'danger');
        return;
    }

    const formHtml = `
        <div class="card" id="advisor-form-card">
            <div class="card-header">
                <h2><i class="fas fa-user-tie"></i> 编辑销售顾问</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-row">
                    <div class="form-group">
                        <label>姓名 *</label>
                        <input type="text" id="advisor-name" value="${advisor.name}" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>状态 *</label>
                        <select id="advisor-status" style="width: 100%;">
                            <option value="在职" ${advisor.status === '在职' ? 'selected' : ''}>在职</option>
                            <option value="离职" ${advisor.status === '离职' ? 'selected' : ''}>离职</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>入职日期 *</label>
                        <input type="date" id="advisor-date" value="${formatDate(advisor.date)}" style="width: 100%;">
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="update-advisor-btn">
                        <i class="fas fa-save"></i> 更新
                    </button>
                    <button class="btn" id="cancel-advisor-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;
    const oldForm = document.getElementById('advisor-form-card');
    if (oldForm) oldForm.remove();
    settingsModule.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('update-advisor-btn').onclick = () => updateAdvisor(id);
    document.getElementById('cancel-advisor-btn').onclick = () => {
        const formCard = document.getElementById('advisor-form-card');
        if (formCard) formCard.remove();
    };
}

async function updateAdvisor(id) {
    const name = document.getElementById('advisor-name').value.trim();
    const status = document.getElementById('advisor-status').value;
    const date = document.getElementById('advisor-date').value;

    if (!name || !status || !date) {
        showNotification('输入错误', '请填写所有必填字段', 'danger');
        return;
    }

    try {
        await window.dbFunctions.updateSalesAdvisor(id, {
            name,
            status,
            date: new Date(date)
        });
        showNotification('更新成功', '销售顾问信息已更新', 'success');
        const formCard = document.getElementById('advisor-form-card');
        if (formCard) formCard.remove();
        loadSettings();
    } catch (error) {
        console.error('更新销售顾问失败:', error);
        showNotification('更新失败', '更新销售顾问信息时出错', 'danger');
    }
}

async function deleteAdvisor(id) {
    if (confirm('确定要删除这个销售顾问吗？')) {
        try {
            await window.dbFunctions.deleteSalesAdvisor(id);
            showNotification('删除成功', '销售顾问已删除', 'success');
            loadSalesAdvisors();
        } catch (error) {
            console.error('删除销售顾问失败:', error);
            showNotification('删除失败', '删除销售顾问时出错', 'danger');
        }
    }
}



function formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    if (isNaN(d.getTime())) return date; // 如果不是有效日期，返回原值

    const year = d.getFullYear();
    const month = d.getMonth() + 1; // 不补零
    const day = d.getDate(); // 不补零
    return `${year}/${month}/${day}`;
}

// 导出函数
window.settingsFunctions = {
    loadSettings,
    renderAdvisorsTable,
    renderCarModelsTable,
    showAdvisorForm,
    showCarModelForm,
    loadIntentions,
    loadCompetitors,
    loadRegions,
    loadChannels,
    loadVisitTypes,
    loadTestDriveModels,
    backupData,
    restoreData
};

// 意向管理模块
async function loadIntentions() {
    const settingsContent = document.getElementById('settings-content');
    const intentions = await window.dbFunctions.getAllIntentions();
    settingsContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-bullseye"></i> 意向管理</h2>
                <button class="btn btn-primary" id="add-intention-btn">
                    <i class="fas fa-plus"></i> 添加意向
                </button>
            </div>
            <div class="config-table-container">
                <table class="config-table" id="intentions-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>意向名称</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${renderIntentionsTable(intentions)}
                    </tbody>
                </table>
            </div>
        </div>
    `;

    document.getElementById('add-intention-btn').onclick = () => showIntentionForm();
}

function renderIntentionsTable(intentions) {
    if (!intentions || intentions.length === 0) {
        return `<tr><td colspan="3" style="text-align:center; color: var(--gray);">暂无意向数据</td></tr>`;
    }
    return intentions.map((item, index) => `
        <tr>
            <td>${index + 1}</td>
            <td>${item.name}</td>
            <td class="action-buttons">
                <button class="btn btn-outline" onclick="editIntention(${item.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteIntention(${item.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

async function showIntentionForm(editId = null) {
    const settingsContent = document.getElementById('settings-content');
    let intention = { name: '' };

    if (editId !== null) {
        intention = await window.dbFunctions.getIntentionById(editId) || { name: '' };
    }

    const formHtml = `
        <div class="card" id="intention-form-card">
            <div class="card-header">
                <h2><i class="fas fa-bullseye"></i> ${editId !== null ? '编辑' : '添加'} 意向</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-group">
                    <label>意向名称 *</label>
                    <input type="text" id="intention-name" value="${intention.name}" placeholder="请输入意向名称" style="width: 100%;">
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="save-intention-btn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button class="btn" id="cancel-intention-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;

    const oldForm = document.getElementById('intention-form-card');
    if (oldForm) oldForm.remove();
    settingsContent.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('save-intention-btn').onclick = () => saveIntention(editId);
    document.getElementById('cancel-intention-btn').onclick = () => {
        const formCard = document.getElementById('intention-form-card');
        if (formCard) formCard.remove();
    };
}

async function saveIntention(editId) {
    const name = document.getElementById('intention-name').value.trim();
    if (!name) {
        showNotification('输入错误', '请填写意向名称', 'danger');
        return;
    }

    try {
        if (editId !== null) {
            await window.dbFunctions.updateIntention(editId, { name });
            showNotification('更新成功', '意向已更新', 'success');
        } else {
            await window.dbFunctions.addIntention({ name });
            showNotification('保存成功', '意向已添加', 'success');
        }
        const formCard = document.getElementById('intention-form-card');
        if (formCard) formCard.remove();
        loadIntentions();
    } catch (error) {
        console.error('保存意向失败:', error);
        showNotification('保存失败', '保存意向时出错', 'danger');
    }
}

function editIntention(id) {
    showIntentionForm(id);
}

async function deleteIntention(id) {
    if (confirm('确定要删除该意向吗？')) {
        try {
            await window.dbFunctions.deleteIntention(id);
            showNotification('删除成功', '意向已删除', 'success');
            loadIntentions();
        } catch (error) {
            console.error('删除意向失败:', error);
            showNotification('删除失败', '删除意向时出错', 'danger');
        }
    }
}

async function loadCompetitors() {
    const settingsContent = document.getElementById('settings-content');
    const competitors = await window.dbFunctions.getAllCompetitors();
    settingsContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-balance-scale"></i> 竞品管理</h2>
                <button class="btn btn-primary" id="add-competitor-btn">
                    <i class="fas fa-plus"></i> 添加竞品
                </button>
            </div>
            <div class="config-table-container">
                <table class="config-table" id="competitors-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>竞品名称</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${renderCompetitorsTable(competitors)}
                    </tbody>
                </table>
            </div>
        </div>
    `;
    document.getElementById('add-competitor-btn').onclick = () => showCompetitorForm();
}

function renderCompetitorsTable(competitors) {
    if (!competitors || competitors.length === 0) {
        return `<tr><td colspan="3" style="text-align:center; color: var(--gray);">暂无竞品数据</td></tr>`;
    }
    return competitors.map((item, index) => `
        <tr>
            <td>${index + 1}</td>
            <td>${item.name}</td>
            <td class="action-buttons">
                <button class="btn btn-outline" onclick="editCompetitor(${item.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteCompetitor(${item.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

async function showCompetitorForm(editId = null) {
    const settingsContent = document.getElementById('settings-content');
    let competitor = { name: '' };

    if (editId !== null) {
        competitor = await window.dbFunctions.getCompetitorById(editId) || { name: '' };
    }

    const formHtml = `
        <div class="card" id="competitor-form-card">
            <div class="card-header">
                <h2><i class="fas fa-balance-scale"></i> ${editId !== null ? '编辑' : '添加'} 竞品</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-group">
                    <label>竞品名称 *</label>
                    <input type="text" id="competitor-name" value="${competitor.name}" placeholder="请输入竞品名称" style="width: 100%;">
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="save-competitor-btn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button class="btn" id="cancel-competitor-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;

    const oldForm = document.getElementById('competitor-form-card');
    if (oldForm) oldForm.remove();
    settingsContent.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('save-competitor-btn').onclick = () => saveCompetitor(editId);
    document.getElementById('cancel-competitor-btn').onclick = () => {
        const formCard = document.getElementById('competitor-form-card');
        if (formCard) formCard.remove();
    };
}

async function saveCompetitor(editId) {
    const name = document.getElementById('competitor-name').value.trim();
    if (!name) {
        showNotification('输入错误', '请填写竞品名称', 'danger');
        return;
    }

    try {
        if (editId !== null) {
            await window.dbFunctions.updateCompetitor(editId, { name });
            showNotification('更新成功', '竞品已更新', 'success');
        } else {
            await window.dbFunctions.addCompetitor({ name });
            showNotification('保存成功', '竞品已添加', 'success');
        }
        const formCard = document.getElementById('competitor-form-card');
        if (formCard) formCard.remove();
        loadCompetitors();
    } catch (error) {
        console.error('保存竞品失败:', error);
        showNotification('保存失败', '保存竞品时出错', 'danger');
    }
}

function editCompetitor(id) {
    showCompetitorForm(id);
}

async function deleteCompetitor(id) {
    if (confirm('确定要删除该竞品吗？')) {
        try {
            await window.dbFunctions.deleteCompetitor(id);
            showNotification('删除成功', '竞品已删除', 'success');
            loadCompetitors();
        } catch (error) {
            console.error('删除竞品失败:', error);
            showNotification('删除失败', '删除竞品时出错', 'danger');
        }
    }
}

async function loadRegions() {
    const settingsContent = document.getElementById('settings-content');
    const regions = await window.dbFunctions.getAllRegions();
    settingsContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-map-marker-alt"></i> 区域管理</h2>
                <button class="btn btn-primary" id="add-region-btn">
                    <i class="fas fa-plus"></i> 添加区域
                </button>
            </div>
            <div class="config-table-container">
                <table class="config-table" id="regions-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>区域名称</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${renderRegionsTable(regions)}
                    </tbody>
                </table>
            </div>
        </div>
    `;
    document.getElementById('add-region-btn').onclick = () => showRegionForm();
}

function renderRegionsTable(regions) {
    if (!regions || regions.length === 0) {
        return `<tr><td colspan="3" style="text-align:center; color: var(--gray);">暂无区域数据</td></tr>`;
    }
    return regions.map((item, index) => `
        <tr>
            <td>${index + 1}</td>
            <td>${item.name}</td>
            <td class="action-buttons">
                <button class="btn btn-outline" onclick="editRegion(${item.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteRegion(${item.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

async function showRegionForm(editId = null) {
    const settingsContent = document.getElementById('settings-content');
    let region = { name: '' };

    if (editId !== null) {
        region = await window.dbFunctions.getRegionById(editId) || { name: '' };
    }

    const formHtml = `
        <div class="card" id="region-form-card">
            <div class="card-header">
                <h2><i class="fas fa-map-marker-alt"></i> ${editId !== null ? '编辑' : '添加'} 区域</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-group">
                    <label>区域名称 *</label>
                    <input type="text" id="region-name" value="${region.name}" placeholder="请输入区域名称" style="width: 100%;">
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="save-region-btn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button class="btn" id="cancel-region-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;

    const oldForm = document.getElementById('region-form-card');
    if (oldForm) oldForm.remove();
    settingsContent.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('save-region-btn').onclick = () => saveRegion(editId);
    document.getElementById('cancel-region-btn').onclick = () => {
        const formCard = document.getElementById('region-form-card');
        if (formCard) formCard.remove();
    };
}

async function saveRegion(editId) {
    const name = document.getElementById('region-name').value.trim();
    if (!name) {
        showNotification('输入错误', '请填写区域名称', 'danger');
        return;
    }

    try {
        if (editId !== null) {
            await window.dbFunctions.updateRegion(editId, { name });
            showNotification('更新成功', '区域已更新', 'success');
        } else {
            await window.dbFunctions.addRegion({ name });
            showNotification('保存成功', '区域已添加', 'success');
        }
        const formCard = document.getElementById('region-form-card');
        if (formCard) formCard.remove();
        loadRegions();
    } catch (error) {
        console.error('保存区域失败:', error);
        showNotification('保存失败', '保存区域时出错', 'danger');
    }
}

function editRegion(id) {
    showRegionForm(id);
}

async function deleteRegion(id) {
    if (confirm('确定要删除该区域吗？')) {
        try {
            await window.dbFunctions.deleteRegion(id);
            showNotification('删除成功', '区域已删除', 'success');
            loadRegions();
        } catch (error) {
            console.error('删除区域失败:', error);
            showNotification('删除失败', '删除区域时出错', 'danger');
        }
    }
}

async function loadChannels() {
    const settingsContent = document.getElementById('settings-content');
    const channels = await window.dbFunctions.getAllChannels();
    settingsContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-network-wired"></i> 渠道管理</h2>
                <button class="btn btn-primary" id="add-channel-btn">
                    <i class="fas fa-plus"></i> 添加渠道
                </button>
            </div>
            <div class="config-table-container">
                <table class="config-table" id="channels-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>渠道名称</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${renderChannelsTable(channels)}
                    </tbody>
                </table>
            </div>
        </div>
    `;
    document.getElementById('add-channel-btn').onclick = () => showChannelForm();
}

function renderChannelsTable(channels) {
    if (!channels || channels.length === 0) {
        return `<tr><td colspan="3" style="text-align:center; color: var(--gray);">暂无渠道数据</td></tr>`;
    }
    return channels.map((item, index) => `
        <tr>
            <td>${index + 1}</td>
            <td>${item.name}</td>
            <td class="action-buttons">
                <button class="btn btn-outline" onclick="editChannel(${item.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteChannel(${item.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

async function showChannelForm(editId = null) {
    const settingsContent = document.getElementById('settings-content');
    let channel = { name: '' };

    if (editId !== null) {
        channel = await window.dbFunctions.getChannelById(editId) || { name: '' };
    }

    const formHtml = `
        <div class="card" id="channel-form-card">
            <div class="card-header">
                <h2><i class="fas fa-network-wired"></i> ${editId !== null ? '编辑' : '添加'} 渠道</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-group">
                    <label>渠道名称 *</label>
                    <input type="text" id="channel-name" value="${channel.name}" placeholder="请输入渠道名称" style="width: 100%;">
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="save-channel-btn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button class="btn" id="cancel-channel-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;

    const oldForm = document.getElementById('channel-form-card');
    if (oldForm) oldForm.remove();
    settingsContent.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('save-channel-btn').onclick = () => saveChannel(editId);
    document.getElementById('cancel-channel-btn').onclick = () => {
        const formCard = document.getElementById('channel-form-card');
        if (formCard) formCard.remove();
    };
}

async function saveChannel(editId) {
    const name = document.getElementById('channel-name').value.trim();
    if (!name) {
        showNotification('输入错误', '请填写渠道名称', 'danger');
        return;
    }

    try {
        if (editId !== null) {
            await window.dbFunctions.updateChannel(editId, { name });
            showNotification('更新成功', '渠道已更新', 'success');
        } else {
            await window.dbFunctions.addChannel({ name });
            showNotification('保存成功', '渠道已添加', 'success');
        }
        const formCard = document.getElementById('channel-form-card');
        if (formCard) formCard.remove();
        loadChannels();
    } catch (error) {
        console.error('保存渠道失败:', error);
        showNotification('保存失败', '保存渠道时出错', 'danger');
    }
}

function editChannel(id) {
    showChannelForm(id);
}

async function deleteChannel(id) {
    if (confirm('确定要删除该渠道吗？')) {
        try {
            await window.dbFunctions.deleteChannel(id);
            showNotification('删除成功', '渠道已删除', 'success');
            loadChannels();
        } catch (error) {
            console.error('删除渠道失败:', error);
            showNotification('删除失败', '删除渠道时出错', 'danger');
        }
    }
}

async function loadVisitTypes() {
    const settingsContent = document.getElementById('settings-content');
    const visitTypes = await window.dbFunctions.getAllVisitTypes();
    settingsContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-door-open"></i> 来店类型</h2>
                <button class="btn btn-primary" id="add-visit-type-btn">
                    <i class="fas fa-plus"></i> 添加来店类型
                </button>
            </div>
            <div class="config-table-container">
                <table class="config-table" id="visit-types-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>来店类型名称</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${renderVisitTypesTable(visitTypes)}
                    </tbody>
                </table>
            </div>
        </div>
    `;
    document.getElementById('add-visit-type-btn').onclick = () => showVisitTypeForm();
}

function renderVisitTypesTable(visitTypes) {
    if (!visitTypes || visitTypes.length === 0) {
        return `<tr><td colspan="3" style="text-align:center; color: var(--gray);">暂无来店类型数据</td></tr>`;
    }
    return visitTypes.map((item, index) => `
        <tr>
            <td>${index + 1}</td>
            <td>${item.name}</td>
            <td class="action-buttons">
                <button class="btn btn-outline" onclick="editVisitType(${item.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteVisitType(${item.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

async function showVisitTypeForm(editId = null) {
    const settingsContent = document.getElementById('settings-content');
    let visitType = { name: '' };

    if (editId !== null) {
        visitType = await window.dbFunctions.getVisitTypeById(editId) || { name: '' };
    }

    const formHtml = `
        <div class="card" id="visit-type-form-card">
            <div class="card-header">
                <h2><i class="fas fa-door-open"></i> ${editId !== null ? '编辑' : '添加'} 来店类型</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-group">
                    <label>来店类型名称 *</label>
                    <input type="text" id="visit-type-name" value="${visitType.name}" placeholder="请输入来店类型名称" style="width: 100%;">
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="save-visit-type-btn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button class="btn" id="cancel-visit-type-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;

    const oldForm = document.getElementById('visit-type-form-card');
    if (oldForm) oldForm.remove();
    settingsContent.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('save-visit-type-btn').onclick = () => saveVisitType(editId);
    document.getElementById('cancel-visit-type-btn').onclick = () => {
        const formCard = document.getElementById('visit-type-form-card');
        if (formCard) formCard.remove();
    };
}

async function saveVisitType(editId) {
    const name = document.getElementById('visit-type-name').value.trim();
    if (!name) {
        showNotification('输入错误', '请填写来店类型名称', 'danger');
        return;
    }

    try {
        if (editId !== null) {
            await window.dbFunctions.updateVisitType(editId, { name });
            showNotification('更新成功', '来店类型已更新', 'success');
        } else {
            await window.dbFunctions.addVisitType({ name });
            showNotification('保存成功', '来店类型已添加', 'success');
        }
        const formCard = document.getElementById('visit-type-form-card');
        if (formCard) formCard.remove();
        loadVisitTypes();
    } catch (error) {
        console.error('保存来店类型失败:', error);
        showNotification('保存失败', '保存来店类型时出错', 'danger');
    }
}

function editVisitType(id) {
    showVisitTypeForm(id);
}

async function deleteVisitType(id) {
    if (confirm('确定要删除该来店类型吗？')) {
        try {
            await window.dbFunctions.deleteVisitType(id);
            showNotification('删除成功', '来店类型已删除', 'success');
            loadVisitTypes();
        } catch (error) {
            console.error('删除来店类型失败:', error);
            showNotification('删除失败', '删除来店类型时出错', 'danger');
        }
    }
}

async function loadTestDriveModels() {
    const settingsContent = document.getElementById('settings-content');
    const testDriveModels = await window.dbFunctions.getAllTestDriveModels();

    // 加载列设置
    loadTestDriveModelsColumnSettings();

    settingsContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-car"></i> 试驾车型</h2>
                <div style="display: flex; gap: 10px;">
                    <button class="btn btn-secondary" onclick="showTestDriveModelsColumnSettings()" title="列设置">
                        <i class="fas fa-columns"></i>
                    </button>
                    <button class="btn btn-primary" id="add-test-drive-model-btn">
                        <i class="fas fa-plus"></i> 添加试驾车型
                    </button>
                </div>
            </div>
            <div class="config-table-container">
                ${renderTestDriveModelsTableWithColumns(testDriveModels)}
            </div>
        </div>
    `;
    document.getElementById('add-test-drive-model-btn').onclick = () => showTestDriveModelForm();
}

function renderTestDriveModelsTable(testDriveModels) {
    if (!testDriveModels || testDriveModels.length === 0) {
        return `<tr><td colspan="11" style="text-align:center; color: var(--gray);">暂无试驾车型数据</td></tr>`;
    }
    return testDriveModels.map((item, index) => `
        <tr>
            <td>${index + 1}</td>
            <td>${item.plateNumber || '-'}</td>
            <td>${item.name || '-'}</td>
            <td>${item.configuration || '-'}</td>
            <td>${item.vin || '-'}</td>
            <td>${item.exteriorColor || '-'}</td>
            <td>${item.interiorColor || '-'}</td>
            <td>${item.guidePrice ? '¥' + item.guidePrice : '-'}</td>
            <td><span class="status status-${item.status || 'unknown'}">${item.status || '-'}</span></td>
            <td><span class="type type-${item.type || 'unknown'}">${item.type || '-'}</span></td>
            <td class="action-buttons">
                <button class="btn btn-outline" onclick="editTestDriveModel(${item.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteTestDriveModel(${item.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

async function showTestDriveModelForm(editId = null) {
    const settingsContent = document.getElementById('settings-content');
    let model = {
        name: '',
        plateNumber: '',
        configuration: '',
        vin: '',
        exteriorColor: '',
        interiorColor: '',
        factoryOptions: '',
        guidePrice: '',
        status: '服役',
        type: '试驾车',
        invoiceDate: '',
        reportDate: '',
        registrationDate: '',
        expiryDate: '',
        notes: ''
    };

    if (editId !== null) {
        model = await window.dbFunctions.getTestDriveModelById(editId) || model;
    }

    // 获取车型列表用于下拉菜单
    const carModels = await window.dbFunctions.getAllCarModels();

    const formHtml = `
        <div class="card" id="test-drive-model-form-card">
            <div class="card-header">
                <h2><i class="fas fa-car"></i> ${editId !== null ? '编辑' : '添加'} 试驾车型</h2>
            </div>
            <div style="padding: 20px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div class="form-group">
                        <label>车牌 *</label>
                        <input type="text" id="test-drive-model-plateNumber" value="${model.plateNumber || ''}" placeholder="请输入车牌号" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>车型 *</label>
                        <select id="test-drive-model-name" style="width: 100%;">
                            <option value="">请选择车型</option>
                            ${carModels.map(car => `<option value="${car.name}" ${car.name === model.name ? 'selected' : ''}>${car.name}</option>`).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label>配置</label>
                        <input type="text" id="test-drive-model-configuration" value="${model.configuration || ''}" placeholder="请输入配置" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>车架号</label>
                        <input type="text" id="test-drive-model-vin" value="${model.vin || ''}" placeholder="请输入车架号" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>外色</label>
                        <input type="text" id="test-drive-model-exteriorColor" value="${model.exteriorColor || ''}" placeholder="请输入外色" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>内饰</label>
                        <input type="text" id="test-drive-model-interiorColor" value="${model.interiorColor || ''}" placeholder="请输入内饰" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>原厂选装</label>
                        <input type="text" id="test-drive-model-factoryOptions" value="${model.factoryOptions || ''}" placeholder="请输入原厂选装" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>指导价</label>
                        <input type="number" id="test-drive-model-guidePrice" value="${model.guidePrice || ''}" placeholder="请输入指导价" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>状态 *</label>
                        <select id="test-drive-model-status" style="width: 100%;">
                            <option value="服役" ${model.status === '服役' ? 'selected' : ''}>服役</option>
                            <option value="超期" ${model.status === '超期' ? 'selected' : ''}>超期</option>
                            <option value="处置" ${model.status === '处置' ? 'selected' : ''}>处置</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>类型 *</label>
                        <select id="test-drive-model-type" style="width: 100%;">
                            <option value="试驾车" ${model.type === '试驾车' ? 'selected' : ''}>试驾车</option>
                            <option value="代步车" ${model.type === '代步车' ? 'selected' : ''}>代步车</option>
                            <option value="服务车" ${model.type === '服务车' ? 'selected' : ''}>服务车</option>
                            <option value="指定车" ${model.type === '指定车' ? 'selected' : ''}>指定车</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>开票日期</label>
                        <input type="date" id="test-drive-model-invoiceDate" value="${model.invoiceDate || ''}" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>上报日期</label>
                        <input type="date" id="test-drive-model-reportDate" value="${model.reportDate || ''}" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>登记日期</label>
                        <input type="date" id="test-drive-model-registrationDate" value="${model.registrationDate || ''}" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>到期日期</label>
                        <input type="date" id="test-drive-model-expiryDate" value="${model.expiryDate || ''}" style="width: 100%;">
                    </div>
                </div>
                <div class="form-group" style="margin-top: 20px;">
                    <label>备注</label>
                    <textarea id="test-drive-model-notes" placeholder="请输入备注" style="width: 100%; height: 80px;">${model.notes || ''}</textarea>
                </div>
                <div class="action-buttons" style="margin-top: 20px;">
                    <button class="btn btn-primary" id="save-test-drive-model-btn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button class="btn" id="cancel-test-drive-model-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;

    const oldForm = document.getElementById('test-drive-model-form-card');
    if (oldForm) oldForm.remove();
    settingsContent.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('save-test-drive-model-btn').onclick = () => saveTestDriveModel(editId);
    document.getElementById('cancel-test-drive-model-btn').onclick = () => {
        const formCard = document.getElementById('test-drive-model-form-card');
        if (formCard) formCard.remove();
    };
}

async function saveTestDriveModel(editId) {
    const plateNumber = document.getElementById('test-drive-model-plateNumber').value.trim();
    const name = document.getElementById('test-drive-model-name').value.trim();
    const configuration = document.getElementById('test-drive-model-configuration').value.trim();
    const vin = document.getElementById('test-drive-model-vin').value.trim();
    const exteriorColor = document.getElementById('test-drive-model-exteriorColor').value.trim();
    const interiorColor = document.getElementById('test-drive-model-interiorColor').value.trim();
    const factoryOptions = document.getElementById('test-drive-model-factoryOptions').value.trim();
    const guidePrice = document.getElementById('test-drive-model-guidePrice').value.trim();
    const status = document.getElementById('test-drive-model-status').value;
    const type = document.getElementById('test-drive-model-type').value;
    const invoiceDate = document.getElementById('test-drive-model-invoiceDate').value;
    const reportDate = document.getElementById('test-drive-model-reportDate').value;
    const registrationDate = document.getElementById('test-drive-model-registrationDate').value;
    const expiryDate = document.getElementById('test-drive-model-expiryDate').value;
    const notes = document.getElementById('test-drive-model-notes').value.trim();

    if (!plateNumber) {
        showNotification('输入错误', '请填写车牌号', 'danger');
        return;
    }

    if (!name) {
        showNotification('输入错误', '请选择车型', 'danger');
        return;
    }

    const modelData = {
        plateNumber,
        name,
        configuration,
        vin,
        exteriorColor,
        interiorColor,
        factoryOptions,
        guidePrice,
        status,
        type,
        invoiceDate,
        reportDate,
        registrationDate,
        expiryDate,
        notes
    };

    try {
        if (editId !== null) {
            await window.dbFunctions.updateTestDriveModel(editId, modelData);
            showNotification('更新成功', '试驾车型已更新', 'success');
        } else {
            await window.dbFunctions.addTestDriveModel(modelData);
            showNotification('保存成功', '试驾车型已添加', 'success');
        }
        const formCard = document.getElementById('test-drive-model-form-card');
        if (formCard) formCard.remove();
        loadTestDriveModels();
    } catch (error) {
        console.error('保存试驾车型失败:', error);
        showNotification('保存失败', '保存试驾车型时出错', 'danger');
    }
}

function editTestDriveModel(id) {
    showTestDriveModelForm(id);
}

async function deleteTestDriveModel(id) {
    if (confirm('确定要删除该试驾车型吗？')) {
        try {
            await window.dbFunctions.deleteTestDriveModel(id);
            showNotification('删除成功', '试驾车型已删除', 'success');
            loadTestDriveModels();
        } catch (error) {
            console.error('删除试驾车型失败:', error);
            showNotification('删除失败', '删除试驾车型时出错', 'danger');
        }
    }
}

// 定义管理模块
async function loadDefinitions() {
    const settingsContent = document.getElementById('settings-content');
    const definitions = await window.dbFunctions.getAllDefinitions();
    settingsContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-book"></i> 定义管理</h2>
                <button class="btn btn-primary" id="add-definition-btn">
                    <i class="fas fa-plus"></i> 添加定义
                </button>
            </div>
            <div class="config-table-container">
                <table class="config-table" id="definitions-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>来店类型</th>
                            <th>定义</th>
                            <th>意向</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${renderDefinitionsTable(definitions)}
                    </tbody>
                </table>
            </div>
        </div>
    `;
    document.getElementById('add-definition-btn').onclick = () => showDefinitionForm();
}

function renderDefinitionsTable(definitions) {
    if (!definitions || definitions.length === 0) {
        return `<tr><td colspan="5" style="text-align:center; color: var(--gray);">暂无定义数据</td></tr>`;
    }
    return definitions.map((item, index) => `
        <tr>
            <td>${index + 1}</td>
            <td>${item.visitType}</td>
            <td>${item.definition}</td>
            <td>${item.intention}</td>
            <td class="action-buttons">
                <button class="btn btn-outline" onclick="editDefinition(${item.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteDefinition(${item.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

async function showDefinitionForm(editId = null) {
    const settingsContent = document.getElementById('settings-content');
    let definition = { visitType: '', definition: '', intention: '' };

    if (editId !== null) {
        definition = await window.dbFunctions.getDefinitionById(editId) || { visitType: '', definition: '', intention: '' };
    }

    const formHtml = `
        <div class="card" id="definition-form-card">
            <div class="card-header">
                <h2><i class="fas fa-book"></i> ${editId !== null ? '编辑' : '添加'} 定义</h2>
            </div>
            <div style="padding: 20px;">
                <div class="form-row">
                    <div class="form-group">
                        <label>来店类型 *</label>
                        <input type="text" id="definition-visit-type" value="${definition.visitType}" placeholder="请输入来店类型" style="width: 100%;">
                    </div>
                    <div class="form-group">
                        <label>定义 *</label>
                        <textarea id="definition-definition" placeholder="请输入定义" style="width: 100%; height: 80px;">${definition.definition}</textarea>
                    </div>
                    <div class="form-group">
                        <label>意向 *</label>
                        <input type="text" id="definition-intention" value="${definition.intention}" placeholder="请输入意向" style="width: 100%;">
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" id="save-definition-btn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button class="btn" id="cancel-definition-btn" style="background: #eee;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    `;

    const oldForm = document.getElementById('definition-form-card');
    if (oldForm) oldForm.remove();
    settingsContent.insertAdjacentHTML('beforeend', formHtml);

    document.getElementById('save-definition-btn').onclick = () => saveDefinition(editId);
    document.getElementById('cancel-definition-btn').onclick = () => {
        const formCard = document.getElementById('definition-form-card');
        if (formCard) formCard.remove();
    };
}

async function saveDefinition(editId) {
    const visitType = document.getElementById('definition-visit-type').value.trim();
    const definition = document.getElementById('definition-definition').value.trim();
    const intention = document.getElementById('definition-intention').value.trim();

    if (!visitType || !definition || !intention) {
        showNotification('输入错误', '请填写所有必填字段', 'danger');
        return;
    }

    try {
        if (editId !== null) {
            await window.dbFunctions.updateDefinition(editId, { visitType, definition, intention });
            showNotification('更新成功', '定义已更新', 'success');
        } else {
            await window.dbFunctions.addDefinition({ visitType, definition, intention });
            showNotification('保存成功', '定义已添加', 'success');
        }
        const formCard = document.getElementById('definition-form-card');
        if (formCard) formCard.remove();
        loadDefinitions();
    } catch (error) {
        console.error('保存定义失败:', error);
        showNotification('保存失败', '保存定义时出错', 'danger');
    }
}

function editDefinition(id) {
    showDefinitionForm(id);
}

async function deleteDefinition(id) {
    if (confirm('确定要删除该定义吗？')) {
        try {
            await window.dbFunctions.deleteDefinition(id);
            showNotification('删除成功', '定义已删除', 'success');
            loadDefinitions();
        } catch (error) {
            console.error('删除定义失败:', error);
            showNotification('删除失败', '删除定义时出错', 'danger');
        }
    }
}

// 线索渠道管理
async function loadLeadChannels() {
    const settingsContent = document.getElementById('settings-content');
    const leadChannels = await window.dbFunctions.getAllLeadChannels();
    settingsContent.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-share-alt"></i> 线索渠道</h2>
                <button class="btn btn-primary" id="add-lead-channel-btn">
                    <i class="fas fa-plus"></i> 添加线索渠道
                </button>
            </div>
            <div class="config-table-container">
                <table class="config-table" id="lead-channels-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>线索渠道名称</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${renderLeadChannelsTable(leadChannels)}
                    </tbody>
                </table>
            </div>
        </div>
    `;

    // 绑定添加按钮事件
    document.getElementById('add-lead-channel-btn').addEventListener('click', () => {
        showLeadChannelForm();
    });
}

function renderLeadChannelsTable(leadChannels) {
    return leadChannels.map(channel => `
        <tr>
            <td>${channel.id}</td>
            <td>${channel.name}</td>
            <td class="action-buttons">
                <button class="btn btn-outline" onclick="editLeadChannel(${channel.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteLeadChannel(${channel.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function showLeadChannelForm(leadChannel = null) {
    const isEdit = leadChannel !== null;
    const title = isEdit ? '编辑线索渠道' : '添加线索渠道';
    const name = isEdit ? leadChannel.name : '';

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="lead-channel-form">
                    <div class="form-group">
                        <label>线索渠道名称 *</label>
                        <input type="text" name="name" value="${name}" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-primary" onclick="saveLeadChannel(${isEdit ? leadChannel.id : 'null'})">
                            <i class="fas fa-save"></i> 保存
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i> 取消
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

async function saveLeadChannel(id) {
    try {
        const form = document.getElementById('lead-channel-form');
        const formData = new FormData(form);
        const name = formData.get('name').trim();

        if (!name) {
            alert('请输入线索渠道名称');
            return;
        }

        if (id) {
            await window.dbFunctions.updateLeadChannel(id, { name });
            showNotification('更新成功', '线索渠道已更新', 'success');
        } else {
            await window.dbFunctions.addLeadChannel({ name });
            showNotification('添加成功', '线索渠道已添加', 'success');
        }

        document.querySelector('.modal-overlay').remove();
        loadLeadChannels();
    } catch (error) {
        console.error('保存线索渠道失败:', error);
        showNotification('保存失败', '保存线索渠道时出错', 'danger');
    }
}

async function editLeadChannel(id) {
    try {
        const leadChannel = await window.dbFunctions.getLeadChannelById(id);
        if (leadChannel) {
            showLeadChannelForm(leadChannel);
        }
    } catch (error) {
        console.error('获取线索渠道失败:', error);
        showNotification('获取失败', '获取线索渠道信息时出错', 'danger');
    }
}

async function deleteLeadChannel(id) {
    if (confirm('确定要删除这个线索渠道吗？')) {
        try {
            await window.dbFunctions.deleteLeadChannel(id);
            showNotification('删除成功', '线索渠道已删除', 'success');
            loadLeadChannels();
        } catch (error) {
            console.error('删除线索渠道失败:', error);
            showNotification('删除失败', '删除线索渠道时出错', 'danger');
        }
    }
}

// 导出loadSettings函数到全局作用域
window.loadSettings = loadSettings;

// 试驾车型列设置功能
let testDriveModelsColumnSettings = {};

// 试驾车型所有可用列定义
const testDriveModelsAllColumns = {
    serialNumber: { label: '序号', key: 'serialNumber' },
    plateNumber: { label: '车牌', key: 'plateNumber' },
    name: { label: '车型', key: 'name' },
    configuration: { label: '配置', key: 'configuration' },
    vin: { label: '车架号', key: 'vin' },
    exteriorColor: { label: '外色', key: 'exteriorColor' },
    interiorColor: { label: '内饰', key: 'interiorColor' },
    factoryOptions: { label: '出厂选装', key: 'factoryOptions' },
    guidePrice: { label: '指导价', key: 'guidePrice' },
    status: { label: '状态', key: 'status' },
    type: { label: '类型', key: 'type' },
    invoiceDate: { label: '开票日期', key: 'invoiceDate' },
    reportDate: { label: '报备日期', key: 'reportDate' },
    registrationDate: { label: '上牌日期', key: 'registrationDate' },
    expiryDate: { label: '到期日期', key: 'expiryDate' },
    notes: { label: '备注', key: 'notes' }
};

// 加载试驾车型列设置
function loadTestDriveModelsColumnSettings() {
    const saved = localStorage.getItem('testDriveModelsColumnSettings');
    if (saved) {
        testDriveModelsColumnSettings = JSON.parse(saved);
    } else {
        // 默认显示的列
        testDriveModelsColumnSettings = {
            serialNumber: true,
            plateNumber: true,
            name: true,
            configuration: true,
            vin: true,
            exteriorColor: true,
            interiorColor: true,
            guidePrice: true,
            status: true,
            type: true,
            factoryOptions: false,
            invoiceDate: false,
            reportDate: false,
            registrationDate: false,
            expiryDate: false,
            notes: false
        };
    }
}

// 保存试驾车型列设置
function saveTestDriveModelsColumnSettings() {
    localStorage.setItem('testDriveModelsColumnSettings', JSON.stringify(testDriveModelsColumnSettings));
}

// 显示试驾车型列设置界面
function showTestDriveModelsColumnSettings() {
    const modalContent = `
        <div class="column-settings-modal">
            <h3><i class="fas fa-columns"></i> 试驾车型列设置</h3>
            <div style="margin: 20px 0;">
                <p style="color: #6c757d; margin-bottom: 15px;">选择要显示的列：</p>
                <div class="column-checkboxes" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; max-height: 300px; overflow-y: auto;">
                    ${Object.entries(testDriveModelsAllColumns).map(([key, column]) => `
                        <label style="display: flex; align-items: center; padding: 5px;">
                            <input type="checkbox"
                                   id="col-${key}"
                                   ${testDriveModelsColumnSettings[key] ? 'checked' : ''}
                                   style="margin-right: 8px;">
                            <span>${column.label}</span>
                        </label>
                    `).join('')}
                </div>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                <button class="btn btn-primary" onclick="applyTestDriveModelsColumnSettings()">应用设置</button>
            </div>
        </div>
    `;

    // 创建并显示模态框
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-body">
                ${modalContent}
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

// 应用试驾车型列设置
async function applyTestDriveModelsColumnSettings() {
    try {
        // 更新设置
        Object.keys(testDriveModelsAllColumns).forEach(key => {
            const checkbox = document.getElementById(`col-${key}`);
            if (checkbox) {
                testDriveModelsColumnSettings[key] = checkbox.checked;
            }
        });

        // 保存设置
        saveTestDriveModelsColumnSettings();

        // 关闭模态框
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.remove();
        }

        // 重新渲染表格
        const testDriveModels = await window.dbFunctions.getAllTestDriveModels();
        const tableContainer = document.querySelector('.config-table-container');
        if (tableContainer) {
            tableContainer.innerHTML = renderTestDriveModelsTableWithColumns(testDriveModels);
        }

        showNotification('设置成功', '列设置已应用', 'success');

    } catch (error) {
        console.error('应用列设置失败:', error);
        showNotification('设置失败', '应用列设置时出错', 'danger');
    }
}

// 根据列设置渲染试驾车型表格
function renderTestDriveModelsTableWithColumns(testDriveModels) {
    // 获取要显示的列
    const visibleColumns = Object.keys(testDriveModelsAllColumns).filter(key => testDriveModelsColumnSettings[key]);

    if (!testDriveModels || testDriveModels.length === 0) {
        return `
            <table class="config-table" id="test-drive-models-table">
                <thead>
                    <tr>
                        ${visibleColumns.map(key => `<th>${testDriveModelsAllColumns[key].label}</th>`).join('')}
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td colspan="${visibleColumns.length + 1}" style="text-align:center; color: var(--gray);">暂无试驾车型数据</td></tr>
                </tbody>
            </table>
        `;
    }

    return `
        <table class="config-table" id="test-drive-models-table">
            <thead>
                <tr>
                    ${visibleColumns.map(key => `<th>${testDriveModelsAllColumns[key].label}</th>`).join('')}
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${testDriveModels.map((item, index) => `
                    <tr>
                        ${visibleColumns.map(key => {
                            let value = '';
                            switch (key) {
                                case 'serialNumber':
                                    value = index + 1;
                                    break;
                                case 'guidePrice':
                                    value = item.guidePrice ? '¥' + item.guidePrice : '-';
                                    break;
                                case 'status':
                                    value = `<span class="status status-${item.status || 'unknown'}">${item.status || '-'}</span>`;
                                    break;
                                case 'type':
                                    value = `<span class="type type-${item.type || 'unknown'}">${item.type || '-'}</span>`;
                                    break;
                                default:
                                    value = item[key] || '-';
                                    break;
                            }
                            return `<td>${value}</td>`;
                        }).join('')}
                        <td class="action-buttons">
                            <button class="btn btn-outline" onclick="editTestDriveModel(${item.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-danger" onclick="deleteTestDriveModel(${item.id})" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

// 确保模块已加载
console.log('设置模块已加载');
