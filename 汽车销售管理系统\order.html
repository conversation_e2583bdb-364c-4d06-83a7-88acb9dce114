<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>订单管理 - 汽车销售漏斗管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dexie@3.2.2/dist/dexie.min.js"></script>
    <script src="xlsx.full.min.js"></script>
    <link rel="stylesheet" href="styles.css" />
</head>
<body style="height: 100vh; margin: 0;">
    <div class="container" style="flex: 1; display: flex; flex-direction: column; height: 100%;">
        <header style="background: linear-gradient(135deg, #4361ee, #3f37c9); color: white; padding: 15px 30px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
            <div class="logo" style="display: flex; align-items: center; gap: 15px;">
                <i class="fas fa-trophy" style="font-size: 28px;"></i>
                <h1 style="font-size: 22px; font-weight: 600;">订单管理</h1>
            </div>
            <div class="user-info" style="display: flex; align-items: center; gap: 12px;">
                <div class="user-avatar" style="width: 42px; height: 42px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 18px;">张</div>
                <div>
                    <div>张经理</div>
                    <div style="font-size: 13px; opacity: 0.8;">销售主管</div>
                </div>
            </div>
        </header>
        
        <div class="main-content" style="flex: 1; display: flex; overflow: hidden;">
            <div class="sidebar" style="width: 260px; background: white; padding: 25px 20px; display: flex; flex-direction: column; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); z-index: 10; height: 100%; overflow-y: auto;">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 13px; color: #6c757d; margin-bottom: 5px;">本月成交</div>
                    <div style="font-size: 28px; font-weight: 700; color: #3f37c9;" id="monthly-deals">0</div>
                    <div style="font-size: 14px; color: #6c757d;">成交台数</div>
                </div>
                
                <ul class="nav-menu" style="list-style: none; margin-top: 20px;">
                    <li style="margin-bottom: 8px;"><a href="index.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-home" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 首页</a></li>
                    <li style="margin-bottom: 8px;"><a href="salesanalytics.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-chart-line" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 销售分析</a></li>
                    <li style="margin-bottom: 8px;"><a href="customers.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 客户管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="order.html" class="active" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #4361ee; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-shopping-cart" style="margin-right: 12px; width: 20px; text-align: center; color: #4361ee;"></i> 订单管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="parts.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-boxes" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 配件库管理</a></li>
                    <li style="margin-bottom: 8px;"><a href="settings.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 系统设置</a></li>
                    <li style="margin-bottom: 8px;"><a href="user.html" style="display: flex; align-items: center; padding: 12px 15px; text-decoration: none; color: #212529; border-radius: 8px; transition: all 0.3s ease; font-size: 15px; font-weight: 500;"><i class="fas fa-users-cog" style="margin-right: 12px; width: 20px; text-align: center; color: #6c757d;"></i> 用户管理</a></li>
                </ul>
                
                <div style="margin-top: 30px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h3 style="font-size: 15px; margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-chart-bar" style="color: #3f37c9;"></i> 成交统计
                    </h3>
                    <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 8px;">
                        <span>月度营收:</span>
                        <span id="monthly-revenue">¥0</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 8px;">
                        <span>平均单价:</span>
                        <span id="avg-price">¥0</span>
                    </div>
                    <div class="progress-bar" style="height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden; margin-top: 5px;">
                        <div class="progress" id="deal-progress" style="height: 100%; background: #4cc9f0; border-radius: 4px; width: 0%;"></div>
                    </div>
                </div>
            </div>
            
            <div class="content-area" style="flex: 1; padding: 30px; overflow-y: auto;">
                <div class="module-container active" id="order-module">
                    <!-- 内容由orderModule.js动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="database.js"></script>
    <script src="notification.js"></script>
    <script src="orderModule.js"></script>
    <script src="test-data.js"></script>

    <style>
        /* 订单管理标签页样式 */
        .order-tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
        }

        .order-tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #6c757d;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .order-tab:hover {
            color: #4361ee;
            background-color: #f8f9fa;
        }

        .order-tab.active {
            color: #4361ee;
            border-bottom-color: #4361ee;
            background-color: #f8f9fa;
        }

        .tab-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-header h2 {
            margin: 0;
            color: #212529;
            font-size: 18px;
        }

        .targets-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        @media (max-width: 1200px) {
            .targets-container {
                grid-template-columns: 1fr;
            }
        }

        .target-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            min-height: 200px;
            display: flex;
            flex-direction: column;
        }

        .target-section h3 {
            margin: 0 0 15px 0;
            color: #212529;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .target-actions {
            margin-bottom: 15px;
        }

        .targets-table {
            font-size: 12px;
        }

        .targets-table th,
        .targets-table td {
            padding: 8px 6px;
            text-align: center;
        }

        .targets-table th {
            font-size: 11px;
            background-color: #f8f9fa;
        }

        .completion-rate {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            white-space: nowrap;
        }

        .completion-rate.high {
            background-color: #d4edda;
            color: #155724;
        }

        .completion-rate.medium {
            background-color: #fff3cd;
            color: #856404;
        }

        .completion-rate.low {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status.order-正常 { background-color: #d4edda; color: #155724; }
        .status.order-异常 { background-color: #fff3cd; color: #856404; }
        .status.order-取消 { background-color: #f8d7da; color: #721c24; }

        .status.inventory-可售 { background-color: #d4edda; color: #155724; }
        .status.inventory-预售 { background-color: #fff3cd; color: #856404; }
        .status.inventory-整备 { background-color: #f8d7da; color: #721c24; }

        .status.audit-已审核 { background-color: #d4edda; color: #155724; }
        .status.audit-待审核 { background-color: #fff3cd; color: #856404; }
        .status.audit-已拒绝 { background-color: #f8d7da; color: #721c24; }

        .status.delivery-已交付 { background-color: #d4edda; color: #155724; }
        .status.delivery-待交付 { background-color: #fff3cd; color: #856404; }
        .status.delivery-延期 { background-color: #f8d7da; color: #721c24; }

        .status.resource-正常 { background-color: #d4edda; color: #155724; }
        .status.resource-紧缺 { background-color: #fff3cd; color: #856404; }
        .status.resource-缺货 { background-color: #f8d7da; color: #721c24; }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .modal-header h3 {
            margin: 0;
            color: #212529;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #6c757d;
        }

        .modal-close:hover {
            color: #212529;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #212529;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4361ee;
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }
    </style>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 等待所有脚本加载完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 检查必要的函数是否存在
                if (!window.dbFunctions) {
                    throw new Error('数据库函数未加载');
                }

                if (!window.orderFunctions) {
                    throw new Error('订单模块未加载');
                }

                // 初始化数据库
                await window.dbFunctions.initDB();

                // 加载订单模块
                await window.orderFunctions.loadOrders();

                console.log('订单管理页面初始化完成');
            } catch (error) {
                console.error('初始化失败:', error);
                if (window.showNotification) {
                    showNotification('初始化失败', '系统初始化时出错: ' + error.message, 'danger');
                } else {
                    alert('初始化失败: ' + error.message);
                }
            }
        });
    </script>
</body>
</html>
