// 销售分析模块
window.salesAnalytics = {
    // 缓存数据
    cachedData: {
        customers: [],
        showroomEntries: [],
        leadEntries: [],
        orders: [],
        deliveries: [],
        inventory: [],
        targets: {
            delivery: [],
            order: [],
            retail: []
        }
    },

    // 初始化销售分析模块
    init: async function() {
        try {
            await this.loadAllData();
            this.renderAnalytics();
            this.bindEvents();
        } catch (error) {
            console.error('销售分析模块初始化失败:', error);
            throw error;
        }
    },

    // 加载所有数据
    loadAllData: async function() {
        try {
            // 并行加载所有数据
            const [
                customers,
                showroomEntries,
                leadEntries,
                orders,
                deliveries,
                inventory,
                deliveryTargets,
                orderTargets,
                retailTargets
            ] = await Promise.all([
                window.dbFunctions.getAllCustomerManagement(),
                window.dbFunctions.getAllShowroomEntries(),
                window.dbFunctions.getAllLeadEntries(),
                window.dbFunctions.getAllOrderManagement(),
                window.dbFunctions.getAllDeliveryManagement(),
                window.dbFunctions.getAllInventoryManagement(),
                window.dbFunctions.getAllDeliveryTargets(),
                window.dbFunctions.getAllOrderTargets(),
                window.dbFunctions.getAllRetailTargets()
            ]);

            this.cachedData = {
                customers,
                showroomEntries,
                leadEntries,
                orders,
                deliveries,
                inventory,
                targets: {
                    delivery: deliveryTargets,
                    order: orderTargets,
                    retail: retailTargets
                }
            };

            console.log('销售分析数据加载完成:', this.cachedData);
        } catch (error) {
            console.error('加载销售分析数据失败:', error);
            throw error;
        }
    },

    // 获取销售漏斗数据
    getSalesFunnelData: function() {
        const { leadEntries, showroomEntries, orders, deliveries } = this.cachedData;
        
        // 统计各阶段数量
        const leads = leadEntries.filter(entry => entry.isValid).length;
        const showroomVisits = showroomEntries.length;
        // 修复试驾字段判断逻辑 - 检查是否有试驾车型而不是简单的"是"
        const testDrives = showroomEntries.filter(entry =>
            entry.testDrive && entry.testDrive.trim() !== '' && entry.testDrive !== '否').length;
        const ordersCount = orders.length;
        // 修复交车状态判断逻辑 - 统一使用auditStatus字段
        const deliveriesCount = orders.filter(order =>
            order.auditStatus === '已交车' || order.deliveryStatus === '已交车').length;

        return {
            leads,
            showroomVisits,
            testDrives,
            orders: ordersCount,
            deliveries: deliveriesCount,
            conversion: {
                leadsToShowroom: showroomVisits > 0 ? ((showroomVisits / leads) * 100).toFixed(1) : 0,
                showroomToTestDrive: testDrives > 0 ? ((testDrives / showroomVisits) * 100).toFixed(1) : 0,
                testDriveToOrder: ordersCount > 0 ? ((ordersCount / testDrives) * 100).toFixed(1) : 0,
                orderToDelivery: deliveriesCount > 0 ? ((deliveriesCount / ordersCount) * 100).toFixed(1) : 0
            }
        };
    },

    // 获取月度销售趋势数据
    getMonthlySalesTrend: function(months = 12) {
        const { orders, deliveries } = this.cachedData;
        const now = new Date();
        const monthlyData = [];

        for (let i = months - 1; i >= 0; i--) {
            const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            
            const monthOrders = orders.filter(order => {
                const orderDate = new Date(order.orderDate);
                return orderDate.getFullYear() === date.getFullYear() && 
                       orderDate.getMonth() === date.getMonth();
            });

            const monthDeliveries = deliveries.filter(delivery => {
                if (!delivery.deliveryDate) return false;
                const deliveryDate = new Date(delivery.deliveryDate);
                return deliveryDate.getFullYear() === date.getFullYear() && 
                       deliveryDate.getMonth() === date.getMonth();
            });

            monthlyData.push({
                month: monthKey,
                monthName: `${date.getFullYear()}年${date.getMonth() + 1}月`,
                orders: monthOrders.length,
                deliveries: monthDeliveries.length,
                revenue: monthDeliveries.reduce((sum, delivery) => {
                    const order = orders.find(o => o.serialNumber === delivery.serialNumber);
                    return sum + (order ? parseFloat(order.totalPrice || 0) : 0);
                }, 0)
            });
        }

        return monthlyData;
    },

    // 获取客户来源分析数据
    getCustomerSourceAnalysis: function() {
        const { leadEntries, showroomEntries } = this.cachedData;
        const sourceData = {};

        // 统计线索来源
        leadEntries.forEach(entry => {
            const source = entry.channel || '未知';
            if (!sourceData[source]) {
                sourceData[source] = { leads: 0, showroom: 0, conversion: 0 };
            }
            sourceData[source].leads++;
        });

        // 统计展厅来访渠道
        showroomEntries.forEach(entry => {
            const source = entry.channel || '未知';
            if (!sourceData[source]) {
                sourceData[source] = { leads: 0, showroom: 0, conversion: 0 };
            }
            sourceData[source].showroom++;
        });

        // 计算转化率
        Object.keys(sourceData).forEach(source => {
            const data = sourceData[source];
            data.conversion = data.leads > 0 ? ((data.showroom / data.leads) * 100).toFixed(1) : 0;
        });

        return sourceData;
    },

    // 获取销售顾问业绩数据
    getSalesAdvisorPerformance: function() {
        const { orders, deliveries, showroomEntries } = this.cachedData;
        const advisorData = {};

        // 统计订单数据
        orders.forEach(order => {
            const advisor = order.salesAdvisor || '未知';
            if (!advisorData[advisor]) {
                advisorData[advisor] = {
                    orders: 0,
                    deliveries: 0,
                    revenue: 0,
                    showroomVisits: 0
                };
            }
            advisorData[advisor].orders++;
            advisorData[advisor].revenue += parseFloat(order.totalPrice || 0);
        });

        // 统计交付数据
        deliveries.forEach(delivery => {
            if (delivery.orderStatus === '已交付') {
                const order = orders.find(o => o.serialNumber === delivery.serialNumber);
                if (order) {
                    const advisor = order.salesAdvisor || '未知';
                    if (advisorData[advisor]) {
                        advisorData[advisor].deliveries++;
                    }
                }
            }
        });

        // 统计展厅接待数据
        showroomEntries.forEach(entry => {
            const advisor = entry.salesAdvisor || '未知';
            if (!advisorData[advisor]) {
                advisorData[advisor] = {
                    orders: 0,
                    deliveries: 0,
                    revenue: 0,
                    showroomVisits: 0
                };
            }
            advisorData[advisor].showroomVisits++;
        });

        return advisorData;
    },

    // 获取车型销售分析数据
    getVehicleModelAnalysis: function() {
        const { orders, deliveries, inventory } = this.cachedData;
        const modelData = {};

        // 统计订单中的车型
        orders.forEach(order => {
            const model = order.vehicleModel || '未知';
            if (!modelData[model]) {
                modelData[model] = {
                    orders: 0,
                    deliveries: 0,
                    inventory: 0,
                    revenue: 0
                };
            }
            modelData[model].orders++;
            modelData[model].revenue += parseFloat(order.totalPrice || 0);
        });

        // 统计交付中的车型
        deliveries.forEach(delivery => {
            if (delivery.orderStatus === '已交付') {
                const order = orders.find(o => o.serialNumber === delivery.serialNumber);
                if (order) {
                    const model = order.vehicleModel || '未知';
                    if (modelData[model]) {
                        modelData[model].deliveries++;
                    }
                }
            }
        });

        // 统计库存中的车型
        inventory.forEach(item => {
            const model = item.vehicleModel || '未知';
            if (!modelData[model]) {
                modelData[model] = {
                    orders: 0,
                    deliveries: 0,
                    inventory: 0,
                    revenue: 0
                };
            }
            modelData[model].inventory++;
        });

        return modelData;
    },

    // 获取目标完成情况数据
    getTargetCompletionData: function() {
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM格式
        const { targets } = this.cachedData;
        const { orders, deliveries } = this.cachedData;

        // 获取当月目标
        const currentDeliveryTarget = targets.delivery.find(t => t.yearMonth === currentMonth);
        const currentOrderTarget = targets.order.find(t => t.yearMonth === currentMonth);
        const currentRetailTarget = targets.retail.find(t => t.yearMonth === currentMonth);

        // 计算当月实际完成情况
        const currentMonthOrders = orders.filter(order => {
            const orderDate = new Date(order.orderDate);
            return orderDate.toISOString().slice(0, 7) === currentMonth;
        }).length;

        const currentMonthDeliveries = deliveries.filter(delivery => {
            if (!delivery.deliveryDate) return false;
            const deliveryDate = new Date(delivery.deliveryDate);
            return deliveryDate.toISOString().slice(0, 7) === currentMonth;
        }).length;

        const currentMonthRevenue = deliveries.filter(delivery => {
            if (!delivery.deliveryDate) return false;
            const deliveryDate = new Date(delivery.deliveryDate);
            return deliveryDate.toISOString().slice(0, 7) === currentMonth;
        }).reduce((sum, delivery) => {
            const order = orders.find(o => o.serialNumber === delivery.serialNumber);
            return sum + (order ? parseFloat(order.totalPrice || 0) : 0);
        }, 0);

        return {
            delivery: {
                target: currentDeliveryTarget ? currentDeliveryTarget.target : 0,
                actual: currentMonthDeliveries,
                completion: currentDeliveryTarget ?
                    Math.floor((currentMonthDeliveries / currentDeliveryTarget.target) * 100) : 0
            },
            order: {
                target: currentOrderTarget ? currentOrderTarget.target : 0,
                actual: currentMonthOrders,
                completion: currentOrderTarget ?
                    Math.floor((currentMonthOrders / currentOrderTarget.target) * 100) : 0
            },
            retail: {
                target: currentRetailTarget ? currentRetailTarget.target : 0,
                actual: currentMonthRevenue,
                completion: currentRetailTarget ?
                    Math.floor((currentMonthRevenue / currentRetailTarget.target) * 100) : 0
            }
        };
    },

    // 渲染分析页面
    renderAnalytics: function() {
        // HTML内容已经在页面中定义，直接渲染图表和表格
        this.renderAllCharts();
        this.renderAllTables();
    },

    // 渲染所有图表
    renderAllCharts: function() {
        this.renderAdvisorRankingChart();
        this.renderLeadChannelChart();
        this.renderSalesTrendChart();
        this.renderLeadTrendChart();
    },



    // 渲染线索渠道图表
    renderLeadChannelChart: function() {
        const ctx = document.getElementById('lead-channel-chart');
        if (!ctx) return;

        const { leadEntries } = this.cachedData;
        const channelData = {};

        // 统计各渠道线索数量
        leadEntries.forEach(lead => {
            const channel = lead.channel || '未知';
            channelData[channel] = (channelData[channel] || 0) + 1;
        });

        const channels = Object.keys(channelData);
        const counts = Object.values(channelData);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: channels,
                datasets: [{
                    label: '线索数量',
                    data: counts,
                    backgroundColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0',
                        '#06ffa5',
                        '#f77f00',
                        '#fcbf49'
                    ],
                    borderColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0',
                        '#06ffa5',
                        '#f77f00',
                        '#fcbf49'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: '#e9ecef'
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            },
                            maxRotation: 45
                        }
                    }
                }
            }
        });
    },

    // 渲染销售趋势图表
    renderSalesTrendChart: function() {
        const ctx = document.getElementById('sales-trend-chart');
        if (!ctx) return;

        const trendData = this.getMonthlySalesTrend(6); // 最近6个月

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.map(item => item.monthName.replace('年', '/').replace('月', '')),
                datasets: [
                    {
                        label: '新订单客户',
                        data: trendData.map(item => item.orders),
                        borderColor: '#4361ee',
                        backgroundColor: 'rgba(67, 97, 238, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 4,
                        pointBackgroundColor: '#4361ee'
                    },
                    {
                        label: '成交客户',
                        data: trendData.map(item => item.deliveries),
                        borderColor: '#4cc9f0',
                        backgroundColor: 'rgba(76, 201, 240, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 4,
                        pointBackgroundColor: '#4cc9f0'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                size: 10
                            },
                            usePointStyle: true
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: '#e9ecef'
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    },

    // 渲染线索趋势图表
    renderLeadTrendChart: function() {
        const ctx = document.getElementById('lead-trend-chart');
        if (!ctx) return;

        const leadTrendData = this.getLeadTrendData(6); // 最近6个月

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: leadTrendData.map(item => item.monthName.replace('年', '/').replace('月', '')),
                datasets: [
                    {
                        label: '新线索客户',
                        data: leadTrendData.map(item => item.newLeads),
                        borderColor: '#4361ee',
                        backgroundColor: 'rgba(67, 97, 238, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 4,
                        pointBackgroundColor: '#4361ee'
                    },
                    {
                        label: '成交客户',
                        data: leadTrendData.map(item => item.convertedLeads),
                        borderColor: '#4cc9f0',
                        backgroundColor: 'rgba(76, 201, 240, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 4,
                        pointBackgroundColor: '#4cc9f0'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                size: 10
                            },
                            usePointStyle: true
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: '#e9ecef'
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    },

    // 获取线索趋势数据
    getLeadTrendData: function(months = 6) {
        const { leadEntries } = this.cachedData;
        const now = new Date();
        const monthlyData = [];

        for (let i = months - 1; i >= 0; i--) {
            const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

            const monthLeads = leadEntries.filter(lead => {
                const leadDate = new Date(lead.createDate || lead.visitDate);
                return leadDate.getFullYear() === date.getFullYear() &&
                       leadDate.getMonth() === date.getMonth();
            });

            const convertedLeads = monthLeads.filter(lead => lead.status === '已成交');

            monthlyData.push({
                month: monthKey,
                monthName: `${date.getFullYear()}年${date.getMonth() + 1}月`,
                newLeads: monthLeads.length,
                convertedLeads: convertedLeads.length
            });
        }

        return monthlyData;
    },

    // 渲染销售顾问排名图表
    renderAdvisorRankingChart: function() {
        const ctx = document.getElementById('advisor-ranking-chart');
        if (!ctx) return;

        // 获取销售顾问业绩数据
        const performanceData = this.getSalesAdvisorPerformance();
        const advisors = Object.keys(performanceData)
            .sort((a, b) => performanceData[b].orders - performanceData[a].orders)
            .slice(0, 6); // 取前6名

        const orderCounts = advisors.map(advisor => performanceData[advisor].orders);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: advisors,
                datasets: [{
                    label: '订单数量',
                    data: orderCounts,
                    backgroundColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0',
                        '#06ffa5'
                    ],
                    borderColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0',
                        '#06ffa5'
                    ],
                    borderWidth: 1,
                    barThickness: 20
                }]
            },
            options: {
                indexAxis: 'y', // 横向条形图
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: '#e9ecef'
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    },

    // 渲染所有表格
    renderAllTables: function() {
        this.renderShowroomTrafficTable();
        this.renderAdvisorPerformanceTable();
        this.renderVehicleIntentionTable();
        this.renderTestDriveAnalysis();
        this.renderOrderRankingTable();
        this.renderDeliveryStatisticsTable();
        this.renderIntentionCustomerStats();
        this.renderSalesAdvisorKPIs();
        this.renderSalesFunnelAnalysis();
        this.renderLeadsFunnelAnalysis();
        this.renderLeadChannelAnalysis();
        this.renderTrendAnalysis();
    },

    // 渲染展厅客流表格
    renderShowroomTrafficTable: function() {
        const container = document.getElementById('showroom-traffic-table');
        if (!container) return;

        // 计算展厅客流数据
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();

        const monthlyData = this.calculateMonthlyTraffic(currentYear, currentMonth);

        container.innerHTML = `
            <table style="width: 100%; border-collapse: collapse; font-size: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style="background: #f1f3f4; color: #333;">
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: left; font-weight: 600;">客流类型</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">今日</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">本月</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">上月</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">环比</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">全年</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">月占比</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="background: white;">
                        <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333; font-weight: 600;">首次到店</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.todayFirst}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${monthlyData.monthFirst}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #666;">${monthlyData.lastMonthFirst}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: ${monthlyData.firstGrowthRate >= 0 ? '#28a745' : '#dc3545'};">
                            ${monthlyData.firstGrowthRate >= 0 ? '+' : ''}${monthlyData.firstGrowthRate}%
                        </td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.yearFirst}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.firstPercentage}%</td>
                    </tr>
                    <tr style="background: #fafafa;">
                        <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333; font-weight: 600;">再次到店</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.todayReturn}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${monthlyData.monthReturn}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #666;">${monthlyData.lastMonthReturn}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: ${monthlyData.returnGrowthRate >= 0 ? '#28a745' : '#dc3545'};">
                            ${monthlyData.returnGrowthRate >= 0 ? '+' : ''}${monthlyData.returnGrowthRate}%
                        </td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.yearReturn}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${monthlyData.returnPercentage}%</td>
                    </tr>
                    <tr style="background: white; border-top: 2px solid #ddd;">
                        <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333; font-weight: 700;">总计</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${monthlyData.todayTotal}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 700;">${monthlyData.monthTotal}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #666;">${monthlyData.lastMonthTotal}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: ${monthlyData.totalGrowthRate >= 0 ? '#28a745' : '#dc3545'}; font-weight: 600;">
                            ${monthlyData.totalGrowthRate >= 0 ? '+' : ''}${monthlyData.totalGrowthRate}%
                        </td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${monthlyData.yearTotal}</td>
                        <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">100%</td>
                    </tr>
                </tbody>
            </table>
            <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; font-size: 10px;">
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">首次客流转化率</div>
                        <div style="color: #333; font-weight: 600;">${monthlyData.firstPercentage}%</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">再次客流转化率</div>
                        <div style="color: #333; font-weight: 600;">${monthlyData.returnPercentage}%</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">月度总增长</div>
                        <div style="color: ${monthlyData.totalGrowthRate >= 0 ? '#28a745' : '#dc3545'}; font-weight: 600;">
                            ${monthlyData.totalGrowthRate >= 0 ? '+' : ''}${monthlyData.totalGrowthRate}%
                        </div>
                    </div>
                </div>
                <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #ddd; font-size: 10px; color: #666; text-align: center;">
                    数据更新时间: ${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;
    },

    // 计算月度客流数据
    calculateMonthlyTraffic: function(year, month) {
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];

        // 使用展厅录入数据
        const { showroomEntries } = this.cachedData;

        // 筛选当月客户
        const monthCustomers = showroomEntries.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate.getFullYear() === year && entryDate.getMonth() === month;
        });

        // 筛选上月客户（用于环比计算）
        const lastMonth = month === 0 ? 11 : month - 1;
        const lastMonthYear = month === 0 ? year - 1 : year;
        const lastMonthCustomers = showroomEntries.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate.getFullYear() === lastMonthYear && entryDate.getMonth() === lastMonth;
        });

        // 筛选当年客户
        const yearCustomers = showroomEntries.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate.getFullYear() === year;
        });

        // 筛选今日客户
        const todayCustomers = showroomEntries.filter(entry => {
            return entry.entryDate === todayStr;
        });

        // 计算首次和再次到店
        const todayFirst = todayCustomers.filter(c => c.visitType === '首次到店').length;
        const todayReturn = todayCustomers.filter(c => c.visitType === '再次到店').length;

        const monthFirst = monthCustomers.filter(c => c.visitType === '首次到店').length;
        const monthReturn = monthCustomers.filter(c => c.visitType === '再次到店').length;

        const lastMonthFirst = lastMonthCustomers.filter(c => c.visitType === '首次到店').length;
        const lastMonthReturn = lastMonthCustomers.filter(c => c.visitType === '再次到店').length;

        const yearFirst = yearCustomers.filter(c => c.visitType === '首次到店').length;
        const yearReturn = yearCustomers.filter(c => c.visitType === '再次到店').length;

        const monthTotal = monthFirst + monthReturn;
        const lastMonthTotal = lastMonthFirst + lastMonthReturn;
        const firstPercentage = monthTotal > 0 ? Math.round((monthFirst / monthTotal) * 100) : 0;
        const returnPercentage = monthTotal > 0 ? Math.round((monthReturn / monthTotal) * 100) : 0;

        // 计算环比增长率
        const firstGrowthRate = lastMonthFirst > 0 ?
            Math.round(((monthFirst - lastMonthFirst) / lastMonthFirst) * 100) :
            (monthFirst > 0 ? 100 : 0);
        const returnGrowthRate = lastMonthReturn > 0 ?
            Math.round(((monthReturn - lastMonthReturn) / lastMonthReturn) * 100) :
            (monthReturn > 0 ? 100 : 0);
        const totalGrowthRate = lastMonthTotal > 0 ?
            Math.round(((monthTotal - lastMonthTotal) / lastMonthTotal) * 100) :
            (monthTotal > 0 ? 100 : 0);

        return {
            todayFirst,
            todayReturn,
            todayTotal: todayFirst + todayReturn,
            monthFirst,
            monthReturn,
            monthTotal,
            lastMonthFirst,
            lastMonthReturn,
            lastMonthTotal,
            yearFirst,
            yearReturn,
            yearTotal: yearFirst + yearReturn,
            firstPercentage,
            returnPercentage,
            firstGrowthRate,
            returnGrowthRate,
            totalGrowthRate
        };
    },

    // 渲染销售顾问成果分析表格
    renderAdvisorPerformanceTable: function() {
        const container = document.getElementById('advisor-performance-table');
        if (!container) return;

        // 计算销售顾问成果数据
        const advisorStats = this.calculateAdvisorPerformanceStats();

        container.innerHTML = `
            <table style="width: 100%; border-collapse: collapse; font-size: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style="background: #f1f3f4; color: #333;">
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: left; font-weight: 600;">销售顾问</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">A</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">B</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">C</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">F</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">合计</th>
                    </tr>
                </thead>
                <tbody>
                    ${advisorStats.map((advisor, index) => `
                        <tr style="background: ${index % 2 === 0 ? 'white' : '#fafafa'};">
                            <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333;">${advisor.name}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${advisor.gradeA}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${advisor.gradeB}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${advisor.gradeC}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${advisor.gradeF}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${advisor.total}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 8px; font-size: 10px;">
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">销售顾问</div>
                        <div style="color: #333; font-weight: 600;">首次</div>
                        <div style="color: #333;">再次</div>
                        <div style="color: #333;">王五</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">首次到店</div>
                        <div style="color: #333; font-weight: 600;">H</div>
                        <div style="color: #333;">科室</div>
                        <div style="color: #333;">配车</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">接待时间</div>
                        <div style="color: #333; font-weight: 600;">A</div>
                        <div style="color: #333;">配车类型</div>
                        <div style="color: #333;">意向车</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">意向车</div>
                        <div style="color: #333; font-weight: 600;">B</div>
                        <div style="color: #333;">试驾车</div>
                        <div style="color: #333;">成交率</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">试驾车</div>
                        <div style="color: #333; font-weight: 600;">C</div>
                        <div style="color: #333;">试驾率</div>
                        <div style="color: #333;">成交率</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">成交率</div>
                        <div style="color: #333; font-weight: 600;">合计</div>
                        <div style="color: #333;">成交率</div>
                        <div style="color: #333;">成交率</div>
                    </div>
                </div>
            </div>
        `;
    },

    // 计算销售顾问成果统计数据
    calculateAdvisorPerformanceStats: function() {
        const { showroomEntries } = this.cachedData;
        const advisorMap = new Map();

        // 统计各销售顾问的客户意向等级
        showroomEntries.forEach(entry => {
            const advisor = entry.salesAdvisor || '未知';
            if (!advisorMap.has(advisor)) {
                advisorMap.set(advisor, { name: advisor, gradeA: 0, gradeB: 0, gradeC: 0, gradeF: 0, total: 0 });
            }

            const stats = advisorMap.get(advisor);
            const intention = entry.intention || 'F';

            switch (intention) {
                case 'A': stats.gradeA++; break;
                case 'B': stats.gradeB++; break;
                case 'C': stats.gradeC++; break;
                default: stats.gradeF++; break;
            }
            stats.total++;
        });

        return Array.from(advisorMap.values())
            .sort((a, b) => b.total - a.total)
            .slice(0, 5); // 取前5名
    },

    // 获取车型意向分析数据
    getVehicleIntentionAnalysis: function() {
        const { showroomEntries } = this.cachedData;
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();

        const vehicleStats = new Map();

        // 统计所有车型意向数据
        showroomEntries.forEach(entry => {
            if (!entry.intendedModels) return;

            // 处理多个意向车型（可能用逗号分隔）
            const models = entry.intendedModels.split(/[,，]/).map(m => m.trim()).filter(m => m);

            models.forEach(model => {
                if (!vehicleStats.has(model)) {
                    vehicleStats.set(model, {
                        model: model,
                        todayCount: 0,
                        monthCount: 0,
                        yearCount: 0,
                        testDriveCount: 0,
                        testDriveRate: 0
                    });
                }

                const stats = vehicleStats.get(model);
                const entryDate = new Date(entry.entryDate);

                // 统计今日数据
                if (entry.entryDate === todayStr) {
                    stats.todayCount++;
                }

                // 统计本月数据
                if (entryDate.getFullYear() === currentYear && entryDate.getMonth() === currentMonth) {
                    stats.monthCount++;
                }

                // 统计全年数据
                if (entryDate.getFullYear() === currentYear) {
                    stats.yearCount++;
                }

                // 统计试驾数据 - 修复试驾逻辑
                if (entry.testDrive && entry.testDrive !== '否' && entry.testDrive !== '') {
                    // 检查试驾车型是否包含当前意向车型
                    if (entry.testDrive.includes(model)) {
                        stats.testDriveCount++;
                    }
                }
            });
        });

        // 计算试驾率
        vehicleStats.forEach(stats => {
            stats.testDriveRate = stats.monthCount > 0 ?
                Math.round((stats.testDriveCount / stats.monthCount) * 100) : 0;
        });

        return Array.from(vehicleStats.values())
            .sort((a, b) => b.monthCount - a.monthCount)
            .slice(0, 10); // 取前10名
    },

    // 获取试驾率分析数据
    getTestDriveAnalysis: function() {
        const { showroomEntries } = this.cachedData;
        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();

        // 筛选本月数据
        const monthEntries = showroomEntries.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate.getFullYear() === currentYear && entryDate.getMonth() === currentMonth;
        });

        // 筛选全年数据
        const yearEntries = showroomEntries.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate.getFullYear() === currentYear;
        });

        // 计算试驾统计
        const monthTestDrives = monthEntries.filter(entry =>
            entry.testDrive && entry.testDrive !== '否' && entry.testDrive !== '').length;
        const yearTestDrives = yearEntries.filter(entry =>
            entry.testDrive && entry.testDrive !== '否' && entry.testDrive !== '').length;

        const monthTestDriveRate = monthEntries.length > 0 ?
            Math.round((monthTestDrives / monthEntries.length) * 100) : 0;
        const yearTestDriveRate = yearEntries.length > 0 ?
            Math.round((yearTestDrives / yearEntries.length) * 100) : 0;

        return {
            monthTotal: monthEntries.length,
            monthTestDrives,
            monthTestDriveRate,
            yearTotal: yearEntries.length,
            yearTestDrives,
            yearTestDriveRate
        };
    },

    // 渲染车型意向分析表格
    renderVehicleIntentionTable: function() {
        const container = document.getElementById('vehicle-intention-table');
        if (!container) return;

        const vehicleData = this.getVehicleIntentionAnalysis();

        if (vehicleData.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #666;">
                    <i class="fas fa-info-circle"></i> 暂无车型意向数据
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <table style="width: 100%; border-collapse: collapse; font-size: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style="background: #f1f3f4; color: #333;">
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: left; font-weight: 600;">车型</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">今日</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">本月</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">全年</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">试驾率</th>
                    </tr>
                </thead>
                <tbody>
                    ${vehicleData.map((vehicle, index) => `
                        <tr style="background: ${index % 2 === 0 ? 'white' : '#fafafa'};">
                            <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333; font-weight: 600;">${vehicle.model}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${vehicle.todayCount}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${vehicle.monthCount}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${vehicle.yearCount}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: ${vehicle.testDriveRate >= 50 ? '#28a745' : vehicle.testDriveRate >= 30 ? '#ffc107' : '#dc3545'}; font-weight: 600;">
                                ${vehicle.testDriveRate}%
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; font-size: 10px;">
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">热门车型</div>
                        <div style="color: #333; font-weight: 600;">${vehicleData[0]?.model || '暂无'}</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">平均试驾率</div>
                        <div style="color: #333; font-weight: 600;">
                            ${vehicleData.length > 0 ? Math.round(vehicleData.reduce((sum, v) => sum + v.testDriveRate, 0) / vehicleData.length) : 0}%
                        </div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">车型总数</div>
                        <div style="color: #333; font-weight: 600;">${vehicleData.length}</div>
                    </div>
                </div>
                <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #ddd; font-size: 10px; color: #666; text-align: center;">
                    数据更新时间: ${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;
    },

    // 渲染试驾率分析
    renderTestDriveAnalysis: function() {
        const container = document.getElementById('test-drive-analysis');
        if (!container) return;

        const testDriveData = this.getTestDriveAnalysis();

        container.innerHTML = `
            <div style="padding: 20px;">
                <!-- 试驾率概览 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div style="text-align: center; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; color: white;">
                        <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">${testDriveData.monthTestDriveRate}%</div>
                        <div style="font-size: 12px; opacity: 0.9;">本月试驾率</div>
                        <div style="font-size: 10px; opacity: 0.8; margin-top: 5px;">${testDriveData.monthTestDrives}/${testDriveData.monthTotal}</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 8px; color: white;">
                        <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">${testDriveData.yearTestDriveRate}%</div>
                        <div style="font-size: 12px; opacity: 0.9;">全年试驾率</div>
                        <div style="font-size: 10px; opacity: 0.8; margin-top: 5px;">${testDriveData.yearTestDrives}/${testDriveData.yearTotal}</div>
                    </div>
                </div>

                <!-- 试驾率分析表格 -->
                <table style="width: 100%; border-collapse: collapse; font-size: 11px; font-family: Arial, sans-serif;">
                    <thead>
                        <tr style="background: #f1f3f4; color: #333;">
                            <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: left; font-weight: 600;">指标</th>
                            <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">本月</th>
                            <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">全年</th>
                            <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">完成率</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="background: white;">
                            <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333; font-weight: 600;">总客流量</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${testDriveData.monthTotal}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${testDriveData.yearTotal}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #666;">-</td>
                        </tr>
                        <tr style="background: #fafafa;">
                            <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333; font-weight: 600;">试驾客户</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${testDriveData.monthTestDrives}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${testDriveData.yearTestDrives}</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #666;">-</td>
                        </tr>
                        <tr style="background: white;">
                            <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333; font-weight: 600;">试驾转化率</td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: ${testDriveData.monthTestDriveRate >= 50 ? '#28a745' : testDriveData.monthTestDriveRate >= 30 ? '#ffc107' : '#dc3545'}; font-weight: 600;">
                                ${testDriveData.monthTestDriveRate}%
                            </td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: ${testDriveData.yearTestDriveRate >= 50 ? '#28a745' : testDriveData.yearTestDriveRate >= 30 ? '#ffc107' : '#dc3545'}; font-weight: 600;">
                                ${testDriveData.yearTestDriveRate}%
                            </td>
                            <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #666;">
                                ${testDriveData.monthTestDriveRate >= 40 ? '优秀' : testDriveData.monthTestDriveRate >= 25 ? '良好' : '待提升'}
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 10px; color: #666;">
                    <div style="margin-bottom: 5px;"><strong>试驾率评估标准：</strong></div>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                        <div><span style="color: #28a745;">●</span> 优秀：≥40%</div>
                        <div><span style="color: #ffc107;">●</span> 良好：25-39%</div>
                        <div><span style="color: #dc3545;">●</span> 待提升：<25%</div>
                    </div>
                </div>
            </div>
        `;
    },

    // 获取销售顾问订单排名数据
    getSalesAdvisorOrderRanking: function(year, month = null) {
        const { orders } = this.cachedData;

        // 筛选指定时间范围的订单
        const filteredOrders = orders.filter(order => {
            const orderDate = new Date(order.orderDate);
            const orderYear = orderDate.getFullYear();
            const orderMonth = orderDate.getMonth();

            if (month !== null) {
                return orderYear === year && orderMonth === month;
            } else {
                return orderYear === year;
            }
        });

        // 按销售顾问统计订单
        const advisorStats = new Map();

        filteredOrders.forEach(order => {
            const advisor = order.salesAdvisor || '未分配';

            if (!advisorStats.has(advisor)) {
                advisorStats.set(advisor, {
                    advisor: advisor,
                    orderCount: 0,
                    totalAmount: 0,
                    deliveredCount: 0,
                    deliveredAmount: 0,
                    avgOrderAmount: 0,
                    deliveryRate: 0
                });
            }

            const stats = advisorStats.get(advisor);
            stats.orderCount++;

            // 计算订单金额（如果有的话）
            if (order.totalAmount && !isNaN(parseFloat(order.totalAmount))) {
                stats.totalAmount += parseFloat(order.totalAmount);
            }

            // 统计已交车订单
            if (order.deliveryStatus === '已交车' || order.status === '已交车') {
                stats.deliveredCount++;
                if (order.totalAmount && !isNaN(parseFloat(order.totalAmount))) {
                    stats.deliveredAmount += parseFloat(order.totalAmount);
                }
            }
        });

        // 计算平均订单金额和交车率
        advisorStats.forEach(stats => {
            stats.avgOrderAmount = stats.orderCount > 0 ?
                Math.round(stats.totalAmount / stats.orderCount) : 0;
            stats.deliveryRate = stats.orderCount > 0 ?
                Math.round((stats.deliveredCount / stats.orderCount) * 100) : 0;
        });

        return Array.from(advisorStats.values())
            .sort((a, b) => b.orderCount - a.orderCount);
    },

    // 获取交车统计数据
    getDeliveryStatistics: function(year, month = null) {
        const { orders } = this.cachedData;

        // 筛选已交车的订单
        const deliveredOrders = orders.filter(order => {
            const deliveryDate = order.deliveryDate ? new Date(order.deliveryDate) : new Date(order.orderDate);
            const deliveryYear = deliveryDate.getFullYear();
            const deliveryMonth = deliveryDate.getMonth();

            // 修复交车状态判断逻辑 - 统一使用auditStatus字段
            const isDelivered = order.auditStatus === '已交车' || order.deliveryStatus === '已交车' || order.status === '已交车';

            if (month !== null) {
                return isDelivered && deliveryYear === year && deliveryMonth === month;
            } else {
                return isDelivered && deliveryYear === year;
            }
        });

        // 按销售顾问统计交车
        const advisorDeliveries = new Map();

        deliveredOrders.forEach(order => {
            const advisor = order.salesAdvisor || '未分配';

            if (!advisorDeliveries.has(advisor)) {
                advisorDeliveries.set(advisor, {
                    advisor: advisor,
                    deliveryCount: 0,
                    deliveryAmount: 0,
                    avgDeliveryAmount: 0
                });
            }

            const stats = advisorDeliveries.get(advisor);
            stats.deliveryCount++;

            if (order.totalAmount && !isNaN(parseFloat(order.totalAmount))) {
                stats.deliveryAmount += parseFloat(order.totalAmount);
            }
        });

        // 计算平均交车金额
        advisorDeliveries.forEach(stats => {
            stats.avgDeliveryAmount = stats.deliveryCount > 0 ?
                Math.round(stats.deliveryAmount / stats.deliveryCount) : 0;
        });

        return Array.from(advisorDeliveries.values())
            .sort((a, b) => b.deliveryCount - a.deliveryCount);
    },

    // 渲染订单排名表格
    renderOrderRankingTable: function() {
        const container = document.getElementById('order-ranking-table');
        if (!container) return;

        const timeSelector = document.getElementById('order-ranking-time-selector');
        const selectedTime = timeSelector ? timeSelector.value : '本月';

        const today = new Date();
        const currentYear = today.getFullYear();
        const currentMonth = today.getMonth();

        const orderData = selectedTime === '本月' ?
            this.getSalesAdvisorOrderRanking(currentYear, currentMonth) :
            this.getSalesAdvisorOrderRanking(currentYear);

        if (orderData.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #666;">
                    <i class="fas fa-info-circle"></i> 暂无订单数据
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <table style="width: 100%; border-collapse: collapse; font-size: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style="background: #f1f3f4; color: #333;">
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">排名</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: left; font-weight: 600;">销售顾问</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">订单数</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">交车数</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">交车率</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">平均订单额</th>
                    </tr>
                </thead>
                <tbody>
                    ${orderData.slice(0, 10).map((advisor, index) => {
                        let rankIcon = '';
                        let rankStyle = '';
                        if (index === 0) {
                            rankIcon = '🥇';
                            rankStyle = 'background: linear-gradient(135deg, #ffd700, #ffed4e); color: #333;';
                        } else if (index === 1) {
                            rankIcon = '🥈';
                            rankStyle = 'background: linear-gradient(135deg, #c0c0c0, #e8e8e8); color: #333;';
                        } else if (index === 2) {
                            rankIcon = '🥉';
                            rankStyle = 'background: linear-gradient(135deg, #cd7f32, #daa520); color: #333;';
                        } else {
                            rankStyle = index % 2 === 0 ? 'background: white;' : 'background: #fafafa;';
                        }

                        return `
                            <tr style="${rankStyle}">
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">
                                    ${rankIcon} ${index + 1}
                                </td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333; font-weight: 600;">${advisor.advisor}</td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${advisor.orderCount}</td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${advisor.deliveredCount}</td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: ${advisor.deliveryRate >= 80 ? '#28a745' : advisor.deliveryRate >= 60 ? '#ffc107' : '#dc3545'}; font-weight: 600;">
                                    ${advisor.deliveryRate}%
                                </td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">
                                    ${advisor.avgOrderAmount > 0 ? (advisor.avgOrderAmount / 10000).toFixed(1) + '万' : '-'}
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
            <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 8px; font-size: 10px;">
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">总订单数</div>
                        <div style="color: #333; font-weight: 600;">${orderData.reduce((sum, a) => sum + a.orderCount, 0)}</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">总交车数</div>
                        <div style="color: #333; font-weight: 600;">${orderData.reduce((sum, a) => sum + a.deliveredCount, 0)}</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">平均交车率</div>
                        <div style="color: #333; font-weight: 600;">
                            ${orderData.length > 0 ? Math.round(orderData.reduce((sum, a) => sum + a.deliveryRate, 0) / orderData.length) : 0}%
                        </div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #666; margin-bottom: 2px;">参与人数</div>
                        <div style="color: #333; font-weight: 600;">${orderData.length}</div>
                    </div>
                </div>
                <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #ddd; font-size: 10px; color: #666; text-align: center;">
                    统计时间: ${selectedTime} | 数据更新: ${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;

        // 绑定时间选择器事件
        if (timeSelector && !timeSelector.hasAttribute('data-bound')) {
            timeSelector.setAttribute('data-bound', 'true');
            timeSelector.addEventListener('change', () => {
                this.renderOrderRankingTable();
            });
        }
    },

    // 渲染交车统计表格
    renderDeliveryStatisticsTable: function() {
        const container = document.getElementById('delivery-statistics-table');
        if (!container) return;

        const timeSelector = document.getElementById('delivery-stats-time-selector');
        const selectedTime = timeSelector ? timeSelector.value : '本月';

        const today = new Date();
        const currentYear = today.getFullYear();
        const currentMonth = today.getMonth();

        const deliveryData = selectedTime === '本月' ?
            this.getDeliveryStatistics(currentYear, currentMonth) :
            this.getDeliveryStatistics(currentYear);

        if (deliveryData.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #666;">
                    <i class="fas fa-info-circle"></i> 暂无交车数据
                </div>
            `;
            return;
        }

        const totalDeliveries = deliveryData.reduce((sum, a) => sum + a.deliveryCount, 0);
        const totalAmount = deliveryData.reduce((sum, a) => sum + a.deliveryAmount, 0);

        container.innerHTML = `
            <!-- 交车概览 -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                <div style="text-align: center; padding: 15px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 8px; color: white;">
                    <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">${totalDeliveries}</div>
                    <div style="font-size: 12px; opacity: 0.9;">${selectedTime}交车总数</div>
                </div>
                <div style="text-align: center; padding: 15px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 8px; color: white;">
                    <div style="font-size: 20px; font-weight: bold; margin-bottom: 5px;">${(totalAmount / 10000).toFixed(1)}万</div>
                    <div style="font-size: 12px; opacity: 0.9;">${selectedTime}交车金额</div>
                </div>
            </div>

            <!-- 交车排名表格 -->
            <table style="width: 100%; border-collapse: collapse; font-size: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style="background: #f1f3f4; color: #333;">
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">排名</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: left; font-weight: 600;">销售顾问</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">交车数</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">交车金额</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">平均单价</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">占比</th>
                    </tr>
                </thead>
                <tbody>
                    ${deliveryData.slice(0, 10).map((advisor, index) => {
                        let rankIcon = '';
                        let rankStyle = '';
                        if (index === 0) {
                            rankIcon = '🏆';
                            rankStyle = 'background: linear-gradient(135deg, #ffd700, #ffed4e); color: #333;';
                        } else if (index === 1) {
                            rankIcon = '🥈';
                            rankStyle = 'background: linear-gradient(135deg, #c0c0c0, #e8e8e8); color: #333;';
                        } else if (index === 2) {
                            rankIcon = '🥉';
                            rankStyle = 'background: linear-gradient(135deg, #cd7f32, #daa520); color: #333;';
                        } else {
                            rankStyle = index % 2 === 0 ? 'background: white;' : 'background: #fafafa;';
                        }

                        const percentage = totalDeliveries > 0 ?
                            Math.round((advisor.deliveryCount / totalDeliveries) * 100) : 0;

                        return `
                            <tr style="${rankStyle}">
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">
                                    ${rankIcon} ${index + 1}
                                </td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; color: #333; font-weight: 600;">${advisor.advisor}</td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${advisor.deliveryCount}</td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">
                                    ${advisor.deliveryAmount > 0 ? (advisor.deliveryAmount / 10000).toFixed(1) + '万' : '-'}
                                </td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">
                                    ${advisor.avgDeliveryAmount > 0 ? (advisor.avgDeliveryAmount / 10000).toFixed(1) + '万' : '-'}
                                </td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">
                                    ${percentage}%
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>

            <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 10px; color: #666;">
                <div style="margin-bottom: 5px;"><strong>交车统计说明：</strong></div>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                    <div>• 统计范围：${selectedTime}已交车订单</div>
                    <div>• 排名依据：交车数量优先</div>
                </div>
                <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #ddd; text-align: center;">
                    数据更新时间: ${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;

        // 绑定时间选择器事件
        if (timeSelector && !timeSelector.hasAttribute('data-bound')) {
            timeSelector.setAttribute('data-bound', 'true');
            timeSelector.addEventListener('change', () => {
                this.renderDeliveryStatisticsTable();
            });
        }
    },

    // 获取意向客户统计数据
    getIntentionCustomerStats: function() {
        const { showroomEntries, leadEntries } = this.cachedData;
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();

        // 统计展厅录入的意向客户
        const showroomIntentions = showroomEntries.filter(entry => {
            return entry.intention && entry.intention !== '无意向' && entry.intention !== '';
        });

        // 统计线索录入的意向客户
        const leadIntentions = leadEntries.filter(entry => {
            return entry.intention && entry.intention !== '无意向' && entry.intention !== '';
        });

        // 按意向等级分类
        const intentionLevels = {
            '高意向': { today: 0, month: 0, year: 0 },
            '中意向': { today: 0, month: 0, year: 0 },
            '低意向': { today: 0, month: 0, year: 0 },
            '其他': { today: 0, month: 0, year: 0 }
        };

        // 统计展厅意向客户
        showroomIntentions.forEach(entry => {
            const entryDate = new Date(entry.entryDate);
            let level = '其他';

            if (entry.intention.includes('高') || entry.intention.includes('A')) {
                level = '高意向';
            } else if (entry.intention.includes('中') || entry.intention.includes('B')) {
                level = '中意向';
            } else if (entry.intention.includes('低') || entry.intention.includes('C')) {
                level = '低意向';
            }

            if (entry.entryDate === todayStr) {
                intentionLevels[level].today++;
            }
            if (entryDate.getFullYear() === currentYear && entryDate.getMonth() === currentMonth) {
                intentionLevels[level].month++;
            }
            if (entryDate.getFullYear() === currentYear) {
                intentionLevels[level].year++;
            }
        });

        // 统计线索意向客户
        leadIntentions.forEach(entry => {
            const entryDate = new Date(entry.entryDate);
            let level = '其他';

            if (entry.intention.includes('高') || entry.intention.includes('A')) {
                level = '高意向';
            } else if (entry.intention.includes('中') || entry.intention.includes('B')) {
                level = '中意向';
            } else if (entry.intention.includes('低') || entry.intention.includes('C')) {
                level = '低意向';
            }

            if (entry.entryDate === todayStr) {
                intentionLevels[level].today++;
            }
            if (entryDate.getFullYear() === currentYear && entryDate.getMonth() === currentMonth) {
                intentionLevels[level].month++;
            }
            if (entryDate.getFullYear() === currentYear) {
                intentionLevels[level].year++;
            }
        });

        return intentionLevels;
    },

    // 获取销售顾问关键指标
    getSalesAdvisorKPIs: function() {
        const { showroomEntries, leadEntries, orders } = this.cachedData;
        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();

        const advisorKPIs = new Map();

        // 统计展厅录入数据
        showroomEntries.forEach(entry => {
            const entryDate = new Date(entry.entryDate);
            if (entryDate.getFullYear() !== currentYear || entryDate.getMonth() !== currentMonth) return;

            const advisor = entry.salesAdvisor || '未分配';

            if (!advisorKPIs.has(advisor)) {
                advisorKPIs.set(advisor, {
                    advisor: advisor,
                    totalReceptions: 0,        // 总接待数
                    contactLeft: 0,            // 留资数
                    invitations: 0,            // 邀约数
                    testDrives: 0,             // 试驾数
                    orders: 0,                 // 订单数
                    totalReceptionTime: 0,     // 总接待时长
                    avgReceptionTime: 0,       // 平均接待时长
                    contactRate: 0,            // 留资率
                    invitationRate: 0,         // 邀约率
                    testDriveRate: 0,          // 试驾率
                    conversionRate: 0          // 成交率
                });
            }

            const kpi = advisorKPIs.get(advisor);
            kpi.totalReceptions++;

            // 统计留资（有电话号码的视为留资）
            if (entry.phone && entry.phone.trim() !== '') {
                kpi.contactLeft++;
            }

            // 统计邀约（再次到店的视为邀约成功）
            if (entry.visitType === '再次到店') {
                kpi.invitations++;
            }

            // 统计试驾
            if (entry.testDrive && entry.testDrive !== '否' && entry.testDrive !== '') {
                kpi.testDrives++;
            }

            // 统计接待时长
            if (entry.stayDuration && !isNaN(parseFloat(entry.stayDuration))) {
                kpi.totalReceptionTime += parseFloat(entry.stayDuration);
            }
        });

        // 统计订单数据
        orders.forEach(order => {
            const orderDate = new Date(order.orderDate);
            if (orderDate.getFullYear() !== currentYear || orderDate.getMonth() !== currentMonth) return;

            const advisor = order.salesAdvisor || '未分配';

            if (advisorKPIs.has(advisor)) {
                advisorKPIs.get(advisor).orders++;
            }
        });

        // 计算各项指标
        advisorKPIs.forEach(kpi => {
            kpi.avgReceptionTime = kpi.totalReceptions > 0 ?
                Math.round(kpi.totalReceptionTime / kpi.totalReceptions) : 0;
            kpi.contactRate = kpi.totalReceptions > 0 ?
                Math.round((kpi.contactLeft / kpi.totalReceptions) * 100) : 0;
            kpi.invitationRate = kpi.contactLeft > 0 ?
                Math.round((kpi.invitations / kpi.contactLeft) * 100) : 0;
            kpi.testDriveRate = kpi.totalReceptions > 0 ?
                Math.round((kpi.testDrives / kpi.totalReceptions) * 100) : 0;
            kpi.conversionRate = kpi.totalReceptions > 0 ?
                Math.round((kpi.orders / kpi.totalReceptions) * 100) : 0;
        });

        return Array.from(advisorKPIs.values())
            .sort((a, b) => b.totalReceptions - a.totalReceptions);
    },

    // 渲染意向客户统计
    renderIntentionCustomerStats: function() {
        const container = document.getElementById('intention-customer-stats');
        if (!container) return;

        const intentionData = this.getIntentionCustomerStats();

        const totalToday = Object.values(intentionData).reduce((sum, level) => sum + level.today, 0);
        const totalMonth = Object.values(intentionData).reduce((sum, level) => sum + level.month, 0);
        const totalYear = Object.values(intentionData).reduce((sum, level) => sum + level.year, 0);

        container.innerHTML = `
            <!-- 意向客户概览 -->
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 20px;">
                <div style="text-align: center; padding: 12px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 6px; color: white;">
                    <div style="font-size: 18px; font-weight: bold; margin-bottom: 3px;">${totalToday}</div>
                    <div style="font-size: 10px; opacity: 0.9;">今日意向</div>
                </div>
                <div style="text-align: center; padding: 12px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 6px; color: white;">
                    <div style="font-size: 18px; font-weight: bold; margin-bottom: 3px;">${totalMonth}</div>
                    <div style="font-size: 10px; opacity: 0.9;">本月意向</div>
                </div>
                <div style="text-align: center; padding: 12px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 6px; color: white;">
                    <div style="font-size: 18px; font-weight: bold; margin-bottom: 3px;">${totalYear}</div>
                    <div style="font-size: 10px; opacity: 0.9;">全年意向</div>
                </div>
            </div>

            <!-- 意向客户分级表格 -->
            <table style="width: 100%; border-collapse: collapse; font-size: 11px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style="background: #f1f3f4; color: #333;">
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: left; font-weight: 600;">意向等级</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">今日</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">本月</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">全年</th>
                        <th style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">月占比</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.entries(intentionData).map(([level, data], index) => {
                        const percentage = totalMonth > 0 ? Math.round((data.month / totalMonth) * 100) : 0;
                        let levelColor = '#333';
                        let levelIcon = '';

                        if (level === '高意向') {
                            levelColor = '#dc3545';
                            levelIcon = '🔥';
                        } else if (level === '中意向') {
                            levelColor = '#ffc107';
                            levelIcon = '⭐';
                        } else if (level === '低意向') {
                            levelColor = '#28a745';
                            levelIcon = '💡';
                        } else {
                            levelIcon = '📋';
                        }

                        return `
                            <tr style="background: ${index % 2 === 0 ? 'white' : '#fafafa'};">
                                <td style="padding: 8px 6px; border: 1px solid #ddd; color: ${levelColor}; font-weight: 600;">
                                    ${levelIcon} ${level}
                                </td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${data.today}</td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${data.month}</td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: #333;">${data.year}</td>
                                <td style="padding: 8px 6px; border: 1px solid #ddd; text-align: center; color: ${levelColor}; font-weight: 600;">
                                    ${percentage}%
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>

            <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 10px; color: #666;">
                <div style="margin-bottom: 5px;"><strong>意向等级说明：</strong></div>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                    <div><span style="color: #dc3545;">🔥</span> 高意向：购买意愿强烈</div>
                    <div><span style="color: #ffc107;">⭐</span> 中意向：有购买考虑</div>
                    <div><span style="color: #28a745;">💡</span> 低意向：初步了解阶段</div>
                    <div><span style="color: #333;">📋</span> 其他：待分类客户</div>
                </div>
            </div>
        `;
    },

    // 渲染销售顾问关键指标
    renderSalesAdvisorKPIs: function() {
        const container = document.getElementById('sales-advisor-kpis');
        if (!container) return;

        const kpiData = this.getSalesAdvisorKPIs();

        if (kpiData.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #666;">
                    <i class="fas fa-info-circle"></i> 暂无销售顾问数据
                </div>
            `;
            return;
        }

        // 计算平均指标
        const avgContactRate = kpiData.length > 0 ?
            Math.round(kpiData.reduce((sum, kpi) => sum + kpi.contactRate, 0) / kpiData.length) : 0;
        const avgTestDriveRate = kpiData.length > 0 ?
            Math.round(kpiData.reduce((sum, kpi) => sum + kpi.testDriveRate, 0) / kpiData.length) : 0;
        const avgConversionRate = kpiData.length > 0 ?
            Math.round(kpiData.reduce((sum, kpi) => sum + kpi.conversionRate, 0) / kpiData.length) : 0;

        container.innerHTML = `
            <!-- KPI概览 -->
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 20px;">
                <div style="text-align: center; padding: 12px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 6px; color: white;">
                    <div style="font-size: 18px; font-weight: bold; margin-bottom: 3px;">${avgContactRate}%</div>
                    <div style="font-size: 10px; opacity: 0.9;">平均留资率</div>
                </div>
                <div style="text-align: center; padding: 12px; background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); border-radius: 6px; color: white;">
                    <div style="font-size: 18px; font-weight: bold; margin-bottom: 3px;">${avgTestDriveRate}%</div>
                    <div style="font-size: 10px; opacity: 0.9;">平均试驾率</div>
                </div>
                <div style="text-align: center; padding: 12px; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); border-radius: 6px; color: #333;">
                    <div style="font-size: 18px; font-weight: bold; margin-bottom: 3px;">${avgConversionRate}%</div>
                    <div style="font-size: 10px; opacity: 0.8;">平均成交率</div>
                </div>
            </div>

            <!-- KPI详细表格 -->
            <table style="width: 100%; border-collapse: collapse; font-size: 10px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style="background: #f1f3f4; color: #333;">
                        <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: left; font-weight: 600;">顾问</th>
                        <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">接待</th>
                        <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">留资率</th>
                        <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">邀约率</th>
                        <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">试驾率</th>
                        <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">成交率</th>
                        <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">平均时长</th>
                    </tr>
                </thead>
                <tbody>
                    ${kpiData.slice(0, 8).map((kpi, index) => `
                        <tr style="background: ${index % 2 === 0 ? 'white' : '#fafafa'};">
                            <td style="padding: 6px 4px; border: 1px solid #ddd; color: #333; font-weight: 600; max-width: 60px; overflow: hidden; text-overflow: ellipsis;">
                                ${kpi.advisor}
                            </td>
                            <td style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; color: #333;">${kpi.totalReceptions}</td>
                            <td style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; color: ${kpi.contactRate >= 80 ? '#28a745' : kpi.contactRate >= 60 ? '#ffc107' : '#dc3545'}; font-weight: 600;">
                                ${kpi.contactRate}%
                            </td>
                            <td style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; color: ${kpi.invitationRate >= 30 ? '#28a745' : kpi.invitationRate >= 20 ? '#ffc107' : '#dc3545'}; font-weight: 600;">
                                ${kpi.invitationRate}%
                            </td>
                            <td style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; color: ${kpi.testDriveRate >= 40 ? '#28a745' : kpi.testDriveRate >= 25 ? '#ffc107' : '#dc3545'}; font-weight: 600;">
                                ${kpi.testDriveRate}%
                            </td>
                            <td style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; color: ${kpi.conversionRate >= 15 ? '#28a745' : kpi.conversionRate >= 8 ? '#ffc107' : '#dc3545'}; font-weight: 600;">
                                ${kpi.conversionRate}%
                            </td>
                            <td style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; color: #333;">
                                ${kpi.avgReceptionTime}分钟
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 10px; color: #666;">
                <div style="margin-bottom: 5px;"><strong>KPI评估标准：</strong></div>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                    <div>留资率：<span style="color: #28a745;">优秀≥80%</span> <span style="color: #ffc107;">良好≥60%</span></div>
                    <div>试驾率：<span style="color: #28a745;">优秀≥40%</span> <span style="color: #ffc107;">良好≥25%</span></div>
                    <div>邀约率：<span style="color: #28a745;">优秀≥30%</span> <span style="color: #ffc107;">良好≥20%</span></div>
                    <div>成交率：<span style="color: #28a745;">优秀≥15%</span> <span style="color: #ffc107;">良好≥8%</span></div>
                </div>
                <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #ddd; text-align: center;">
                    数据统计时间：本月 | 更新时间：${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;
    },

    // 获取销售漏斗分析数据
    getSalesFunnelAnalysis: function() {
        const { showroomEntries, orders } = this.cachedData;
        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();
        const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
        const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

        // 筛选本月数据
        const monthShowroomEntries = showroomEntries.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate.getFullYear() === currentYear && entryDate.getMonth() === currentMonth;
        });

        const monthOrders = orders.filter(order => {
            const orderDate = new Date(order.orderDate);
            return orderDate.getFullYear() === currentYear && orderDate.getMonth() === currentMonth;
        });

        // 筛选上月数据（用于环比）
        const lastMonthShowroomEntries = showroomEntries.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate.getFullYear() === lastMonthYear && entryDate.getMonth() === lastMonth;
        });

        const lastMonthOrders = orders.filter(order => {
            const orderDate = new Date(order.orderDate);
            return orderDate.getFullYear() === lastMonthYear && orderDate.getMonth() === lastMonth;
        });

        // 计算本月漏斗数据
        const totalTraffic = monthShowroomEntries.length;
        const testDriveCount = monthShowroomEntries.filter(entry =>
            entry.testDrive && entry.testDrive !== '否' && entry.testDrive !== '').length;
        const quoteCount = monthShowroomEntries.filter(entry =>
            entry.quote && entry.quote !== '否' && entry.quote !== '').length;
        const orderCount = monthOrders.length;

        // 计算上月漏斗数据
        const lastTotalTraffic = lastMonthShowroomEntries.length;
        const lastTestDriveCount = lastMonthShowroomEntries.filter(entry =>
            entry.testDrive && entry.testDrive !== '否' && entry.testDrive !== '').length;
        const lastQuoteCount = lastMonthShowroomEntries.filter(entry =>
            entry.quote && entry.quote !== '否' && entry.quote !== '').length;
        const lastOrderCount = lastMonthOrders.length;

        // 计算转化率
        const testDriveRate = totalTraffic > 0 ? Math.round((testDriveCount / totalTraffic) * 100) : 0;
        const quoteRate = testDriveCount > 0 ? Math.round((quoteCount / testDriveCount) * 100) : 0;
        const orderRate = quoteCount > 0 ? Math.round((orderCount / quoteCount) * 100) : 0;
        const overallRate = totalTraffic > 0 ? Math.round((orderCount / totalTraffic) * 100) : 0;

        // 计算上月转化率
        const lastTestDriveRate = lastTotalTraffic > 0 ? Math.round((lastTestDriveCount / lastTotalTraffic) * 100) : 0;
        const lastQuoteRate = lastTestDriveCount > 0 ? Math.round((lastQuoteCount / lastTestDriveCount) * 100) : 0;
        const lastOrderRate = lastQuoteCount > 0 ? Math.round((lastOrderCount / lastQuoteCount) * 100) : 0;
        const lastOverallRate = lastTotalTraffic > 0 ? Math.round((lastOrderCount / lastTotalTraffic) * 100) : 0;

        // 计算环比增长率
        const trafficGrowth = lastTotalTraffic > 0 ?
            Math.round(((totalTraffic - lastTotalTraffic) / lastTotalTraffic) * 100) :
            (totalTraffic > 0 ? 100 : 0);
        const testDriveGrowth = lastTestDriveCount > 0 ?
            Math.round(((testDriveCount - lastTestDriveCount) / lastTestDriveCount) * 100) :
            (testDriveCount > 0 ? 100 : 0);
        const quoteGrowth = lastQuoteCount > 0 ?
            Math.round(((quoteCount - lastQuoteCount) / lastQuoteCount) * 100) :
            (quoteCount > 0 ? 100 : 0);
        const orderGrowth = lastOrderCount > 0 ?
            Math.round(((orderCount - lastOrderCount) / lastOrderCount) * 100) :
            (orderCount > 0 ? 100 : 0);

        return {
            current: {
                totalTraffic,
                testDriveCount,
                quoteCount,
                orderCount,
                testDriveRate,
                quoteRate,
                orderRate,
                overallRate
            },
            last: {
                totalTraffic: lastTotalTraffic,
                testDriveCount: lastTestDriveCount,
                quoteCount: lastQuoteCount,
                orderCount: lastOrderCount,
                testDriveRate: lastTestDriveRate,
                quoteRate: lastQuoteRate,
                orderRate: lastOrderRate,
                overallRate: lastOverallRate
            },
            growth: {
                trafficGrowth,
                testDriveGrowth,
                quoteGrowth,
                orderGrowth
            }
        };
    },

    // 渲染销售漏斗分析
    renderSalesFunnelAnalysis: function() {
        const container = document.getElementById('sales-funnel-analysis');
        if (!container) return;

        const funnelData = this.getSalesFunnelAnalysis();

        container.innerHTML = `
            <div style="padding: 20px;">
                <!-- 漏斗概览 -->
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 30px;">
                    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; color: white; position: relative;">
                        <div style="font-size: 28px; font-weight: bold; margin-bottom: 8px;">${funnelData.current.totalTraffic}</div>
                        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 5px;">总客流</div>
                        <div style="font-size: 12px; opacity: 0.8;">100%</div>
                        <div style="position: absolute; top: 10px; right: 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px;">
                            ${funnelData.growth.trafficGrowth >= 0 ? '+' : ''}${funnelData.growth.trafficGrowth}%
                        </div>
                    </div>
                    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 10px; color: white; position: relative;">
                        <div style="font-size: 28px; font-weight: bold; margin-bottom: 8px;">${funnelData.current.testDriveCount}</div>
                        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 5px;">试驾客户</div>
                        <div style="font-size: 12px; opacity: 0.8;">${funnelData.current.testDriveRate}%</div>
                        <div style="position: absolute; top: 10px; right: 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px;">
                            ${funnelData.growth.testDriveGrowth >= 0 ? '+' : ''}${funnelData.growth.testDriveGrowth}%
                        </div>
                    </div>
                    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 10px; color: white; position: relative;">
                        <div style="font-size: 28px; font-weight: bold; margin-bottom: 8px;">${funnelData.current.quoteCount}</div>
                        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 5px;">报价客户</div>
                        <div style="font-size: 12px; opacity: 0.8;">${funnelData.current.quoteRate}%</div>
                        <div style="position: absolute; top: 10px; right: 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px;">
                            ${funnelData.growth.quoteGrowth >= 0 ? '+' : ''}${funnelData.growth.quoteGrowth}%
                        </div>
                    </div>
                    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 10px; color: white; position: relative;">
                        <div style="font-size: 28px; font-weight: bold; margin-bottom: 8px;">${funnelData.current.orderCount}</div>
                        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 5px;">成交客户</div>
                        <div style="font-size: 12px; opacity: 0.8;">${funnelData.current.orderRate}%</div>
                        <div style="position: absolute; top: 10px; right: 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px;">
                            ${funnelData.growth.orderGrowth >= 0 ? '+' : ''}${funnelData.growth.orderGrowth}%
                        </div>
                    </div>
                </div>

                <!-- 漏斗转化详细分析 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <!-- 转化率对比表格 -->
                    <div>
                        <h4 style="margin-bottom: 15px; color: #333; font-size: 14px;">
                            <i class="fas fa-chart-line"></i> 转化率对比分析
                        </h4>
                        <table style="width: 100%; border-collapse: collapse; font-size: 11px; font-family: Arial, sans-serif;">
                            <thead>
                                <tr style="background: #f1f3f4; color: #333;">
                                    <th style="padding: 10px 8px; border: 1px solid #ddd; text-align: left; font-weight: 600;">转化环节</th>
                                    <th style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; font-weight: 600;">本月</th>
                                    <th style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; font-weight: 600;">上月</th>
                                    <th style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; font-weight: 600;">环比</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background: white;">
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; color: #333; font-weight: 600;">客流→试驾</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${funnelData.current.testDriveRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #666;">${funnelData.last.testDriveRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: ${(funnelData.current.testDriveRate - funnelData.last.testDriveRate) >= 0 ? '#28a745' : '#dc3545'}; font-weight: 600;">
                                        ${funnelData.current.testDriveRate - funnelData.last.testDriveRate >= 0 ? '+' : ''}${funnelData.current.testDriveRate - funnelData.last.testDriveRate}%
                                    </td>
                                </tr>
                                <tr style="background: #fafafa;">
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; color: #333; font-weight: 600;">试驾→报价</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${funnelData.current.quoteRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #666;">${funnelData.last.quoteRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: ${(funnelData.current.quoteRate - funnelData.last.quoteRate) >= 0 ? '#28a745' : '#dc3545'}; font-weight: 600;">
                                        ${funnelData.current.quoteRate - funnelData.last.quoteRate >= 0 ? '+' : ''}${funnelData.current.quoteRate - funnelData.last.quoteRate}%
                                    </td>
                                </tr>
                                <tr style="background: white;">
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; color: #333; font-weight: 600;">报价→成交</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${funnelData.current.orderRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #666;">${funnelData.last.orderRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: ${(funnelData.current.orderRate - funnelData.last.orderRate) >= 0 ? '#28a745' : '#dc3545'}; font-weight: 600;">
                                        ${funnelData.current.orderRate - funnelData.last.orderRate >= 0 ? '+' : ''}${funnelData.current.orderRate - funnelData.last.orderRate}%
                                    </td>
                                </tr>
                                <tr style="background: #f8f9fa; border-top: 2px solid #ddd;">
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; color: #333; font-weight: 700;">整体转化率</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 700;">${funnelData.current.overallRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #666;">${funnelData.last.overallRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: ${(funnelData.current.overallRate - funnelData.last.overallRate) >= 0 ? '#28a745' : '#dc3545'}; font-weight: 700;">
                                        ${funnelData.current.overallRate - funnelData.last.overallRate >= 0 ? '+' : ''}${funnelData.current.overallRate - funnelData.last.overallRate}%
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 漏斗流失分析 -->
                    <div>
                        <h4 style="margin-bottom: 15px; color: #333; font-size: 14px;">
                            <i class="fas fa-exclamation-triangle"></i> 漏斗流失分析
                        </h4>
                        <div style="background: #f8f9fa; border-radius: 8px; padding: 15px;">
                            <div style="margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="font-size: 12px; color: #666;">客流流失</span>
                                    <span style="font-size: 12px; font-weight: 600; color: #dc3545;">
                                        ${funnelData.current.totalTraffic - funnelData.current.testDriveCount}人 (${100 - funnelData.current.testDriveRate}%)
                                    </span>
                                </div>
                                <div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
                                    <div style="background: #dc3545; height: 100%; width: ${100 - funnelData.current.testDriveRate}%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="font-size: 12px; color: #666;">试驾流失</span>
                                    <span style="font-size: 12px; font-weight: 600; color: #ffc107;">
                                        ${funnelData.current.testDriveCount - funnelData.current.quoteCount}人 (${100 - funnelData.current.quoteRate}%)
                                    </span>
                                </div>
                                <div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
                                    <div style="background: #ffc107; height: 100%; width: ${100 - funnelData.current.quoteRate}%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="font-size: 12px; color: #666;">报价流失</span>
                                    <span style="font-size: 12px; font-weight: 600; color: #fd7e14;">
                                        ${funnelData.current.quoteCount - funnelData.current.orderCount}人 (${100 - funnelData.current.orderRate}%)
                                    </span>
                                </div>
                                <div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
                                    <div style="background: #fd7e14; height: 100%; width: ${100 - funnelData.current.orderRate}%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style="margin-top: 20px; padding: 10px; background: white; border-radius: 6px; border-left: 4px solid #28a745;">
                                <div style="font-size: 11px; color: #666; margin-bottom: 5px;">优化建议</div>
                                <div style="font-size: 10px; color: #333; line-height: 1.4;">
                                    ${funnelData.current.testDriveRate < 30 ? '• 提升试驾邀约话术和技巧<br>' : ''}
                                    ${funnelData.current.quoteRate < 60 ? '• 加强试驾体验和产品介绍<br>' : ''}
                                    ${funnelData.current.orderRate < 40 ? '• 优化报价策略和成交技巧<br>' : ''}
                                    • 定期分析流失原因并制定改进措施
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 12px; background: #f8f9fa; border-radius: 6px; font-size: 10px; color: #666; text-align: center;">
                    数据统计时间：本月 | 环比数据：上月同期 | 更新时间：${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;
    },

    // 获取线索漏斗分析数据
    getLeadsFunnelAnalysis: function() {
        const { leadEntries, showroomEntries, orders } = this.cachedData;
        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();
        const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
        const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

        // 筛选本月线索数据
        const monthLeads = leadEntries.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate.getFullYear() === currentYear && entryDate.getMonth() === currentMonth;
        });

        // 筛选上月线索数据
        const lastMonthLeads = leadEntries.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate.getFullYear() === lastMonthYear && entryDate.getMonth() === lastMonth;
        });

        // 计算本月线索漏斗数据
        const totalLeads = monthLeads.length;
        const validLeads = monthLeads.filter(lead =>
            lead.intention && lead.intention !== '无意向' && lead.intention !== '' &&
            lead.phone && lead.phone.trim() !== '').length;

        // 统计到店转化（线索客户在展厅录入中出现）
        const arrivedLeads = monthLeads.filter(lead => {
            return showroomEntries.some(entry =>
                entry.phone === lead.phone && entry.customerName === lead.customerName);
        }).length;

        // 统计成交转化（线索客户在订单中出现）- 修复字段名称
        const convertedLeads = monthLeads.filter(lead => {
            return orders.some(order =>
                order.phone1 === lead.phone || order.customerName === lead.customerName);
        }).length;

        // 计算上月线索漏斗数据
        const lastTotalLeads = lastMonthLeads.length;
        const lastValidLeads = lastMonthLeads.filter(lead =>
            lead.intention && lead.intention !== '无意向' && lead.intention !== '' &&
            lead.phone && lead.phone.trim() !== '').length;

        const lastArrivedLeads = lastMonthLeads.filter(lead => {
            return showroomEntries.some(entry =>
                entry.phone === lead.phone && entry.customerName === lead.customerName);
        }).length;

        const lastConvertedLeads = lastMonthLeads.filter(lead => {
            return orders.some(order =>
                order.phone1 === lead.phone || order.customerName === lead.customerName);
        }).length;

        // 计算转化率
        const validRate = totalLeads > 0 ? Math.round((validLeads / totalLeads) * 100) : 0;
        const arrivalRate = validLeads > 0 ? Math.round((arrivedLeads / validLeads) * 100) : 0;
        const conversionRate = arrivedLeads > 0 ? Math.round((convertedLeads / arrivedLeads) * 100) : 0;
        const overallRate = totalLeads > 0 ? Math.round((convertedLeads / totalLeads) * 100) : 0;

        // 计算上月转化率
        const lastValidRate = lastTotalLeads > 0 ? Math.round((lastValidLeads / lastTotalLeads) * 100) : 0;
        const lastArrivalRate = lastValidLeads > 0 ? Math.round((lastArrivedLeads / lastValidLeads) * 100) : 0;
        const lastConversionRate = lastArrivedLeads > 0 ? Math.round((lastConvertedLeads / lastArrivedLeads) * 100) : 0;
        const lastOverallRate = lastTotalLeads > 0 ? Math.round((lastConvertedLeads / lastTotalLeads) * 100) : 0;

        // 计算环比增长率
        const leadsGrowth = lastTotalLeads > 0 ?
            Math.round(((totalLeads - lastTotalLeads) / lastTotalLeads) * 100) :
            (totalLeads > 0 ? 100 : 0);
        const validGrowth = lastValidLeads > 0 ?
            Math.round(((validLeads - lastValidLeads) / lastValidLeads) * 100) :
            (validLeads > 0 ? 100 : 0);
        const arrivalGrowth = lastArrivedLeads > 0 ?
            Math.round(((arrivedLeads - lastArrivedLeads) / lastArrivedLeads) * 100) :
            (arrivedLeads > 0 ? 100 : 0);
        const conversionGrowth = lastConvertedLeads > 0 ?
            Math.round(((convertedLeads - lastConvertedLeads) / lastConvertedLeads) * 100) :
            (convertedLeads > 0 ? 100 : 0);

        return {
            current: {
                totalLeads,
                validLeads,
                arrivedLeads,
                convertedLeads,
                validRate,
                arrivalRate,
                conversionRate,
                overallRate
            },
            last: {
                totalLeads: lastTotalLeads,
                validLeads: lastValidLeads,
                arrivedLeads: lastArrivedLeads,
                convertedLeads: lastConvertedLeads,
                validRate: lastValidRate,
                arrivalRate: lastArrivalRate,
                conversionRate: lastConversionRate,
                overallRate: lastOverallRate
            },
            growth: {
                leadsGrowth,
                validGrowth,
                arrivalGrowth,
                conversionGrowth
            }
        };
    },

    // 渲染线索漏斗分析
    renderLeadsFunnelAnalysis: function() {
        const container = document.getElementById('leads-funnel-analysis');
        if (!container) return;

        const funnelData = this.getLeadsFunnelAnalysis();

        container.innerHTML = `
            <div style="padding: 20px;">
                <!-- 线索漏斗概览 -->
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 30px;">
                    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; color: white; position: relative;">
                        <div style="font-size: 28px; font-weight: bold; margin-bottom: 8px;">${funnelData.current.totalLeads}</div>
                        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 5px;">总线索</div>
                        <div style="font-size: 12px; opacity: 0.8;">100%</div>
                        <div style="position: absolute; top: 10px; right: 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px;">
                            ${funnelData.growth.leadsGrowth >= 0 ? '+' : ''}${funnelData.growth.leadsGrowth}%
                        </div>
                    </div>
                    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 10px; color: white; position: relative;">
                        <div style="font-size: 28px; font-weight: bold; margin-bottom: 8px;">${funnelData.current.validLeads}</div>
                        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 5px;">有效线索</div>
                        <div style="font-size: 12px; opacity: 0.8;">${funnelData.current.validRate}%</div>
                        <div style="position: absolute; top: 10px; right: 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px;">
                            ${funnelData.growth.validGrowth >= 0 ? '+' : ''}${funnelData.growth.validGrowth}%
                        </div>
                    </div>
                    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 10px; color: white; position: relative;">
                        <div style="font-size: 28px; font-weight: bold; margin-bottom: 8px;">${funnelData.current.arrivedLeads}</div>
                        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 5px;">到店客户</div>
                        <div style="font-size: 12px; opacity: 0.8;">${funnelData.current.arrivalRate}%</div>
                        <div style="position: absolute; top: 10px; right: 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px;">
                            ${funnelData.growth.arrivalGrowth >= 0 ? '+' : ''}${funnelData.growth.arrivalGrowth}%
                        </div>
                    </div>
                    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 10px; color: white; position: relative;">
                        <div style="font-size: 28px; font-weight: bold; margin-bottom: 8px;">${funnelData.current.convertedLeads}</div>
                        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 5px;">成交客户</div>
                        <div style="font-size: 12px; opacity: 0.8;">${funnelData.current.conversionRate}%</div>
                        <div style="position: absolute; top: 10px; right: 10px; font-size: 10px; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px;">
                            ${funnelData.growth.conversionGrowth >= 0 ? '+' : ''}${funnelData.growth.conversionGrowth}%
                        </div>
                    </div>
                </div>

                <!-- 线索转化详细分析 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <!-- 转化率对比表格 -->
                    <div>
                        <h4 style="margin-bottom: 15px; color: #333; font-size: 14px;">
                            <i class="fas fa-chart-line"></i> 线索转化率对比分析
                        </h4>
                        <table style="width: 100%; border-collapse: collapse; font-size: 11px; font-family: Arial, sans-serif;">
                            <thead>
                                <tr style="background: #f1f3f4; color: #333;">
                                    <th style="padding: 10px 8px; border: 1px solid #ddd; text-align: left; font-weight: 600;">转化环节</th>
                                    <th style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; font-weight: 600;">本月</th>
                                    <th style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; font-weight: 600;">上月</th>
                                    <th style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; font-weight: 600;">环比</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background: white;">
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; color: #333; font-weight: 600;">线索→有效</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${funnelData.current.validRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #666;">${funnelData.last.validRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: ${(funnelData.current.validRate - funnelData.last.validRate) >= 0 ? '#28a745' : '#dc3545'}; font-weight: 600;">
                                        ${funnelData.current.validRate - funnelData.last.validRate >= 0 ? '+' : ''}${funnelData.current.validRate - funnelData.last.validRate}%
                                    </td>
                                </tr>
                                <tr style="background: #fafafa;">
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; color: #333; font-weight: 600;">有效→到店</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${funnelData.current.arrivalRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #666;">${funnelData.last.arrivalRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: ${(funnelData.current.arrivalRate - funnelData.last.arrivalRate) >= 0 ? '#28a745' : '#dc3545'}; font-weight: 600;">
                                        ${funnelData.current.arrivalRate - funnelData.last.arrivalRate >= 0 ? '+' : ''}${funnelData.current.arrivalRate - funnelData.last.arrivalRate}%
                                    </td>
                                </tr>
                                <tr style="background: white;">
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; color: #333; font-weight: 600;">到店→成交</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${funnelData.current.conversionRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #666;">${funnelData.last.conversionRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: ${(funnelData.current.conversionRate - funnelData.last.conversionRate) >= 0 ? '#28a745' : '#dc3545'}; font-weight: 600;">
                                        ${funnelData.current.conversionRate - funnelData.last.conversionRate >= 0 ? '+' : ''}${funnelData.current.conversionRate - funnelData.last.conversionRate}%
                                    </td>
                                </tr>
                                <tr style="background: #f8f9fa; border-top: 2px solid #ddd;">
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; color: #333; font-weight: 700;">整体转化率</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 700;">${funnelData.current.overallRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: #666;">${funnelData.last.overallRate}%</td>
                                    <td style="padding: 10px 8px; border: 1px solid #ddd; text-align: center; color: ${(funnelData.current.overallRate - funnelData.last.overallRate) >= 0 ? '#28a745' : '#dc3545'}; font-weight: 700;">
                                        ${funnelData.current.overallRate - funnelData.last.overallRate >= 0 ? '+' : ''}${funnelData.current.overallRate - funnelData.last.overallRate}%
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 线索质量分析 -->
                    <div>
                        <h4 style="margin-bottom: 15px; color: #333; font-size: 14px;">
                            <i class="fas fa-search"></i> 线索质量分析
                        </h4>
                        <div style="background: #f8f9fa; border-radius: 8px; padding: 15px;">
                            <div style="margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="font-size: 12px; color: #666;">无效线索</span>
                                    <span style="font-size: 12px; font-weight: 600; color: #dc3545;">
                                        ${funnelData.current.totalLeads - funnelData.current.validLeads}个 (${100 - funnelData.current.validRate}%)
                                    </span>
                                </div>
                                <div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
                                    <div style="background: #dc3545; height: 100%; width: ${100 - funnelData.current.validRate}%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="font-size: 12px; color: #666;">未到店线索</span>
                                    <span style="font-size: 12px; font-weight: 600; color: #ffc107;">
                                        ${funnelData.current.validLeads - funnelData.current.arrivedLeads}个 (${100 - funnelData.current.arrivalRate}%)
                                    </span>
                                </div>
                                <div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
                                    <div style="background: #ffc107; height: 100%; width: ${100 - funnelData.current.arrivalRate}%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="font-size: 12px; color: #666;">未成交线索</span>
                                    <span style="font-size: 12px; font-weight: 600; color: #fd7e14;">
                                        ${funnelData.current.arrivedLeads - funnelData.current.convertedLeads}个 (${100 - funnelData.current.conversionRate}%)
                                    </span>
                                </div>
                                <div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
                                    <div style="background: #fd7e14; height: 100%; width: ${100 - funnelData.current.conversionRate}%; transition: width 0.3s ease;"></div>
                                </div>
                            </div>

                            <div style="margin-top: 20px; padding: 10px; background: white; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <div style="font-size: 11px; color: #666; margin-bottom: 5px;">线索优化建议</div>
                                <div style="font-size: 10px; color: #333; line-height: 1.4;">
                                    ${funnelData.current.validRate < 60 ? '• 提升线索筛选和验证机制<br>' : ''}
                                    ${funnelData.current.arrivalRate < 30 ? '• 加强线索跟进和邀约技巧<br>' : ''}
                                    ${funnelData.current.conversionRate < 25 ? '• 优化到店接待和销售流程<br>' : ''}
                                    • 建立线索分级管理和跟进体系
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 12px; background: #f8f9fa; border-radius: 6px; font-size: 10px; color: #666; text-align: center;">
                    数据统计时间：本月 | 环比数据：上月同期 | 更新时间：${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;
    },

    // 获取线索渠道分析数据
    getLeadChannelAnalysis: function() {
        const { leadEntries, showroomEntries, orders } = this.cachedData;
        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();

        // 筛选本月线索数据
        const monthLeads = leadEntries.filter(entry => {
            const entryDate = new Date(entry.entryDate);
            return entryDate.getFullYear() === currentYear && entryDate.getMonth() === currentMonth;
        });

        // 按渠道统计
        const channelStats = new Map();

        monthLeads.forEach(lead => {
            const channel = lead.channel || lead.source || '未知渠道';

            if (!channelStats.has(channel)) {
                channelStats.set(channel, {
                    channel: channel,
                    totalLeads: 0,
                    validLeads: 0,
                    arrivedCount: 0,
                    orderCount: 0,
                    validRate: 0,
                    arrivalRate: 0,
                    conversionRate: 0,
                    overallRate: 0
                });
            }

            const stats = channelStats.get(channel);
            stats.totalLeads++;

            // 统计有效线索
            if (lead.intention && lead.intention !== '无意向' && lead.intention !== '' &&
                lead.phone && lead.phone.trim() !== '') {
                stats.validLeads++;
            }

            // 统计到店数量（线索客户在展厅录入中出现）
            const hasArrived = showroomEntries.some(entry =>
                entry.phone === lead.phone && entry.customerName === lead.customerName);
            if (hasArrived) {
                stats.arrivedCount++;
            }

            // 统计订单数量（线索客户在订单中出现）- 修复字段名称
            const hasOrder = orders.some(order =>
                order.phone1 === lead.phone || order.customerName === lead.customerName);
            if (hasOrder) {
                stats.orderCount++;
            }
        });

        // 计算各项转化率
        channelStats.forEach(stats => {
            stats.validRate = stats.totalLeads > 0 ?
                Math.round((stats.validLeads / stats.totalLeads) * 100) : 0;
            stats.arrivalRate = stats.validLeads > 0 ?
                Math.round((stats.arrivedCount / stats.validLeads) * 100) : 0;
            stats.conversionRate = stats.arrivedCount > 0 ?
                Math.round((stats.orderCount / stats.arrivedCount) * 100) : 0;
            stats.overallRate = stats.totalLeads > 0 ?
                Math.round((stats.orderCount / stats.totalLeads) * 100) : 0;
        });

        return Array.from(channelStats.values())
            .sort((a, b) => b.totalLeads - a.totalLeads);
    },

    // 渲染线索渠道分析
    renderLeadChannelAnalysis: function() {
        const container = document.getElementById('lead-channel-analysis');
        if (!container) return;

        const channelData = this.getLeadChannelAnalysis();

        if (channelData.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #666;">
                    <i class="fas fa-info-circle"></i> 暂无线索渠道数据
                </div>
            `;
            return;
        }

        const totalLeads = channelData.reduce((sum, channel) => sum + channel.totalLeads, 0);
        const totalOrders = channelData.reduce((sum, channel) => sum + channel.orderCount, 0);

        container.innerHTML = `
            <!-- 渠道概览 -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                <div style="text-align: center; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; color: white;">
                    <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">${totalLeads}</div>
                    <div style="font-size: 12px; opacity: 0.9;">总线索数</div>
                </div>
                <div style="text-align: center; padding: 15px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 8px; color: white;">
                    <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">${totalOrders}</div>
                    <div style="font-size: 12px; opacity: 0.9;">总成交数</div>
                </div>
            </div>

            <!-- 渠道详细表格 -->
            <table style="width: 100%; border-collapse: collapse; font-size: 10px; font-family: Arial, sans-serif;">
                <thead>
                    <tr style="background: #f1f3f4; color: #333;">
                        <th style="padding: 8px 4px; border: 1px solid #ddd; text-align: left; font-weight: 600;">渠道</th>
                        <th style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">总线索</th>
                        <th style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">有效线索</th>
                        <th style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">到店数</th>
                        <th style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">成交数</th>
                        <th style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">整体转化</th>
                        <th style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">占比</th>
                    </tr>
                </thead>
                <tbody>
                    ${channelData.map((channel, index) => {
                        const percentage = totalLeads > 0 ? Math.round((channel.totalLeads / totalLeads) * 100) : 0;

                        return `
                            <tr style="background: ${index % 2 === 0 ? 'white' : '#fafafa'};">
                                <td style="padding: 8px 4px; border: 1px solid #ddd; color: #333; font-weight: 600; max-width: 80px; overflow: hidden; text-overflow: ellipsis;">
                                    ${channel.channel}
                                </td>
                                <td style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: 600;">${channel.totalLeads}</td>
                                <td style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; color: #333;">
                                    ${channel.validLeads} (${channel.validRate}%)
                                </td>
                                <td style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; color: #333;">
                                    ${channel.arrivedCount} (${channel.arrivalRate}%)
                                </td>
                                <td style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; color: #333;">
                                    ${channel.orderCount} (${channel.conversionRate}%)
                                </td>
                                <td style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; color: ${channel.overallRate >= 10 ? '#28a745' : channel.overallRate >= 5 ? '#ffc107' : '#dc3545'}; font-weight: 600;">
                                    ${channel.overallRate}%
                                </td>
                                <td style="padding: 8px 4px; border: 1px solid #ddd; text-align: center; color: #333;">
                                    ${percentage}%
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>

            <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 10px; color: #666;">
                <div style="margin-bottom: 5px;"><strong>渠道效果评估：</strong></div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px;">
                    <div><span style="color: #28a745;">●</span> 优质渠道：转化率≥10%</div>
                    <div><span style="color: #ffc107;">●</span> 一般渠道：转化率5-9%</div>
                    <div><span style="color: #dc3545;">●</span> 待优化：转化率<5%</div>
                </div>
                <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #ddd; text-align: center;">
                    数据更新时间: ${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;
    },

    // 获取趋势分析数据
    getTrendAnalysisData: function() {
        const { showroomEntries, leadEntries, orders } = this.cachedData;
        const currentYear = new Date().getFullYear();

        const monthlyData = [];

        for (let month = 0; month < 12; month++) {
            const monthName = `${month + 1}月`;

            // 统计展厅客流
            const monthShowroom = showroomEntries.filter(entry => {
                const entryDate = new Date(entry.entryDate);
                return entryDate.getFullYear() === currentYear && entryDate.getMonth() === month;
            });

            // 统计线索数据
            const monthLeads = leadEntries.filter(entry => {
                const entryDate = new Date(entry.entryDate);
                return entryDate.getFullYear() === currentYear && entryDate.getMonth() === month;
            });

            // 统计订单数据
            const monthOrders = orders.filter(order => {
                const orderDate = new Date(order.orderDate);
                return orderDate.getFullYear() === currentYear && orderDate.getMonth() === month;
            });

            const validLeads = monthLeads.filter(lead =>
                lead.intention && lead.intention !== '无意向' && lead.intention !== '' &&
                lead.phone && lead.phone.trim() !== '').length;

            monthlyData.push({
                month: monthName,
                showroomTraffic: monthShowroom.length,
                totalLeads: monthLeads.length,
                validLeads: validLeads,
                orders: monthOrders.length
            });
        }

        return monthlyData;
    },

    // 渲染趋势分析
    renderTrendAnalysis: function() {
        const container = document.getElementById('trend-analysis');
        if (!container) return;

        const trendData = this.getTrendAnalysisData();
        const currentMonth = new Date().getMonth();

        container.innerHTML = `
            <div style="padding: 15px;">
                <!-- 趋势图表区域 -->
                <div style="height: 200px; background: #f8f9fa; border-radius: 8px; margin-bottom: 20px; display: flex; align-items: center; justify-content: center; position: relative;">
                    <canvas id="trend-chart" style="max-width: 100%; max-height: 100%;"></canvas>
                    <div style="position: absolute; top: 10px; left: 10px; font-size: 10px; color: #666;">
                        ${new Date().getFullYear()}年度趋势
                    </div>
                </div>

                <!-- 月度数据表格 -->
                <table style="width: 100%; border-collapse: collapse; font-size: 10px; font-family: Arial, sans-serif;">
                    <thead>
                        <tr style="background: #f1f3f4; color: #333;">
                            <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">月份</th>
                            <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">到店客流</th>
                            <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">总线索</th>
                            <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">有效线索</th>
                            <th style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">成交订单</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${trendData.map((data, index) => {
                            const isCurrentMonth = index === currentMonth;
                            const bgColor = isCurrentMonth ? '#e3f2fd' : (index % 2 === 0 ? 'white' : '#fafafa');
                            const fontWeight = isCurrentMonth ? '600' : 'normal';

                            return `
                                <tr style="background: ${bgColor};">
                                    <td style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: ${fontWeight};">
                                        ${data.month}${isCurrentMonth ? ' (本月)' : ''}
                                    </td>
                                    <td style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: ${fontWeight};">${data.showroomTraffic}</td>
                                    <td style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: ${fontWeight};">${data.totalLeads}</td>
                                    <td style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: ${fontWeight};">${data.validLeads}</td>
                                    <td style="padding: 6px 4px; border: 1px solid #ddd; text-align: center; color: #333; font-weight: ${fontWeight};">${data.orders}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>

                <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 10px; color: #666; text-align: center;">
                    趋势分析基于${new Date().getFullYear()}年度数据 | 更新时间：${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;

        // 简单的趋势图表渲染（使用Canvas）
        setTimeout(() => {
            this.renderTrendChart(trendData);
        }, 100);
    },

    // 渲染趋势图表
    renderTrendChart: function(data) {
        const canvas = document.getElementById('trend-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const width = canvas.width = canvas.offsetWidth;
        const height = canvas.height = canvas.offsetHeight;

        // 清空画布
        ctx.clearRect(0, 0, width, height);

        // 设置边距
        const margin = { top: 20, right: 20, bottom: 30, left: 40 };
        const chartWidth = width - margin.left - margin.right;
        const chartHeight = height - margin.top - margin.bottom;

        // 获取数据范围
        const maxShowroom = Math.max(...data.map(d => d.showroomTraffic));
        const maxLeads = Math.max(...data.map(d => d.totalLeads));
        const maxOrders = Math.max(...data.map(d => d.orders));
        const maxValue = Math.max(maxShowroom, maxLeads, maxOrders);

        // 绘制网格线
        ctx.strokeStyle = '#e0e0e0';
        ctx.lineWidth = 1;
        for (let i = 0; i <= 5; i++) {
            const y = margin.top + (chartHeight / 5) * i;
            ctx.beginPath();
            ctx.moveTo(margin.left, y);
            ctx.lineTo(margin.left + chartWidth, y);
            ctx.stroke();
        }

        // 绘制线条的函数
        const drawLine = (values, color) => {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();

            values.forEach((value, index) => {
                const x = margin.left + (chartWidth / (values.length - 1)) * index;
                const y = margin.top + chartHeight - (value / maxValue) * chartHeight;

                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });

            ctx.stroke();
        };

        // 绘制三条线
        drawLine(data.map(d => d.showroomTraffic), '#667eea');
        drawLine(data.map(d => d.totalLeads), '#f093fb');
        drawLine(data.map(d => d.orders), '#43e97b');

        // 绘制图例
        const legends = [
            { color: '#667eea', label: '到店客流' },
            { color: '#f093fb', label: '总线索' },
            { color: '#43e97b', label: '成交订单' }
        ];

        legends.forEach((legend, index) => {
            const x = margin.left + index * 80;
            const y = height - 10;

            ctx.fillStyle = legend.color;
            ctx.fillRect(x, y - 8, 12, 8);

            ctx.fillStyle = '#333';
            ctx.font = '10px Arial';
            ctx.fillText(legend.label, x + 16, y - 2);
        });
    },

    // 原有的渲染函数保持不变，但需要修改容器ID
    renderOldAnalytics: function() {
        const container = document.getElementById('analytics-content-old');
        if (!container) return;

        container.innerHTML = `
            <div class="analytics-dashboard">
                <!-- 时间筛选器 -->
                <div class="time-filter-section">
                    <div class="filter-controls">
                        <label>时间范围：</label>
                        <select id="time-range-select">
                            <option value="3">最近3个月</option>
                            <option value="6">最近6个月</option>
                            <option value="12" selected>最近12个月</option>
                        </select>
                        <button class="btn btn-primary" id="refresh-data-btn">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                        <button class="btn btn-success" id="export-data-btn">
                            <i class="fas fa-download"></i> 导出报表
                        </button>
                    </div>
                </div>

                <!-- 销售漏斗分析 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-filter"></i> 销售漏斗分析</h3>
                        <p>展示从线索到交付的完整转化流程</p>
                    </div>
                    <div class="chart-container">
                        <canvas id="sales-funnel-chart"></canvas>
                    </div>
                    <div class="funnel-stats" id="funnel-stats"></div>
                </div>

                <!-- 月度销售趋势 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-chart-line"></i> 月度销售趋势</h3>
                        <p>订单数量、交付数量和销售额变化趋势</p>
                    </div>
                    <div class="chart-container">
                        <canvas id="monthly-trend-chart"></canvas>
                    </div>
                </div>

                <!-- 客户来源分析 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-users"></i> 客户来源分析</h3>
                        <p>不同渠道的线索质量和转化效果</p>
                    </div>
                    <div class="chart-container">
                        <canvas id="customer-source-chart"></canvas>
                    </div>
                </div>

                <!-- 销售顾问业绩排行 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-trophy"></i> 销售顾问业绩排行</h3>
                        <p>个人订单数、交付数、销售额等指标</p>
                    </div>
                    <div class="chart-container">
                        <canvas id="advisor-performance-chart"></canvas>
                    </div>
                </div>

                <!-- 车型销售分析 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-car"></i> 车型销售分析</h3>
                        <p>热销车型、库存周转率、配车效率</p>
                    </div>
                    <div class="chart-container">
                        <canvas id="vehicle-model-chart"></canvas>
                    </div>
                </div>

                <!-- 目标完成情况 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3><i class="fas fa-bullseye"></i> 目标完成情况</h3>
                        <p>提车目标、订单目标、零售目标的达成率对比</p>
                    </div>
                    <div class="target-dashboard" id="target-dashboard"></div>
                </div>
            </div>
        `;

        // 渲染所有图表
        this.renderAllCharts();
    },

    // 渲染所有图表
    renderAllCharts: function() {
        this.renderSalesFunnelChart();
        this.renderMonthlyTrendChart();
        this.renderCustomerSourceChart();
        this.renderAdvisorPerformanceChart();
        this.renderVehicleModelChart();
        this.renderTargetDashboard();
    },

    // 渲染销售漏斗图表
    renderSalesFunnelChart: function() {
        const funnelData = this.getSalesFunnelData();
        const ctx = document.getElementById('sales-funnel-chart');
        if (!ctx) return;

        // 销售漏斗数据
        const stages = ['线索', '展厅来访', '试驾', '订单', '交付'];
        const values = [
            funnelData.leads,
            funnelData.showroomVisits,
            funnelData.testDrives,
            funnelData.orders,
            funnelData.deliveries
        ];

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: stages,
                datasets: [{
                    label: '数量',
                    data: values,
                    backgroundColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0'
                    ],
                    borderColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#7209b7',
                        '#f72585',
                        '#4cc9f0'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '销售漏斗转化分析'
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量'
                        }
                    }
                }
            }
        });

        // 渲染转化率统计
        this.renderFunnelStats(funnelData);
    },

    // 渲染漏斗统计信息
    renderFunnelStats: function(funnelData) {
        const statsContainer = document.getElementById('funnel-stats');
        if (!statsContainer) return;

        statsContainer.innerHTML = `
            <div class="funnel-stats-grid">
                <div class="stat-item">
                    <div class="stat-label">线索→展厅转化率</div>
                    <div class="stat-value">${funnelData.conversion.leadsToShowroom}%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">展厅→试驾转化率</div>
                    <div class="stat-value">${funnelData.conversion.showroomToTestDrive}%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">试驾→订单转化率</div>
                    <div class="stat-value">${funnelData.conversion.testDriveToOrder}%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">订单→交付转化率</div>
                    <div class="stat-value">${funnelData.conversion.orderToDelivery}%</div>
                </div>
            </div>
        `;
    },

    // 绑定事件
    bindEvents: function() {
        // 时间范围选择事件
        const timeRangeSelect = document.getElementById('time-range-select');
        if (timeRangeSelect) {
            timeRangeSelect.addEventListener('change', () => {
                this.renderAllCharts();
            });
        }

        // 刷新数据事件
        const refreshBtn = document.getElementById('refresh-data-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                await this.loadAllData();
                this.renderAllCharts();
                alert('数据已刷新');
            });
        }

        // 导出数据事件
        const exportBtn = document.getElementById('export-data-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportAnalyticsData();
            });
        }
    },

    // 渲染月度销售趋势图表
    renderMonthlyTrendChart: function() {
        const timeRange = document.getElementById('time-range-select')?.value || 12;
        const trendData = this.getMonthlySalesTrend(parseInt(timeRange));
        const ctx = document.getElementById('monthly-trend-chart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.map(item => item.monthName),
                datasets: [
                    {
                        label: '订单数量',
                        data: trendData.map(item => item.orders),
                        borderColor: '#4361ee',
                        backgroundColor: 'rgba(67, 97, 238, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: '交付数量',
                        data: trendData.map(item => item.deliveries),
                        borderColor: '#f72585',
                        backgroundColor: 'rgba(247, 37, 133, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '月度销售趋势'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量'
                        }
                    }
                }
            }
        });
    },

    // 渲染客户来源分析图表
    renderCustomerSourceChart: function() {
        const sourceData = this.getCustomerSourceAnalysis();
        const ctx = document.getElementById('customer-source-chart');
        if (!ctx) return;

        const sources = Object.keys(sourceData);
        const leadsData = sources.map(source => sourceData[source].leads);
        const showroomData = sources.map(source => sourceData[source].showroom);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: sources,
                datasets: [
                    {
                        label: '线索数量',
                        data: leadsData,
                        backgroundColor: '#4361ee'
                    },
                    {
                        label: '展厅来访',
                        data: showroomData,
                        backgroundColor: '#f72585'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '客户来源分析'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量'
                        }
                    }
                }
            }
        });
    },

    // 渲染销售顾问业绩图表
    renderAdvisorPerformanceChart: function() {
        const performanceData = this.getSalesAdvisorPerformance();
        const ctx = document.getElementById('advisor-performance-chart');
        if (!ctx) return;

        const advisors = Object.keys(performanceData).slice(0, 10); // 只显示前10名
        const ordersData = advisors.map(advisor => performanceData[advisor].orders);
        const deliveriesData = advisors.map(advisor => performanceData[advisor].deliveries);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: advisors,
                datasets: [
                    {
                        label: '订单数量',
                        data: ordersData,
                        backgroundColor: '#4361ee'
                    },
                    {
                        label: '交付数量',
                        data: deliveriesData,
                        backgroundColor: '#f72585'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '销售顾问业绩排行（前10名）'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数量'
                        }
                    }
                }
            }
        });
    },

    // 渲染车型销售分析图表
    renderVehicleModelChart: function() {
        const modelData = this.getVehicleModelAnalysis();
        const ctx = document.getElementById('vehicle-model-chart');
        if (!ctx) return;

        const models = Object.keys(modelData).slice(0, 8); // 只显示前8个车型
        const ordersData = models.map(model => modelData[model].orders);
        const deliveriesData = models.map(model => modelData[model].deliveries);

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: models,
                datasets: [{
                    label: '订单数量',
                    data: ordersData,
                    backgroundColor: [
                        '#4361ee', '#f72585', '#4cc9f0', '#7209b7',
                        '#3f37c9', '#f77f00', '#fcbf49', '#06ffa5'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '车型销售分布'
                    },
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    },

    // 渲染目标完成情况仪表盘
    renderTargetDashboard: function() {
        const targetData = this.getTargetCompletionData();
        const container = document.getElementById('target-dashboard');
        if (!container) return;

        container.innerHTML = `
            <div class="target-cards">
                <div class="target-card">
                    <div class="target-header">
                        <h4><i class="fas fa-truck"></i> 提车目标</h4>
                    </div>
                    <div class="target-progress">
                        <div class="progress-circle" data-percentage="${targetData.delivery.completion}">
                            <div class="progress-text">
                                <span class="percentage">${targetData.delivery.completion}%</span>
                                <span class="label">完成率</span>
                            </div>
                        </div>
                        <div class="target-stats">
                            <div class="stat">
                                <span class="label">目标:</span>
                                <span class="value">${targetData.delivery.target}</span>
                            </div>
                            <div class="stat">
                                <span class="label">实际:</span>
                                <span class="value">${targetData.delivery.actual}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="target-card">
                    <div class="target-header">
                        <h4><i class="fas fa-shopping-cart"></i> 订单目标</h4>
                    </div>
                    <div class="target-progress">
                        <div class="progress-circle" data-percentage="${targetData.order.completion}">
                            <div class="progress-text">
                                <span class="percentage">${targetData.order.completion}%</span>
                                <span class="label">完成率</span>
                            </div>
                        </div>
                        <div class="target-stats">
                            <div class="stat">
                                <span class="label">目标:</span>
                                <span class="value">${targetData.order.target}</span>
                            </div>
                            <div class="stat">
                                <span class="label">实际:</span>
                                <span class="value">${targetData.order.actual}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="target-card">
                    <div class="target-header">
                        <h4><i class="fas fa-chart-line"></i> 零售目标</h4>
                    </div>
                    <div class="target-progress">
                        <div class="progress-circle" data-percentage="${targetData.retail.completion}">
                            <div class="progress-text">
                                <span class="percentage">${targetData.retail.completion}%</span>
                                <span class="label">完成率</span>
                            </div>
                        </div>
                        <div class="target-stats">
                            <div class="stat">
                                <span class="label">目标:</span>
                                <span class="value">¥${(targetData.retail.target / 10000).toFixed(1)}万</span>
                            </div>
                            <div class="stat">
                                <span class="label">实际:</span>
                                <span class="value">¥${(targetData.retail.actual / 10000).toFixed(1)}万</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 初始化进度圆环
        this.initProgressCircles();
    },

    // 初始化进度圆环
    initProgressCircles: function() {
        const circles = document.querySelectorAll('.progress-circle');
        circles.forEach(circle => {
            const percentage = parseFloat(circle.dataset.percentage);
            const color = percentage >= 100 ? '#28a745' : percentage >= 80 ? '#ffc107' : '#dc3545';

            circle.style.background = `conic-gradient(${color} ${percentage * 3.6}deg, #e9ecef 0deg)`;
        });
    },

    // 导出分析数据
    exportAnalyticsData: function() {
        // 这个方法将在后续实现
        console.log('导出分析数据');
        alert('导出功能开发中...');
    }
};
