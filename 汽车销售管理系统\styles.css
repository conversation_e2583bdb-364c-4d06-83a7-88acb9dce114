/* styles.css - 统一后的样式 */
:root {
    --primary: #4361ee;
    --secondary: #3f37c9;
    --success: #4cc9f0;
    --info: #4895ef;
    --warning: #f72585;
    --danger: #e63946;
    --light: #f8f9fa;
    --dark: #212529;
    --gray: #6c757d;
    --light-gray: #e9ecef;
    --border-radius: 10px;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

body {
    background-color: #f5f7fb;
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 应用容器 - 用于用户管理页面 */
.app-container {
    display: flex;
    min-height: 100vh;
    background-color: #f5f7fb;
}

header {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 28px;
}

.logo h1 {
    font-size: 22px;
    font-weight: 600;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 42px;
    height: 42px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
}

.main-content {
    display: flex;
    flex: 1;
}

.sidebar {
    width: 260px;
    background: white;
    padding: 25px 20px;
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow);
    z-index: 10;
}

.nav-menu {
    list-style: none;
    margin-top: 20px;
}

.nav-menu li {
    margin-bottom: 8px;
}

.nav-menu a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    text-decoration: none;
    color: var(--dark);
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 15px;
    font-weight: 500;
}

.nav-menu a i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
    color: var(--gray);
}

.nav-menu a:hover, .nav-menu a.active {
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary);
}

.nav-menu a.active i {
    color: var(--primary);
}

.nav-menu a:hover i {
    color: var(--primary);
}

.content-area {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.module-container {
    display: none;
}

.module-container.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.module-header {
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.module-header h1 {
    font-size: 26px;
    font-weight: 700;
    color: var(--dark);
    display: flex;
    align-items: center;
    gap: 12px;
}

.module-header h1 i {
    color: var(--primary);
}

.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 25px;
}

.card-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--light-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h2 i {
    color: var(--primary);
}

.card-body {
    padding: 25px;
}

.funnel-overview {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.funnel-step {
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    background: rgba(67, 97, 238, 0.05);
    transition: transform 0.3s ease;
}

.funnel-step:hover {
    transform: translateY(-5px);
    background: rgba(67, 97, 238, 0.1);
}

.funnel-step h3 {
    font-size: 14px;
    color: var(--gray);
    margin-bottom: 10px;
}

.funnel-step .number {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 5px;
}

.funnel-step .conversion {
    font-size: 14px;
    background: rgba(76, 201, 240, 0.2);
    color: var(--info);
    padding: 4px 8px;
    border-radius: 20px;
    display: inline-block;
}

.chart-container {
    padding: 15px;
    height: 300px;
    position: relative;
    overflow: hidden;
}

/* 销售分析模块样式优化 */
.analytics-module {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.analytics-module .module-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.analytics-module .module-content {
    padding: 20px;
    min-height: 200px;
}

/* 漏斗图表样式 */
.funnel-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px;
}

.funnel-stage {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    position: relative;
    margin: 0 auto;
    min-width: 200px;
}

.funnel-stage:nth-child(1) { width: 100%; }
.funnel-stage:nth-child(2) { width: 85%; }
.funnel-stage:nth-child(3) { width: 70%; }
.funnel-stage:nth-child(4) { width: 55%; }
.funnel-stage:nth-child(5) { width: 40%; }

.funnel-stats {
    text-align: center;
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .analytics-row {
        grid-template-columns: 1fr !important;
    }

    .chart-container {
        height: 250px;
        padding: 10px;
    }

    .analytics-module .module-content {
        padding: 15px;
        min-height: 150px;
    }

    .funnel-stage {
        min-width: 150px;
        padding: 10px;
    }
}

.data-management {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.data-card {
    padding: 20px;
    border-radius: 8px;
    background: rgba(67, 97, 238, 0.05);
    display: flex;
    flex-direction: column;
}

.data-card h3 {
    font-size: 15px;
    color: var(--gray);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.data-card h3 i {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary);
    border-radius: 6px;
}

.data-card .number {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 10px;
}

.data-card .info {
    font-size: 14px;
    color: var(--gray);
    margin-top: auto;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.data-card .info i {
    color: var(--success);
}

.progress-bar {
    height: 8px;
    background: var(--light-gray);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 5px;
}

.progress {
    height: 100%;
    background: var(--success);
    border-radius: 4px;
}

.notification {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 18px 22px;
    display: flex;
    align-items: center;
    gap: 15px;
    transform: translateX(150%);
    transition: transform 0.4s ease;
    z-index: 1000;
    border-left: 4px solid var(--primary);
}

.notification.show {
    transform: translateX(0);
}

.notification i {
    font-size: 24px;
    color: var(--primary);
}

.btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--secondary);
}

.btn-secondary {
    background: white;
    color: var(--primary);
    border: 1px solid var(--primary);
}

.btn-secondary:hover {
    background: rgba(67, 97, 238, 0.1);
}

.form-select {
    padding: 8px 12px;
    border: 1px solid var(--light-gray);
    border-radius: 6px;
    background: white;
    font-size: 14px;
}

/* 首页特定样式 */
.nav-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    text-decoration: none;
    color: var(--dark);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    text-align: center;
}

.nav-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.nav-card-icon {
    font-size: 48px;
    color: var(--primary);
    margin-bottom: 15px;
}

.nav-card h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: var(--primary);
}

.nav-card p {
    font-size: 14px;
    color: var(--gray);
    line-height: 1.4;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

/* 设置页特定样式 */
.settings-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.settings-tab {
    padding: 10px 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #f8f9fa;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.settings-tab:hover {
    background: #e9ecef;
}

.settings-tab.active {
    background: var(--secondary);
    color: white;
    border-color: var(--secondary);
}

.settings-content {
    min-height: 400px;
    padding: 20px 0;
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--secondary);
    color: var(--secondary);
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.btn-outline:hover {
    background: var(--secondary);
    color: white;
}

/* 配置表格样式 */
.config-table-container {
    overflow-x: auto;
    margin-top: 20px;
}

.config-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.config-table th,
.config-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.config-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: var(--primary);
    font-size: 14px;
}

.config-table td {
    font-size: 14px;
    color: var(--dark);
}

.config-table tbody tr:hover {
    background: #f8f9fa;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark);
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.action-buttons .btn {
    padding: 6px 12px;
    font-size: 12px;
    min-width: auto;
}

/* 状态徽章样式 */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

/* 危险按钮样式 */
.btn-danger {
    background: var(--danger);
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* 导入进度样式 */
.import-progress-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.import-progress-container {
    background: white;
    padding: 30px;
    border-radius: 12px;
    width: 450px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.import-progress-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--dark);
}

.import-progress-bar {
    height: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 15px;
}

.import-progress-fill {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, var(--primary), #5a67d8);
    transition: width 0.3s ease;
    border-radius: 6px;
}

.import-progress-text {
    font-size: 14px;
    color: var(--gray);
    margin-top: 10px;
}

/* 到店录入模块样式 */
.large-modal .modal-content {
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
}

.form-grid {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.form-section h4 {
    margin: 0 0 15px 0;
    color: var(--primary);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-section h4 i {
    font-size: 14px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--dark);
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
}

.form-group input[readonly] {
    background: #f8f9fa;
    color: var(--gray);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* 详情页面样式 */
.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: var(--gray);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item span {
    color: var(--dark);
    font-size: 14px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

/* 搜索框样式 */
.search-box {
    position: relative;
    width: 300px;
}

.search-box input {
    width: 100%;
    padding: 10px 40px 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.search-box i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray);
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 20px;
}

.pagination .btn {
    min-width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .large-modal .modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }

    .search-box {
        width: 100%;
        margin-top: 10px;
    }

    .action-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}

/* 客户管理特定样式 */
.filter-btn {
    background: transparent;
    border: 1px solid var(--secondary);
    color: var(--secondary);
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.3s ease;
    text-align: center;
    margin-bottom: 5px;
}

.filter-btn:hover {
    background: var(--secondary);
    color: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .funnel-overview {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    .sidebar {
        width: 100%;
        padding: 15px;
    }
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    .funnel-overview {
        grid-template-columns: 1fr;
    }
    .data-management {
        grid-template-columns: 1fr;
    }
    .module-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
}

/* 统一数据表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    margin-bottom: 0;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
    font-size: 14px;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table td {
    color: #212529;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 表格容器 - 响应式设计 */
.table-container {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* 确保表格在容器内正确显示 */
.table-container .data-table {
    margin-bottom: 0;
    min-width: 1000px; /* 确保小屏幕上有水平滚动 */
}

/* 紧凑型表格样式 */
.data-table.compact {
    font-size: 12px;
    min-width: 600px;
}

.data-table.compact th,
.data-table.compact td {
    padding: 6px 4px;
    font-size: 11px;
}

.data-table.compact .btn-sm {
    padding: 2px 4px;
    font-size: 10px;
    margin-right: 2px;
}

/* 库存管理表格样式 */
.inventory-table {
    font-size: 12px;
    min-width: 1200px;
}

.inventory-table th,
.inventory-table td {
    padding: 8px 6px;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.inventory-table .btn-sm {
    padding: 2px 6px;
    font-size: 10px;
    margin-right: 2px;
}

/* 状态标签统一样式 */
.status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    text-align: center;
    white-space: nowrap;
}

.status-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-secondary {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* 库存状态样式 */
.inventory-可售 {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.inventory-预售 {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.inventory-整备 {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.inventory-已配对 {
    background-color: #cce5ff;
    color: #004085;
    border: 1px solid #b3d7ff;
}

.inventory-已交车 {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 库存车龄样式 */
.stock-age-good {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.stock-age-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.stock-age-caution {
    background-color: #ffeaa7;
    color: #856404;
    border: 1px solid #fdd835;
}

.stock-age-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 订单详情样式 */
.order-details .detail-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.order-details .detail-group label {
    font-weight: 500;
    color: var(--text-color);
    min-width: 80px;
    margin-right: 10px;
}

.order-details .detail-group span {
    color: var(--text-secondary);
}

.order-details .detail-actions {
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
}

/* 审核状态样式 */
.audit-待审核 {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.audit-已审核 {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.audit-驳回 {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 交付状态样式 */
.delivery-待交付 {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.delivery-已配车 {
    background-color: #cce5ff;
    color: #004085;
    border: 1px solid #b3d7ff;
}

.delivery-已交付 {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* 按钮样式统一 */
.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    margin-right: 4px;
    white-space: nowrap;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
    border: 1px solid #ffc107;
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .table-container .data-table {
        min-width: 800px;
    }

    .data-table th,
    .data-table td {
        padding: 8px;
        font-size: 12px;
    }

    .btn-sm {
        padding: 2px 4px;
        font-size: 10px;
    }
}

/* 目标管理专用样式 */
.targets-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.target-section {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 15px;
}

.target-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--dark);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.target-section h3 i {
    color: var(--primary);
    font-size: 14px;
}

.target-actions {
    margin-bottom: 15px;
}

.targets-table {
    width: 100%;
    table-layout: fixed;
    line-height: 1.2;
    font-size: 12px;
}

.targets-table td,
.targets-table th {
    padding: 6px 4px;
    text-align: center;
    font-size: 12px;
    border: 1px solid #e9ecef;
}

.targets-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    font-size: 11px;
}

.targets-table td {
    color: #212529;
}

.targets-table .completion-rate {
    font-weight: 600;
    font-size: 11px;
}

.targets-table .completion-rate.high {
    color: #28a745;
}

.targets-table .completion-rate.medium {
    color: #ffc107;
}

.targets-table .completion-rate.low {
    color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .targets-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .target-section {
        padding: 12px;
    }

    .targets-table {
        font-size: 11px;
    }

    .targets-table td,
    .targets-table th {
        padding: 4px 2px;
        font-size: 10px;
    }
}

/* 用户管理页面特定样式 */
.app-container .sidebar {
    width: 260px;
    background: white;
    padding: 25px 20px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    z-index: 10;
    height: 100vh;
    overflow-y: auto;
}

.app-container .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-header {
    margin-bottom: 30px;
    text-align: center;
}

.sidebar-header h2 {
    color: var(--primary);
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.sidebar-content {
    flex: 1;
}

.content-header {
    background: white;
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.content-header h1 {
    margin: 0;
    color: var(--dark);
}

/* 响应式设计 - 用户管理页面 */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .app-container .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .app-container .main-content {
        margin-left: 0;
    }
}

/* 配件库管理页面特定样式 */
.tab-navigation {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 30px;
}

.tab-btn {
    padding: 12px 24px;
    border: none;
    background: none;
    font-size: 16px;
    font-weight: 500;
    color: #6c757d;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-btn:hover {
    color: var(--primary);
    background: rgba(67, 97, 238, 0.05);
}

.tab-btn.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
}

.tab-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--light-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.tab-header h2 {
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.tab-header h2 i {
    color: var(--primary);
}

.tab-content {
    min-height: 500px;
}

/* 配件库表格样式 */
.table-responsive {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    margin: 0;
    min-width: 1000px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
    font-size: 14px;
    vertical-align: middle;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table td {
    color: #212529;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    color: #dee2e6;
    margin-bottom: 16px;
    display: block;
}

.empty-state h3 {
    color: #6c757d;
    margin-bottom: 8px;
    font-size: 18px;
}

.empty-state p {
    color: #adb5bd;
    font-size: 14px;
    margin: 0;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--dark);
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f8f9fa;
    color: var(--dark);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 表单网格样式 */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

/* 文本颜色工具类 */
.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* 筛选区域样式 */
.filter-section {
    padding: 20px 25px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.filter-row {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.filter-group input,
.filter-group select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background: white;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.pagination-info {
    font-size: 14px;
    color: #6c757d;
}

.pagination-controls {
    display: flex;
    gap: 5px;
    align-items: center;
}

.pagination-controls .btn {
    min-width: 40px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
}

/* 操作按钮样式 */
.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.action-column {
    width: 120px;
    text-align: center;
}

.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
    align-items: center;
}

.action-buttons .btn {
    min-width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 列设置样式 */
.column-settings {
    max-height: 400px;
    overflow-y: auto;
}

.column-setting-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.column-setting-item:last-child {
    border-bottom: none;
}

.column-setting-item label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 14px;
}

.column-setting-item input[type="checkbox"] {
    margin: 0;
}

/* 导入模板样式 */
.import-section {
    padding: 10px 0;
}

.import-template {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.import-template h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: var(--dark);
}

.import-template p {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #6c757d;
}

.import-template ul {
    margin: 10px 0;
    padding-left: 20px;
    font-size: 13px;
    color: #495057;
}

.import-template li {
    margin-bottom: 3px;
}

/* 响应式设计 - 配件库管理 */
@media (max-width: 768px) {
    .tab-navigation {
        flex-wrap: wrap;
        gap: 5px;
    }

    .tab-btn {
        padding: 8px 16px;
        font-size: 14px;
    }

    .table {
        min-width: 800px;
    }

    .table th,
    .table td {
        padding: 8px;
        font-size: 12px;
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .filter-row {
        flex-direction: column;
        gap: 15px;
    }

    .filter-group {
        min-width: 100%;
    }

    .header-actions {
        flex-wrap: wrap;
        gap: 8px;
    }

    .pagination-container {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }
}