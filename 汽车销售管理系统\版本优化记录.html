<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML系统修改记录工具</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #4361ee;
            --secondary: #3f37c9;
            --success: #4cc9f0;
            --warning: #ff9e00;
            --danger: #f72585;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --border: #dee2e6;
            --shadow: rgba(0, 0, 0, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f0f4f8 0%, #e2eaf3 100%);
            min-height: 100vh;
            padding: 20px;
            color: var(--dark);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 6px 18px rgba(67, 97, 238, 0.15);
            border-left: 5px solid var(--primary);
        }
        
        h1 {
            color: var(--primary);
            margin-bottom: 10px;
            font-size: 2.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            color: var(--gray);
            font-size: 1.25rem;
            max-width: 900px;
            margin: 15px auto 0;
            line-height: 1.6;
        }
        
        .app-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        @media (max-width: 1100px) {
            .app-container {
                grid-template-columns: 1fr;
            }
        }
        
        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 6px 18px var(--shadow);
            padding: 30px;
            height: fit-content;
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-title {
            font-size: 1.5rem;
            margin-bottom: 25px;
            color: var(--secondary);
            display: flex;
            align-items: center;
            gap: 12px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--light-gray);
        }
        
        .card-title i {
            font-size: 1.8rem;
            color: var(--primary);
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: var(--dark);
            font-size: 1.1rem;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 14px 18px;
            border: 2px solid var(--border);
            border-radius: 10px;
            font-size: 1.05rem;
            transition: all 0.3s ease;
            background: var(--light);
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(67, 97, 238, 0.2);
            background: white;
        }
        
        textarea {
            resize: vertical;
            font-family: 'Consolas', 'Courier New', monospace;
            line-height: 1.6;
        }
        
        .modification-textarea {
            min-height: 100px;
        }
        
        .content-textarea {
            min-height: 300px;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 14px 28px;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .btn:hover {
            background: var(--secondary);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }
        
        .btn:active {
            transform: translateY(1px);
        }
        
        .btn-block {
            display: block;
            width: 100%;
        }
        
        .btn-export {
            background: #20c997;
        }
        
        .btn-export:hover {
            background: #199d76;
        }
        
        .btn-clear {
            background: var(--danger);
        }
        
        .btn-clear:hover {
            background: #d1145a;
        }
        
        .search-box {
            display: flex;
            gap: 12px;
            margin-bottom: 25px;
        }
        
        .search-box input {
            flex: 1;
        }
        
        .records-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .records-table th {
            background: var(--primary);
            color: white;
            text-align: left;
            padding: 16px;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .records-table td {
            padding: 16px;
            border-bottom: 1px solid var(--border);
            vertical-align: top;
        }
        
        .records-table tr:nth-child(even) {
            background-color: rgba(67, 97, 238, 0.03);
        }
        
        .records-table tr:hover {
            background-color: rgba(67, 97, 238, 0.08);
            cursor: pointer;
        }
        
        .timestamp {
            color: var(--gray);
            font-size: 0.95rem;
            white-space: nowrap;
        }
        
        .modification {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--dark);
            font-size: 1.1rem;
        }
        
        .modified-content {
            color: var(--gray);
            font-size: 0.95rem;
            line-height: 1.5;
            font-family: 'Consolas', 'Courier New', monospace;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: var(--gray);
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #d0d7e0;
            opacity: 0.7;
        }
        
        .empty-state h3 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            color: var(--primary);
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            background: linear-gradient(to right, #f0f7ff, #e6f0ff);
            padding: 20px;
            border-radius: 10px;
            margin-top: 25px;
            border: 1px solid #d6e4ff;
        }
        
        .stat-item {
            text-align: center;
            padding: 0 15px;
        }
        
        .stat-value {
            font-size: 2.2rem;
            font-weight: 800;
            color: var(--primary);
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 1rem;
            color: var(--gray);
            font-weight: 500;
        }
        
        footer {
            text-align: center;
            margin-top: 50px;
            color: var(--gray);
            padding: 25px;
            font-size: 1rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 25px;
        }
        
        .tag {
            display: inline-block;
            padding: 6px 14px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            background: var(--light);
            color: var(--gray);
            margin-right: 5px;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-content {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 900px;
            max-height: 90vh;
            overflow: hidden;
            transform: translateY(30px);
            transition: transform 0.4s ease;
        }
        
        .modal.active .modal-content {
            transform: translateY(0);
        }
        
        .modal-header {
            padding: 20px 25px;
            background: var(--primary);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 1.6rem;
            font-weight: 600;
        }
        
        .close-modal {
            background: none;
            border: none;
            color: white;
            font-size: 1.8rem;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }
        
        .close-modal:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .modal-body {
            padding: 25px;
            overflow-y: auto;
            max-height: 70vh;
        }
        
        .detail-item {
            margin-bottom: 25px;
        }
        
        .detail-label {
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 1.1rem;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .detail-value {
            background: var(--light);
            padding: 18px;
            border-radius: 10px;
            font-size: 1.05rem;
            line-height: 1.6;
        }
        
        .code-content {
            background: #2d2d2d;
            color: #f8f8f2;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .modal-footer {
            padding: 20px 25px;
            background: var(--light-gray);
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }
        
        .record-counter {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 20px;
            padding: 15px;
            background: var(--light);
            border-radius: 10px;
            font-size: 1.1rem;
        }
        
        .counter-value {
            font-weight: 700;
            color: var(--primary);
        }
        
        .full-width {
            grid-column: 1 / -1;
        }
        
        .instructions {
            background: #e8f4ff;
            border-left: 4px solid var(--primary);
            padding: 20px;
            border-radius: 0 10px 10px 0;
            margin-bottom: 25px;
        }
        
        .instructions h3 {
            margin-bottom: 10px;
            color: var(--primary);
        }
        
        .instructions ul {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
        
        /* 移动设备优化 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            header {
                padding: 15px;
            }
            
            h1 {
                font-size: 2rem;
                flex-direction: column;
                gap: 10px;
            }
            
            .subtitle {
                font-size: 1rem;
            }
            
            .card {
                padding: 20px;
            }
            
            .card-title {
                font-size: 1.3rem;
            }
            
            .app-container {
                gap: 20px;
            }
            
            .stats {
                flex-direction: column;
                gap: 15px;
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
            
            .search-box {
                flex-direction: column;
            }
            
            .records-table th, .records-table td {
                padding: 12px 8px;
            }
            
            .timestamp {
                font-size: 0.8rem;
            }
            
            .modification {
                font-size: 1rem;
            }
            
            .modified-content {
                font-size: 0.85rem;
            }
        }
        
        @media (max-width: 480px) {
            .records-table th:nth-child(1),
            .records-table td:nth-child(1) {
                display: none;
            }
            
            .records-table th:nth-child(3),
            .records-table td:nth-child(3) {
                display: none;
            }
            
            .action-icon {
                font-size: 1.2rem;
            }
        }
        
        .action-icon {
            color: var(--primary);
            cursor: pointer;
            font-size: 1.4rem;
            transition: all 0.2s ease;
        }
        
        .action-icon:hover {
            color: var(--secondary);
            transform: scale(1.1);
        }
        
        .record-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid var(--border);
        }
        
        .record-item:hover {
            background-color: rgba(67, 97, 238, 0.08);
        }
        
        .record-main {
            flex: 1;
            overflow: hidden;
        }
        
        .record-actions {
            display: flex;
            gap: 15px;
            padding-left: 15px;
        }
        
        .record-date {
            font-size: 0.9rem;
            color: var(--gray);
            margin-bottom: 5px;
        }
        
        .record-category {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-top: 5px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-code-branch"></i> HTML系统修改记录工具</h1>
            <p class="subtitle">专为HTML系统开发设计的修改记录工具，支持大文本输入，直观查看历史修改，一键导出备份</p>
        </header>
        
        <div class="app-container">
            <div class="input-section">
                <div class="card">
                    <h2 class="card-title"><i class="fas fa-edit"></i> 添加修改记录</h2>
                    
                    <div class="instructions">
                        <h3>使用说明</h3>
                        <ul>
                            <li>填写修改描述和具体修改内容</li>
                            <li>内容区域可自由调整大小</li>
                            <li>支持大文本输入（最多10,000字符）</li>
                            <li>使用类别标签进行分类管理</li>
                        </ul>
                    </div>
                    
                    <div class="form-group">
                        <label for="modification"><i class="fas fa-heading"></i> 修改内容描述</label>
                        <textarea id="modification" class="modification-textarea" rows="2" placeholder="例如：重构导航栏响应式设计..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="modifiedContent"><i class="fas fa-file-code"></i> 修改后的内容</label>
                        <textarea id="modifiedContent" class="content-textarea" placeholder="在此输入修改后的HTML/CSS/JS代码或内容描述..."></textarea>
                        <div class="record-counter">
                            <span>字符数: <span id="charCount">0</span></span>
                            <span>最大: <span class="counter-value">10,000</span> 字符</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="category"><i class="fas fa-tag"></i> 修改类别</label>
                        <select id="category">
                            <option value="design">设计/样式修改</option>
                            <option value="function">功能修改</option>
                            <option value="content">内容更新</option>
                            <option value="bug">Bug修复</option>
                            <option value="optimization">性能优化</option>
                            <option value="structure">结构重构</option>
                        </select>
                    </div>
                    
                    <button id="addBtn" class="btn btn-block"><i class="fas fa-plus-circle"></i> 添加修改记录</button>
                    
                    <div class="action-buttons">
                        <button id="exportBtn" class="btn btn-export"><i class="fas fa-file-export"></i> 导出所有记录</button>
                        <button id="clearBtn" class="btn btn-clear"><i class="fas fa-trash-alt"></i> 清除所有记录</button>
                    </div>
                </div>
                
                <div class="card stats-card">
                    <h2 class="card-title"><i class="fas fa-chart-pie"></i> 修改记录统计</h2>
                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-value" id="totalRecords">0</div>
                            <div class="stat-label">总记录数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="todayRecords">0</div>
                            <div class="stat-label">今日记录</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="charsSaved">0</div>
                            <div class="stat-label">字符保存</div>
                        </div>
                    </div>
                    
                    <div class="form-group" style="margin-top: 25px;">
                        <label for="backupName"><i class="fas fa-file-signature"></i> 备份文件名称</label>
                        <input type="text" id="backupName" value="html-system-modifications">
                    </div>
                    
                    <div class="action-buttons">
                        <button id="exportAllBtn" class="btn btn-export full-width"><i class="fas fa-download"></i> 备份全部数据</button>
                    </div>
                </div>
            </div>
            
            <div class="records-section">
                <div class="card">
                    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                        <h2 class="card-title"><i class="fas fa-history"></i> 修改历史记录</h2>
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="搜索修改记录...">
                            <button id="searchBtn" class="btn"><i class="fas fa-search"></i> 搜索</button>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <div id="recordsBody">
                            <!-- 记录将通过JavaScript动态添加 -->
                        </div>
                        <div id="emptyState" class="empty-state">
                            <i class="fas fa-clipboard-list"></i>
                            <h3>暂无修改记录</h3>
                            <p>添加您的第一条修改记录以开始跟踪</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <footer>
            <p>HTML系统修改记录工具 v2.1 | 响应式优化版 | 数据仅保存在当前浏览器中</p>
            <p>提示：定期导出记录以进行备份，重要数据请勿仅依赖此工具</p>
        </footer>
    </div>
    
    <!-- 记录详情模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title"><i class="fas fa-file-alt"></i> 修改记录详情</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="detail-item">
                    <div class="detail-label"><i class="fas fa-clock"></i> 修改时间</div>
                    <div class="detail-value" id="detailTimestamp"></div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label"><i class="fas fa-tag"></i> 修改类别</div>
                    <div class="detail-value">
                        <span id="detailCategory" class="tag"></span>
                    </div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label"><i class="fas fa-heading"></i> 修改内容描述</div>
                    <div class="detail-value" id="detailModification"></div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label"><i class="fas fa-file-code"></i> 修改后的内容</div>
                    <div class="code-content" id="detailContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="exportDetailBtn" class="btn btn-export"><i class="fas fa-download"></i> 导出此记录</button>
                <button id="closeDetailBtn" class="btn"><i class="fas fa-times"></i> 关闭</button>
            </div>
        </div>
    </div>

    <script>
        // 初始化记录数组
        let records = JSON.parse(localStorage.getItem('modificationRecords')) || [];
        
        // DOM元素
        const recordsBody = document.getElementById('recordsBody');
        const emptyState = document.getElementById('emptyState');
        const totalRecords = document.getElementById('totalRecords');
        const todayRecords = document.getElementById('todayRecords');
        const charsSaved = document.getElementById('charsSaved');
        const charCount = document.getElementById('charCount');
        const addBtn = document.getElementById('addBtn');
        const exportBtn = document.getElementById('exportBtn');
        const exportAllBtn = document.getElementById('exportAllBtn');
        const clearBtn = document.getElementById('clearBtn');
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        const detailModal = document.getElementById('detailModal');
        const closeDetailBtn = document.getElementById('closeDetailBtn');
        const closeModalBtn = document.querySelector('.close-modal');
        const exportDetailBtn = document.getElementById('exportDetailBtn');
        const modifiedContent = document.getElementById('modifiedContent');
        const backupName = document.getElementById('backupName');
        
        // 类别映射
        const categoryMap = {
            'design': { name: '设计', color: '#4361ee', bgColor: '#4361ee20' },
            'function': { name: '功能', color: '#f72585', bgColor: '#f7258520' },
            'content': { name: '内容', color: '#4cc9f0', bgColor: '#4cc9f020' },
            'bug': { name: 'Bug修复', color: '#7209b7', bgColor: '#7209b720' },
            'optimization': { name: '优化', color: '#4895ef', bgColor: '#4895ef20' },
            'structure': { name: '重构', color: '#ff9e00', bgColor: '#ff9e0020' }
        };
        
        // 页面加载时渲染记录
        document.addEventListener('DOMContentLoaded', () => {
            renderRecords(records);
            updateStats();
            
            // 添加记录事件
            addBtn.addEventListener('click', addRecord);
            
            // 导出所有记录
            exportBtn.addEventListener('click', () => exportAllRecords(false));
            exportAllBtn.addEventListener('click', () => exportAllRecords(true));
            
            // 清除记录
            clearBtn.addEventListener('click', clearAllRecords);
            
            // 搜索记录
            searchBtn.addEventListener('click', searchRecords);
            searchInput.addEventListener('keyup', (e) => {
                if (e.key === 'Enter') searchRecords();
            });
            
            // 关闭详情模态框
            closeDetailBtn.addEventListener('click', closeModal);
            closeModalBtn.addEventListener('click', closeModal);
            
            // 导出单个记录
            exportDetailBtn.addEventListener('click', exportSingleRecord);
            
            // 字符计数器
            modifiedContent.addEventListener('input', updateCharCount);
            updateCharCount();
            
            // 点击模态框外部关闭
            detailModal.addEventListener('click', (e) => {
                if (e.target === detailModal) closeModal();
            });
            
            // 添加示例数据
            if (records.length === 0) {
                addSampleData();
            }
        });
        
        // 添加示例数据
        function addSampleData() {
            const sampleRecords = [
                {
                    id: 1,
                    modification: "重构导航栏响应式设计",
                    modifiedContent: "/* 导航栏响应式设计改进 */\n.navbar {\n  display: flex;\n  justify-content: space-between;\n  padding: 1rem 2rem;\n}\n\n@media (max-width: 768px) {\n  .navbar {\n    flex-direction: column;\n  }\n}",
                    category: "design",
                    timestamp: "2023-10-15T09:30:00Z"
                },
                {
                    id: 2,
                    modification: "修复用户登录表单验证问题",
                    modifiedContent: "// 修复表单验证逻辑\nfunction validateForm() {\n  const email = document.getElementById('email').value;\n  const password = document.getElementById('password').value;\n  \n  if (!isValidEmail(email)) {\n    showError('请输入有效的邮箱地址');\n    return false;\n  }\n  \n  if (password.length < 8) {\n    showError('密码长度至少8个字符');\n    return false;\n  }\n  \n  return true;\n}",
                    category: "bug",
                    timestamp: "2023-10-16T14:22:00Z"
                },
                {
                    id: 3,
                    modification: "更新首页产品介绍内容",
                    modifiedContent: "<section class=\"products\">\n  <h2>我们的产品</h2>\n  <div class=\"product-grid\">\n    <div class=\"product-card\">\n      <img src=\"product1.jpg\" alt=\"智能手表\">\n      <h3>智能手表 Pro</h3>\n      <p>全新升级的智能手表，支持心率监测、GPS定位和7天续航</p>\n    </div>\n    <!-- 更多产品卡片 -->\n  </div>\n</section>",
                    category: "content",
                    timestamp: "2023-10-17T11:05:00Z"
                }
            ];
            
            records = [...sampleRecords, ...records];
            saveRecords();
            renderRecords(records);
            updateStats();
        }
        
        // 更新字符计数器
        function updateCharCount() {
            const content = modifiedContent.value;
            charCount.textContent = content.length;
            
            if (content.length > 10000) {
                charCount.style.color = 'var(--danger)';
                charCount.style.fontWeight = 'bold';
            } else {
                charCount.style.color = '';
                charCount.style.fontWeight = '';
            }
        }
        
        // 添加新记录
        function addRecord() {
            const modification = document.getElementById('modification').value.trim();
            const modifiedContent = document.getElementById('modifiedContent').value.trim();
            const category = document.getElementById('category').value;
            
            if (!modification) {
                showNotification('请填写修改内容描述！', 'danger');
                return;
            }
            
            if (!modifiedContent) {
                showNotification('请填写修改后的内容！', 'danger');
                return;
            }
            
            if (modifiedContent.length > 10000) {
                showNotification('修改内容过长！最多允许10,000个字符', 'danger');
                return;
            }
            
            const now = new Date();
            const record = {
                id: Date.now(),
                modification,
                modifiedContent,
                category,
                timestamp: now.toISOString()
            };
            
            records.unshift(record);
            saveRecords();
            renderRecords(records);
            updateStats();
            
            // 清空表单
            document.getElementById('modification').value = '';
            document.getElementById('modifiedContent').value = '';
            updateCharCount();
            
            // 显示成功消息
            showNotification('记录已成功添加！', 'success');
        }
        
        // 显示通知
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--success)' : 'var(--danger)'};
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 2000;
                font-weight: 600;
                transform: translateX(200%);
                transition: transform 0.4s ease;
                display: flex;
                align-items: center;
                gap: 10px;
            `;
            
            const icon = document.createElement('i');
            icon.className = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';
            notification.appendChild(icon);
            
            const text = document.createTextNode(message);
            notification.appendChild(text);
            
            document.body.appendChild(notification);
            
            // 显示通知
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);
            
            // 隐藏通知
            setTimeout(() => {
                notification.style.transform = 'translateX(200%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 400);
            }, 3000);
        }
        
        // 保存记录到localStorage
        function saveRecords() {
            localStorage.setItem('modificationRecords', JSON.stringify(records));
        }
        
        // 渲染记录列表
        function renderRecords(recordsToRender) {
            if (recordsToRender.length === 0) {
                recordsBody.innerHTML = '';
                emptyState.style.display = 'block';
                return;
            }
            
            emptyState.style.display = 'none';
            let html = '';
            
            recordsToRender.forEach(record => {
                const date = new Date(record.timestamp);
                const timeString = date.toLocaleString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                const dateString = date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                
                const categoryInfo = categoryMap[record.category] || categoryMap.design;
                
                html += `
                    <div class="record-item" onclick="showRecordDetail(${record.id})">
                        <div class="record-main">
                            <div class="record-date">${timeString}</div>
                            <div class="modification">${record.modification}</div>
                            <span class="record-category" style="background: ${categoryInfo.bgColor}; color: ${categoryInfo.color};">${categoryInfo.name}</span>
                        </div>
                        <div class="record-actions">
                            <i class="fas fa-eye action-icon" onclick="event.stopPropagation(); showRecordDetail(${record.id})"></i>
                            <i class="fas fa-trash-alt action-icon" onclick="event.stopPropagation(); deleteRecord(${record.id})" style="color: var(--danger);"></i>
                        </div>
                    </div>
                `;
            });
            
            recordsBody.innerHTML = html;
        }
        
        // 删除记录
        function deleteRecord(id) {
            if (confirm('确定要删除这条记录吗？此操作不可恢复！')) {
                records = records.filter(record => record.id !== id);
                saveRecords();
                renderRecords(records);
                updateStats();
                showNotification('记录已删除！', 'success');
            }
        }
        
        // 显示记录详情
        function showRecordDetail(id) {
            const record = records.find(r => r.id === id);
            if (!record) return;
            
            const date = new Date(record.timestamp);
            const dateString = date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            const categoryInfo = categoryMap[record.category] || categoryMap.design;
            
            document.getElementById('detailTimestamp').textContent = dateString;
            document.getElementById('detailCategory').textContent = categoryInfo.name;
            document.getElementById('detailCategory').style.backgroundColor = categoryInfo.bgColor;
            document.getElementById('detailCategory').style.color = categoryInfo.color;
            document.getElementById('detailModification').textContent = record.modification;
            document.getElementById('detailContent').textContent = record.modifiedContent;
            
            // 存储当前查看的记录ID
            detailModal.dataset.currentId = id;
            
            // 显示模态框
            detailModal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
        
        // 关闭模态框
        function closeModal() {
            detailModal.classList.remove('active');
            document.body.style.overflow = '';
        }
        
        // 更新统计信息
        function updateStats() {
            totalRecords.textContent = records.length;
            
            // 计算今天的记录数
            const today = new Date();
            const todayString = today.toISOString().split('T')[0];
            const todayCount = records.filter(record => {
                const recordDate = new Date(record.timestamp).toISOString().split('T')[0];
                return recordDate === todayString;
            }).length;
            
            todayRecords.textContent = todayCount;
            
            // 计算保存的字符数
            const totalChars = records.reduce((sum, record) => sum + record.modifiedContent.length, 0);
            charsSaved.textContent = totalChars.toLocaleString();
        }
        
        // 搜索记录
        function searchRecords() {
            const term = searchInput.value.trim().toLowerCase();
            
            if (!term) {
                renderRecords(records);
                return;
            }
            
            const filteredRecords = records.filter(record => 
                record.modification.toLowerCase().includes(term) || 
                record.modifiedContent.toLowerCase().includes(term) ||
                (categoryMap[record.category]?.name.toLowerCase().includes(term) || '')
            );
            
            renderRecords(filteredRecords);
        }
        
        // 导出所有记录
        function exportAllRecords(fullExport) {
            if (records.length === 0) {
                showNotification('没有记录可导出！', 'danger');
                return;
            }
            
            let content = fullExport 
                ? `HTML系统修改记录完整备份\n生成时间：${new Date().toLocaleString()}\n\n` 
                : `HTML系统修改记录汇总\n生成时间：${new Date().toLocaleString()}\n\n`;
            
            content += `共 ${records.length} 条记录\n`;
            content += `总字符数：${records.reduce((sum, r) => sum + r.modifiedContent.length, 0).toLocaleString()}\n\n`;
            content += '========================================\n\n';
            
            records.forEach((record, index) => {
                const date = new Date(record.timestamp);
                const dateString = date.toLocaleString('zh-CN');
                const categoryInfo = categoryMap[record.category] || categoryMap.design;
                
                content += `记录 #${index + 1}\n`;
                content += `时间: ${dateString}\n`;
                content += `类别: ${categoryInfo.name}\n`;
                content += `修改内容: ${record.modification}\n`;
                content += `修改后的内容:\n${record.modifiedContent}\n`;
                content += '----------------------------------------\n\n';
            });
            
            const fileName = fullExport 
                ? `${backupName.value || 'html-system-backup'}-${new Date().toISOString().slice(0, 10)}.txt`
                : 'html-system-modifications.txt';
            
            downloadFile(content, fileName);
            showNotification(`已导出${records.length}条记录！`, 'success');
        }
        
        // 导出单个记录
        function exportSingleRecord() {
            const id = parseInt(detailModal.dataset.currentId);
            const record = records.find(r => r.id === id);
            
            if (!record) return;
            
            const date = new Date(record.timestamp);
            const dateString = date.toLocaleString('zh-CN');
            const categoryInfo = categoryMap[record.category] || categoryMap.design;
            
            let content = `HTML系统修改记录详情\n生成时间：${new Date().toLocaleString()}\n\n`;
            content += `记录ID: ${record.id}\n`;
            content += `时间: ${dateString}\n`;
            content += `类别: ${categoryInfo.name}\n`;
            content += `修改内容: ${record.modification}\n`;
            content += `修改后的内容:\n${record.modifiedContent}\n`;
            
            downloadFile(content, `modification-${record.id}.txt`);
            showNotification('记录已导出！', 'success');
            closeModal();
        }
        
        // 清除所有记录
        function clearAllRecords() {
            if (records.length === 0) {
                showNotification('没有记录可清除！', 'danger');
                return;
            }
            
            if (confirm('确定要清除所有修改记录吗？此操作不可恢复！')) {
                records = [];
                saveRecords();
                renderRecords(records);
                updateStats();
                showNotification('所有记录已清除！', 'success');
            }
        }
        
        // 下载文件
        function downloadFile(content, fileName) {
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            
            // 清理
            setTimeout(() => {
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 100);
        }
        
        // 暴露函数给全局作用域
        window.showRecordDetail = showRecordDetail;
        window.deleteRecord = deleteRecord;
    </script>
</body>
</html>