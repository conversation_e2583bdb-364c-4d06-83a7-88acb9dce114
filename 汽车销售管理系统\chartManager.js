// 全局图表管理器
window.ChartManager = {
    instances: {},
    
    // 创建或更新图表
    createChart: function(canvasId, config) {
        // 销毁现有图表
        this.destroyChart(canvasId);
        
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.warn(`Canvas with ID '${canvasId}' not found`);
            return null;
        }
        
        const ctx = canvas.getContext('2d');
        this.instances[canvasId] = new Chart(ctx, config);
        return this.instances[canvasId];
    },
    
    // 销毁指定图表
    destroyChart: function(canvasId) {
        if (this.instances[canvasId]) {
            this.instances[canvasId].destroy();
            delete this.instances[canvasId];
        }
    },
    
    // 销毁所有图表
    destroyAll: function() {
        Object.keys(this.instances).forEach(canvasId => {
            this.destroyChart(canvasId);
        });
    }
};

// 页面卸载时清理所有图表
window.addEventListener('beforeunload', function() {
    window.ChartManager.destroyAll();
});