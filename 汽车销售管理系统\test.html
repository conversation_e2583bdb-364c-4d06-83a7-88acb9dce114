<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/dexie@3.2.2/dist/dexie.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f7fb;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #4361ee;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #4361ee;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3f37c9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>汽车销售管理系统 - 功能测试</h1>
        
        <div class="test-section">
            <h3>数据库连接测试</h3>
            <button onclick="testDatabaseConnection()">测试数据库连接</button>
            <div id="db-test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>展厅录入功能测试</h3>
            <button onclick="testShowroomEntry()">测试展厅录入</button>
            <div id="showroom-test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>线索录入功能测试</h3>
            <button onclick="testLeadEntry()">测试线索录入</button>
            <div id="lead-test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>订单管理功能测试</h3>
            <button onclick="testOrderManagement()">测试订单管理</button>
            <div id="order-test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>试驾车型功能测试</h3>
            <button onclick="testTestDriveModels()">测试试驾车型</button>
            <div id="testdrive-test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>用户管理功能测试</h3>
            <button onclick="testUserManagement()">测试用户管理</button>
            <div id="user-test-result"></div>
        </div>

        <div class="test-section">
            <h3>页面导航测试</h3>
            <button onclick="testNavigation()">测试页面导航</button>
            <div id="nav-test-result"></div>
        </div>
    </div>

    <script src="database.js"></script>
    <script>
        // 测试数据库连接
        async function testDatabaseConnection() {
            const resultDiv = document.getElementById('db-test-result');
            try {
                await window.dbFunctions.initDB();
                resultDiv.innerHTML = '<div class="test-result success">✓ 数据库连接成功</div>';
                
                // 测试各个表是否存在
                const tableFunctions = [
                    { name: 'customers', func: 'getAllCustomers' },
                    { name: 'showroomEntries', func: 'getAllShowroomEntries' },
                    { name: 'leadEntries', func: 'getAllLeadEntries' },
                    { name: 'orderManagement', func: 'getAllOrderManagement' },
                    { name: 'inventoryManagement', func: 'getAllInventoryManagement' },
                    { name: 'testDriveModels', func: 'getAllTestDriveModels' },
                    { name: 'userManagement', func: 'getAllUserManagement' }
                ];

                for (const table of tableFunctions) {
                    try {
                        await window.dbFunctions[table.func]();
                        resultDiv.innerHTML += `<div class="test-result success">✓ 表 ${table.name} 存在且可访问</div>`;
                    } catch (error) {
                        resultDiv.innerHTML += `<div class="test-result error">✗ 表 ${table.name} 访问失败: ${error.message}</div>`;
                    }
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">✗ 数据库连接失败: ${error.message}</div>`;
            }
        }
        
        // 测试展厅录入功能
        async function testShowroomEntry() {
            const resultDiv = document.getElementById('showroom-test-result');
            try {
                // 测试添加展厅录入
                const testEntry = {
                    entryDate: '2025-01-22',
                    entryPersonnel: '测试人员',
                    salesAdvisor: '测试顾问',
                    arrivalTime: '09:00',
                    departureTime: '10:30',
                    stayDuration: '1小时30分钟',
                    visitType: '首次到店',
                    channel: '网络',
                    customerName: '测试客户',
                    gender: '男',
                    phone: '13800138000',
                    intendedModels: '测试车型',
                    intention: '购买意向',
                    region: '测试区域',
                    currentModel: '无',
                    competitorModel: '无',
                    finance: '是',
                    tradeIn: '否',
                    testDrive: '是',
                    notes: '测试备注'
                };
                
                const id = await window.dbFunctions.addShowroomEntry(testEntry);
                resultDiv.innerHTML = '<div class="test-result success">✓ 展厅录入添加成功，ID: ' + id + '</div>';
                
                // 测试获取展厅录入
                const entries = await window.dbFunctions.getAllShowroomEntries();
                resultDiv.innerHTML += '<div class="test-result success">✓ 获取展厅录入成功，共 ' + entries.length + ' 条记录</div>';
                
                // 测试删除展厅录入
                await window.dbFunctions.deleteShowroomEntry(id);
                resultDiv.innerHTML += '<div class="test-result success">✓ 展厅录入删除成功</div>';
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">✗ 展厅录入测试失败: ${error.message}</div>`;
            }
        }
        
        // 测试线索录入功能
        async function testLeadEntry() {
            const resultDiv = document.getElementById('lead-test-result');
            try {
                const testLead = {
                    entryDate: '2025-01-22',
                    isValid: true,
                    customerName: '测试线索客户',
                    phone: '13900139000',
                    leadId: 'LEAD001',
                    smartNumber: 'SMART001',
                    intendedModels: '测试车型',
                    region: '测试区域',
                    wechat: '是',
                    channel: '网络',
                    visitDate: '2025-01-23',
                    dealDate: '',
                    salesFollow: '测试顾问',
                    receptionAdvisor: '测试接待',
                    firstFollowDate: '2025-01-22',
                    followStatus: '跟进中'
                };
                
                const id = await window.dbFunctions.addLeadEntry(testLead);
                resultDiv.innerHTML = '<div class="test-result success">✓ 线索录入添加成功，ID: ' + id + '</div>';
                
                const leads = await window.dbFunctions.getAllLeadEntries();
                resultDiv.innerHTML += '<div class="test-result success">✓ 获取线索录入成功，共 ' + leads.length + ' 条记录</div>';
                
                await window.dbFunctions.deleteLeadEntry(id);
                resultDiv.innerHTML += '<div class="test-result success">✓ 线索录入删除成功</div>';
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">✗ 线索录入测试失败: ${error.message}</div>`;
            }
        }
        
        // 测试订单管理功能
        async function testOrderManagement() {
            const resultDiv = document.getElementById('order-test-result');
            try {
                const testOrder = {
                    orderStatus: '正常',
                    orderDate: '2025-01-22',
                    customerName: '测试订单客户',
                    phone1: '13700137000',
                    phone2: '',
                    auditStatus: '已审核',
                    salesAdvisor: '测试顾问',
                    vin: 'TEST123456789',
                    carModel: '测试车型',
                    configuration: '标准配置',
                    exteriorColor: '白色',
                    interiorColor: '黑色',
                    options: '无',
                    deliveryStatus: '待交付',
                    resourceStatus: '正常',
                    deliveryDate: '2025-02-01'
                };
                
                const id = await window.dbFunctions.addOrderManagement(testOrder);
                resultDiv.innerHTML = '<div class="test-result success">✓ 订单添加成功，ID: ' + id + '</div>';
                
                const orders = await window.dbFunctions.getAllOrderManagement();
                resultDiv.innerHTML += '<div class="test-result success">✓ 获取订单成功，共 ' + orders.length + ' 条记录</div>';
                
                await window.dbFunctions.deleteOrderManagement(id);
                resultDiv.innerHTML += '<div class="test-result success">✓ 订单删除成功</div>';
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">✗ 订单管理测试失败: ${error.message}</div>`;
            }
        }
        
        // 测试试驾车型功能
        async function testTestDriveModels() {
            const resultDiv = document.getElementById('testdrive-test-result');
            try {
                const testModel = {
                    name: '测试车型',
                    plateNumber: '京A12345',
                    configuration: '豪华版',
                    vin: 'TEST987654321',
                    exteriorColor: '红色',
                    interiorColor: '米色',
                    factoryOptions: '天窗',
                    guidePrice: '300000',
                    status: '服役',
                    type: '试驾车',
                    invoiceDate: '2025-01-01',
                    reportDate: '2025-01-02',
                    registrationDate: '2025-01-03',
                    expiryDate: '2026-01-03',
                    notes: '测试备注'
                };
                
                const id = await window.dbFunctions.addTestDriveModel(testModel);
                resultDiv.innerHTML = '<div class="test-result success">✓ 试驾车型添加成功，ID: ' + id + '</div>';
                
                const models = await window.dbFunctions.getAllTestDriveModels();
                resultDiv.innerHTML += '<div class="test-result success">✓ 获取试驾车型成功，共 ' + models.length + ' 条记录</div>';
                
                await window.dbFunctions.deleteTestDriveModel(id);
                resultDiv.innerHTML += '<div class="test-result success">✓ 试驾车型删除成功</div>';
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">✗ 试驾车型测试失败: ${error.message}</div>`;
            }
        }
        
        // 测试用户管理功能
        async function testUserManagement() {
            const resultDiv = document.getElementById('user-test-result');
            try {
                const testUser = {
                    serialNumber: '001',
                    account: 'testuser',
                    password: 'test123456',
                    userName: '测试用户',
                    position: '测试职位',
                    permissions: 'index.html,customers.html',
                    status: '启用'
                };

                const id = await window.dbFunctions.addUserManagement(testUser);
                resultDiv.innerHTML = '<div class="test-result success">✓ 用户添加成功，ID: ' + id + '</div>';

                const users = await window.dbFunctions.getAllUserManagement();
                resultDiv.innerHTML += '<div class="test-result success">✓ 获取用户成功，共 ' + users.length + ' 条记录</div>';

                await window.dbFunctions.deleteUserManagement(id);
                resultDiv.innerHTML += '<div class="test-result success">✓ 用户删除成功</div>';

            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">✗ 用户管理测试失败: ${error.message}</div>`;
            }
        }

        // 测试页面导航
        function testNavigation() {
            const resultDiv = document.getElementById('nav-test-result');
            const pages = [
                { name: '首页', url: 'index.html' },
                { name: '销售分析', url: 'salesanalytics.html' },
                { name: '客户管理', url: 'customers.html' },
                { name: '订单管理', url: 'order.html' },
                { name: '系统设置', url: 'settings.html' },
                { name: '用户管理', url: 'user.html' }
            ];

            resultDiv.innerHTML = '<div class="test-result info">页面导航链接测试:</div>';

            pages.forEach(page => {
                const link = `<a href="${page.url}" target="_blank" style="color: #4361ee; text-decoration: none; margin-right: 15px;">${page.name}</a>`;
                resultDiv.innerHTML += `<div class="test-result success">✓ ${link}</div>`;
            });
        }
    </script>
</body>
</html>
